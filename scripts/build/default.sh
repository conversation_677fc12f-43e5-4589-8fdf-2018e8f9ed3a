#!/bin/bash
set -e
cd "$(dirname "${BASH_SOURCE[0]}")/../.."

rm -rf release

export AWS_REGION="us-east-1"
#export domain=neuralgalaxy.com
#export show_version=$(cat package.json | jq -r '.show_version')
#export app_name=$(cat package.json | jq -r '.name')
#
#export APP_SHOW_DEVTOOLS=show
#if [[ ${stack_name} == app ]] || [[ ${stack_name} == staging ]] || [[ ${stack_name} == staging2 ]] || [[ ${stack_name} == pbfs ]]; then
#  export APP_SHOW_DEVTOOLS=notshow
#fi
#
#rm -rf ./dist/version.json
#cat <<EOF | tee ./dist/version.json
#{
#  "APP_VERSION": "${app_version}",
#  "APP_SHOW_VERSION":"${show_version}",
#  "AUTO_UPDATE_ENDPOINT": "s3.cn-northwest-1.amazonaws.com.cn",
#   "S3_HOST": "ng-software.s3.cn-northwest-1.amazonaws.com.cn",
#   "APP_UPDATE_SEED_URL":"https://ng-software.s3.cn-northwest-1.amazonaws.com.cn/${stack_name}/${app_name}",
#   "APP_AWS_SOFTWARE_S3_BUCKET_HREF": "https://ng-software.s3.cn-northwest-1.amazonaws.com.cn",
#   "APP_STACK_NAME":  "${stack_name}",
#   "APP_NG_API_BASEURL":  "https://${stack_name}-api.${domain}/pnt/api",
#   "APP_NG_WEB_BASEURL":  "https://${stack_name}.${domain}",
#   "APP_REQUEST_HEADERS_ORIGIN":  "http://localhost:4000",
#   "APP_SHOW_DEVTOOLS": "${APP_SHOW_DEVTOOLS}"
#}
#EOF

if [ "$(uname -s)" == "Darwin" ]; then
  # npm run native mac
  ./node_modules/.bin/electron-builder -m -p never
else
  sudo rm -rf node_modules_win
  cp -r node_modules node_modules_win

  docker-compose -f ./scripts/build/docker-compose.yml down

  docker-compose -f ./scripts/build/docker-compose.yml up ${build_linux} ${build_win}

  exit_code=$(docker inspect ${build_linux} ${build_win}  | jq '.[].State.ExitCode' | tr -d '0\n')
  if [ "${exit_code}" != "" ]; then
    exit 1
  fi
fi

sudo chown -R "$(id -u):$(id -g)" release
ls -l release/*/*

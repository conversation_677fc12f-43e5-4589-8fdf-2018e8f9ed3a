#!/usr/bin/env bash
set -e

SCRIPT_DIR="$(
  cd "$(dirname "${BASH_SOURCE[0]}")/../.."
  pwd -P
)"

export stack_name="$1"
export specify_profile="$2"
export app_version=$(cat package.json | jq -r '.version')

if [ "$stack_name" == "" ]; then
  echo "stack_name not found"
  exit 1
fi

echo "stack: $stack_name, profile: $specify_profile"

if [ "$SKIP_RUN_BUILD" != "true" ]; then

./scripts/login.sh us

export APP_SHOW_DEVTOOLS=notshow
export BUILD_STAMP=$(date +%Y%m%d%H%M%S)
export BUILD_NUMBER=$(git rev-parse --short HEAD)

cat <<EOF > ./scripts/env_file
stack_name=${stack_name}
VER=${app_version}
BUILD_STAMP=${BUILD_STAMP}
BUILD_NUMBER=${BUILD_NUMBER}
APP_SHOW_DEVTOOLS=${APP_SHOW_DEVTOOLS}
EOF

# 生成 App 生产环境变量
./scripts/build/generate-app-env.sh

npm install
npm run dist
fi

./scripts/build/${specify_profile}.sh

# Building for MacOS

Follow all the steps below for:
* Prepare for Signing
* Prepare for Notarizing
* MacOS Building and Publishing

## Prepare environment for Signing the app

* on the build machine, import Apple intermediary certs
  * download https://developer.apple.com/certificationauthority/AppleWWDRCA.cer
  * download https://www.apple.com/certificateauthority/AppleWWDRCAG3.cer
  * both import into Keychain "System"
* on the build machine, create request for certificate
  * Keychain Access -> Certificate Assistant -> Req Cert from CA
  * email = `<EMAIL>`
  * Common Name = `Neural Galaxy LLC`
  * CA email = `<blank>`
  * Save to Disk
* in Developer Account -> Certificates, Identifiers & Profiles -> Certificates
  * create 2 new Certs (Developer ID Installer, Developer ID Application)
  * use the `*.certSigningRequest` file from above
  * download the 2 `*.cer` files to the build machine
* on build machine, import the 2 certificates into "login" keychain
  * check that they have the cert request as subitem
* check the code signing identity is available:
  * `security find-identity -v -p codesigning`

## Prepare environment for Notarizing the app

* from `s3://ng-vault/macos/` (CN PROD account):
  * download the key file `AuthKey_A9679MQR6P.p8` into `~/.private_keys/`
  * get env vars (`API_KEY_ID`, `API_KEY_ISSUER_ID`) into `~/.bashrc`
* download Xcode 12
  * NOT just command line tools
  * https://developer.apple.com/download/more/?=xcode
  * `sudo xcode-select -s <location of Xcode.app>`
  * check using `xcrun altool --help`
  * may need also `sudo xcodebuild -license` (and type "agree" toward the end)
  * launch Xcode to accept agreement and allow it to install components
  * something to try: install only "Transporter" (it should have `altool`, too)
* install homebrew and tools
  * install homebrew
  * if Xcode command line tools are not installed, homebrew will install it
  * install node@10
  * install git
  * install aws cli
  * install jq
  * install libpng
* setup .aws profile
  * use aws user jenkins from both AWS CN and AWS US
  * two profiles one for CN and one for US
 
# Old stuff

## Prepare environment for Notarizing the app (generate the app key)

* https://samuelmeuli.com/blog/2019-12-28-notarizing-your-electron-app/
  * https://appstoreconnect.apple.com/access/api
  * download the key file `*.p8` into `~/.private_keys/`
  * store env vars (`API_KEY_ID`, `API_KEY_ISSUER_ID`) in `~/.bash_profile`

## Various commands
* `security unlock-keychain login.keychain`
* `sudo xcode-select -r`
* `sudo xcode-select -s ~/Projects/NG/MacOSdev/Xcode.app/`
* `sudo xcodebuild -license`
* `xcrun altool --help`
* `security find-identity -v -p codesigning`

#!/bin/bash
set -e
echo "当前路径为： $(pwd)"
cd "$(dirname "${BASH_SOURCE[0]}")/../.."

export show_version=$(cat package.json | jq -r '.show_version')
export app_name=$(cat package.json | jq -r '.name')
export domain=''
export app_software_domain=''
export app_aws_software_s3_bucket_href=''

if [ "$specify_profile" == "aliyun" ] || [ "$specify_profile" == "aliyun-prod" ]; then
   export domain="neuralgalaxy.cn"
   export app_software_domain='https://ng-software.oss-cn-hangzhou.aliyuncs.com'
   export app_aws_software_s3_bucket_href=''
elif [  "$specify_profile" == "aws-cn" ]; then
   export domain='neuralgalaxy.cn'
   export app_software_domain='https://ng-software.s3.cn-northwest-1.amazonaws.com.cn'
   export app_aws_software_s3_bucket_href=''
else
   # 默认情况为 aws-us
   export domain='neuralgalaxy.com'
   export app_software_domain='https://ng-software.s3.amazonaws.com'
   export app_aws_software_s3_bucket_href='https://ng-software.s3.us-east-1.amazonaws.com'
fi

export app_update_seed_url=${app_software_domain}/${stack_name}/${app_name}
export app_ng_web_base_url=https://${stack_name}-api.${domain}
export app_ng_api_url=${app_ng_web_base_url}/pnt/api

export app_show_devtools=show
if [[ ${stack_name} == app ]] || [[ ${stack_name} == staging ]] || [[ ${stack_name} == staging2 ]] || [[ ${stack_name} == pbfs ]]; then
  export app_show_devtools=notshow
fi


rm -rf ./env/.env.production
cat <<EOF | tee ./env/.env.production
  PUBLIC_APP_NAME=${app_name}

  PUBLIC_APP_VERSION=${app_version}

  PUBLIC_APP_SHOW_VERSION=${show_version}

  PUBLIC_APP_STACK_NAME=${stack_name}

  PUBLIC_APP_UPDATE_SEED_URL=${app_update_seed_url}

  PUBLIC_APP_AWS_SOFTWARE_S3_BUCKET_HREF=${app_aws_software_s3_bucket_href}

  PUBLIC_APP_NG_WEB_BASEURL=${app_ng_web_base_url}

  PUBLIC_APP_NG_API_BASEURL=${app_ng_api_url}

  PUBLIC_APP_SHOW_DEVTOOLS=${app_show_devtools}

EOF


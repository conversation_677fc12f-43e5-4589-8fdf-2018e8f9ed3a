version: "3.7"

services:
  build_linux:
    image: "electronuserland/builder:14-08.22"
    container_name: build_linux
    working_dir: /build
    env_file:
      - ../env_file
    volumes:
      - ../../:/build
    command:
      - /build/scripts/build/_execute_build_publish_linux.sh

  build_win:
    image: "electronuserland/builder:14-wine-05.22"
    container_name: build_win
    working_dir: /build
    env_file:
      - ../env_file
    volumes:
      - ../../:/build
      - ../../node_modules_win:/build/node_modules
      - ../../release/win:/build/release/win
    command:
      - /build/scripts/build/_execute_build_publish_win.sh

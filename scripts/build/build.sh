#!/bin/bash
set -e

SCRIPT_DIR="$(
  cd "$(dirname "${BASH_SOURCE[0]}")/../.."
  pwd -P
)"

export stack_name="$1"
export specify_profile="$2"
export app_version=$(cat package.json | jq -r '.version')

export build_linux=$(if [ "${3}" == "true" ]; then echo 'build_linux'; fi)
export build_win=$(if [ "${4}" == "true" ]; then echo 'build_win'; fi)

if [ "$stack_name" == "" ]; then
  echo "stack_name not found"
  exit 1
fi

echo "stack: $stack_name, profile: $specify_profile"

sudo apt-get update -y
sudo apt-get install -y pkg-config libsecret-tools

if [ "$SKIP_RUN_BUILD" != "true" ]; then

./scripts/login.sh

export CSC_KEY_PASSWORD=$(aws ssm get-parameters --names "/common/windows/csc/password" --with-decryption | jq -r ".Parameters[0].Value")
export CSC_LINK=$(aws ssm get-parameters --names "/common/windows/csc/cert" --with-decryption | jq -r ".Parameters[0].Value")

export VER=$app_version
export BUILD_STAMP=$(date +%Y%m%d%H%M%S)
export BUILD_NUMBER=$(git rev-parse --short HEAD)


cat <<EOF > ./scripts/env_file
stack_name=${stack_name}

VER=${app_version}
BUILD_STAMP=$(date +%Y%m%d%H%M%S)
BUILD_NUMBER=$(git rev-parse --short HEAD)
CSC_KEY_PASSWORD=${CSC_KEY_PASSWORD}
CSC_LINK=${CSC_LINK}
EOF

# 生成 App 生产环境变量
./scripts/build/generate-app-env.sh

npm install
npm run dist
fi

./scripts/build/${specify_profile}.sh

#!/bin/bash
set -ex
cd "$(dirname "${BASH_SOURCE[0]}")/../.."

export stack_name="$1"
if [ "$stack_name" == "" ]; then
    echo "STACK_NAME not found"
    exit 1
fi
export app_version=$(cat package.json | jq -r '.version')

export show_version=$(cat package.json | jq -r '.show_version')

export app_name=$(cat package.json | jq -r '.name')

bucket_dest_dir="s3://ng-software/${stack_name}/${app_name}"
echo "upload build to: ${bucket_dest_dir}"

rm -rf release/*/*-unpacked

for name in release/*/*.yml ; do
  mv "$name" "$(dirname "$name")/${app_version}.yml"
done

profile=""
if [ "$(uname -s)" == "Darwin" ]; then
    profile=" --profile us"
fi

pushd release
  for name in */* ; do
    if [ -f $name ]; then
      aws $profile s3 cp --no-progress --acl=public-read "$name" "$bucket_dest_dir/$name"
    fi
  done
popd

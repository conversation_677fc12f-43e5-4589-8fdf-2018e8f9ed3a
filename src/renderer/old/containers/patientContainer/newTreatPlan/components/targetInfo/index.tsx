import { Input, Select } from 'antd';
import styles from './index.module.less';
import { useAsyncEffect } from 'ahooks';
import { DiseaseTemplateType, DiseaseTypeEnum } from '@renderer/types/disease-template';
import { useRecoilState, useRecoilValue } from 'recoil';
import { plan } from '@renderer/old/recoil/planFiles';
import {
  Configuration,
  ReportTargetDescriptionApi,
} from '@cloud/point-server-frontend-sdk';
import { axiosHttpClient } from '@renderer/old/api/axiosHttpClientImplRenderer';
import React, { FC, useState, useCallback, useMemo } from 'react';
import { allTargetAtom } from '@renderer/old/recoil/treatment';
import { PlanDisplayStatus } from '@renderer/types/treatment-plan';
import StimulusTemplateWrap from '../stimulusTemplateWrap';
import { TargetTypes } from '../../../treatmentPlan/targetDots';
import { getDefaultStimulusTemplate, StimulateTypeEnum } from '../../../treatmentPlan/component/stimulusTemplate/constant';
import StimulusTemplateShow from '../stimulusTemplateShow';

type Props = {
  selectTargetKey?: string
}

// 使用 React.memo 优化组件，避免不必要的重渲染
export const TargetInfo: FC<Props> = React.memo(({
  selectTargetKey,
}) => {
  const config = new Configuration({
    basePath: axiosHttpClient.baseURL.slice(0, axiosHttpClient.baseURL.length - 4),
  });
    const reportTargetDesApi = new ReportTargetDescriptionApi(
    config,
    undefined,
    axiosHttpClient.client,
  );
  const { diseaseType, planDisplayStatus } = useRecoilValue(plan) || {};
  const pendingReview = PlanDisplayStatus.Pending === planDisplayStatus;
  const [allTarget, setAllTarget ] = useRecoilState(allTargetAtom);
  const [loopResult, setLoopResult] = useState<any[]>([]);

  // 使用 useMemo 优化 currentTarget 计算，避免不必要的重新计算
  const currentTarget = useMemo(() => {
    return allTarget?.find(target => `${target.hemi}${target.vertexIndex}` === selectTargetKey);
  }, [allTarget, selectTargetKey]);

  // 使用 useMemo 优化 stimulusTemplate 计算
  const stimulusTemplate = useMemo(() => {
    if (!currentTarget) return undefined;
    const stimulateTemplate = currentTarget.stimulateTemplate;
    return stimulateTemplate ? { ...stimulateTemplate, targetName: currentTarget.type } : undefined;
  }, [currentTarget]);

    // 获取病症环路描述列表
  useAsyncEffect(async () => {
    if (!diseaseType ||  diseaseType === DiseaseTypeEnum.MotorDeficits) return;
    const { data } = await reportTargetDesApi.getPlanPdfReportTargetDescriptions(
      DiseaseTemplateType[diseaseType],
    );
    setLoopResult(data?.records);
  }, [diseaseType]);

  // 优化状态更新函数，减少不必要的深拷贝
  const updateTargetProperty = useCallback((property: keyof TargetTypes, value: any) => {
    setAllTarget(prevTargets => {
      const targetIndex = prevTargets.findIndex(
        target => `${target.hemi}${target.vertexIndex}` === selectTargetKey
      );

      if (targetIndex === -1) return prevTargets;

      // 只拷贝需要修改的目标对象
      const newTargets = [...prevTargets];
      newTargets[targetIndex] = {
        ...prevTargets[targetIndex],
        [property]: value
      };

      return newTargets;
    });
  }, [selectTargetKey, setAllTarget]);

  const handleLoopChange = useCallback((id: number) => {
    updateTargetProperty('reportParc18ParcId', id);
  }, [updateTargetProperty]);

  // 直接更新，不使用防抖，保持输入的实时性
  const handleEditTargetName = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    updateTargetProperty('name', e.target.value);
  }, [updateTargetProperty]);

  const getTargetName = () => {
    if(!currentTarget) return '';
    if(typeof(currentTarget.name) === 'string') return currentTarget.name;

    return `${currentTarget.hemi}${currentTarget.vertexIndex}`;
  };

  // 直接更新刺激参数，保持实时性
  const handleChangeValues = useCallback((changeValues: any) => {
    updateTargetProperty('stimulateTemplate', changeValues);
  }, [updateTargetProperty]);

  const changeStimulusTemplate = useCallback((stimulateType: StimulateTypeEnum) => {
    const defaultStimulusTemplate = getDefaultStimulusTemplate(stimulateType, diseaseType);
    updateTargetProperty('stimulateTemplate', defaultStimulusTemplate);
  }, [diseaseType, updateTargetProperty]);

  return(
    <div className={styles['target-info']}>
      <div className='target-name'>
        <div className='title'>靶点名称：</div>
        {pendingReview && (<Input  onChange={handleEditTargetName} value={getTargetName()}/>)}
        {!pendingReview && <div className='render-name'>{getTargetName()}</div>}
      </div>
      <div className='loop-desc'>
        <div className='title'>环路描述：</div>
        {pendingReview && (
          <Select
            className={styles.previewSelect}
            getPopupContainer={(triggerNode) => triggerNode.parentNode}
            value={currentTarget?.reportParc18ParcId}
            onChange={handleLoopChange}
          >
            {loopResult?.map((item) => (
              <Select.Option
                key={item?.id}
                value={item?.parcId}
              >
                {item?.parcName}
              </Select.Option>
            ))}
            <Select.Option value={null}>无需描述</Select.Option>
          </Select>
        )}
        {!pendingReview && <div className='loop-show'>{loopResult.find(item => item.parcId === currentTarget?.reportParc18ParcId)?.parcName}</div>}
        <div className='desc-val'>
          {currentTarget?.reportParc18ParcId ? loopResult.find((item) => item.parcId === currentTarget.reportParc18ParcId)?.parcDescription : '无'}
        </div>
      </div>
      {!currentTarget?.type?.includes('MEP') && (
        <div className='stimulusTemplate-box'>
          <div className='title'>刺激参数</div>
          {!pendingReview && stimulusTemplate && <StimulusTemplateShow diseaseType={diseaseType} stimulusTemplate={stimulusTemplate}/>}
          {pendingReview && stimulusTemplate && (
            <StimulusTemplateWrap 
              stimulusTemplate={stimulusTemplate}
              diseaseType={diseaseType}
              apiStatus={{ loading: false }}
              handleChangeValues={handleChangeValues}
              changeStimulusTemplate={changeStimulusTemplate}
            />
          )}
        </div>
      )}
    </div>
  )
});

TargetInfo.displayName = 'TargetInfo';
import { Component, createRef } from "react";
import { StimulusTemplateFormModalStateType } from "../../../treatmentPlan/component/stimulusTemplate/stimulusTemplateFormModel";
import { getDefaultStimulusTemplate, StimulateTypeEnum } from "../../../treatmentPlan/component/stimulusTemplate/constant";
import { StimulusTemplateForm } from "../../../treatmentPlan/component/stimulusTemplate/stimulusTemplateForm";
import { StimulateTemplateModel } from '@cloud/point-server-frontend-sdk';
import { IntlPropType } from "@renderer/types/types";
import { ApiStatus } from "@common/types/apiStatus";
import { injectIntl } from "react-intl";
import { isEqual } from "lodash";

type StimulusTemplateFormModalPropsType = {
  stimulusTemplate: (StimulateTemplateModel & { targetName?: string }) | undefined;
  diseaseType: number;
  apiStatus: ApiStatus;
  handleChangeValues: (stimulusTemplate: StimulateTemplateModel) => void;
  changeStimulusTemplate(stimulateType: StimulateTypeEnum): void;
} & IntlPropType;

class StimulusTemplateWrap extends Component<
  StimulusTemplateFormModalPropsType,
  StimulusTemplateFormModalStateType
>{
  private formRef: any | null = createRef(); // tslint:disable-line: no-null-keyword
  constructor(props: StimulusTemplateFormModalPropsType) {
    super(props);
    let stimulateType = StimulateTypeEnum.iTBS;
    if (props.diseaseType === 3 && props.stimulusTemplate?.targetName) {
      if (props.stimulusTemplate?.targetName.includes('RH_'))
        stimulateType = StimulateTypeEnum.cTBS;
    }
    const defaultStimulusTemplate = getDefaultStimulusTemplate(stimulateType, props.diseaseType);

    this.state = {
      stimulateType: stimulateType,
      stimulusTemplate: props.stimulusTemplate ? props.stimulusTemplate : defaultStimulusTemplate,
    };
  }

  componentDidUpdate(prevProps: Readonly<StimulusTemplateFormModalPropsType>, prevState: Readonly<StimulusTemplateFormModalStateType>, snapshot?: any): void {
    // 只在刺激模板真正改变时才更新表单，避免不必要的重渲染
    if(!isEqual(prevProps.stimulusTemplate, this.props.stimulusTemplate) && this.formRef?.formRef?.current){
      // 使用 requestAnimationFrame 延迟更新，避免阻塞 UI
      requestAnimationFrame(() => {
        this.formRef.formRef.current?.setFieldsValue({
          ...this.props.stimulusTemplate,
        });
      });
    }
  }

  render(){
    const { intl, apiStatus, diseaseType, handleChangeValues, changeStimulusTemplate, stimulusTemplate } = this.props;

    return(
      <StimulusTemplateForm
        ref={(node: any) => {
          this.formRef = node;
        }}
        httpErrorCode={apiStatus.error?.code}
        handleChangeValues={handleChangeValues}
        stimulateType={stimulusTemplate?.stimulateType}
        changeStimulusTemplate={changeStimulusTemplate}
        intl={intl}
        stimulusTemplate={stimulusTemplate}
        diseaseType={diseaseType}
      />
    )
  }
};

export default injectIntl(StimulusTemplateWrap);
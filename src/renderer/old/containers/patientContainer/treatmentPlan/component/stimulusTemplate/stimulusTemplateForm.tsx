import styles from './stimulusTemplateForm.module.less';

import { Form, Input, InputNumber, Select } from 'antd';
import { RuleObject } from 'antd/lib/form';
import { Store } from 'antd/lib/form/interface';
import classNames from 'classnames';
import lodash from 'lodash';
import * as React from 'react';
import { FormattedMessage } from 'react-intl';

import { StimulateTemplateModel } from '@cloud/point-server-frontend-sdk';

import { IntlPropType } from '@common/old/ptc-component/lib/propTypes';
import {
  calculatePulseTotal,
  calculateTreatmentTime,
} from '@renderer/old/containers/patientContainer/treatmentPlan/component/stimulusTemplate/calculation-formula';
import {
  getDefaultStimulusTemplate,
  StimulateTypeEnum,
} from '@renderer/old/containers/patientContainer/treatmentPlan/component/stimulusTemplate/constant';
import { DiseaseTypeEnum } from '@renderer/types/disease-template';

import {
  rTMSFields,
  StimulusParamFormField,
  StimulusParamFormFieldEnum,
  tbsFields,
} from './calExciteParam';

const { Item } = Form;
const { Option } = Select;

const StimulateSelectOptions = [
  {
    value: StimulateTypeEnum.rTMS,
    text: <FormattedMessage id="point.cureProject.params.type.rTMS" />,
  },
  {
    value: StimulateTypeEnum.iTBS,
    text: <FormattedMessage id="point.cureProject.params.type.iTBS" />,
  },
  {
    value: StimulateTypeEnum.cTBS,
    text: <FormattedMessage id="point.cureProject.params.type.cTBS" />,
  },
];

export type StimulusTemplateFormPropsType = {
  stimulusTemplate: StimulateTemplateModel;
  stimulateType: StimulateTypeEnum;
  httpErrorCode: string | undefined;
  handleChangeValues(stimulusTemplate: StimulateTemplateModel): void;
  changeStimulusTemplate(stimulateType: StimulateTypeEnum): void;
  diseaseType?: number;
} & IntlPropType;

type editStimulateParamStateType = {
  renderFields: StimulusParamFormField[];
};

export class StimulusTemplateForm extends React.Component<
  StimulusTemplateFormPropsType,
  editStimulateParamStateType
> {
  private formRef: any | null = React.createRef();

  constructor(props: StimulusTemplateFormPropsType) {
    super(props);

    const { stimulusTemplate } = props;
    const stimulateType = stimulusTemplate?.stimulateType;

    let fields;
    if ([StimulateTypeEnum.iTBS, StimulateTypeEnum.cTBS].includes(stimulateType)) {
      fields = tbsFields;
    } else if ([StimulateTypeEnum.rTMS].includes(stimulateType)) {
      fields = rTMSFields;
    }

    this.state = {
      renderFields: fields,
    };

    this.formRef = React.createRef();
  }

  // public shouldComponentUpdate(
  //   nextProps: Readonly<StimulusTemplateFormPropsType>,
  //   nextState: Readonly<editStimulateParamStateType>,
  //   nextContext: any,
  // ): boolean {
  //   if (this.props.stimulusTemplate !== nextProps.stimulusTemplate) {
  //     return true;
  //   }
  //
  //   if (this.props.httpErrorCode !== nextProps.httpErrorCode) {
  //     return true;
  //   }
  //
  //   return false;
  // }

  public render(): React.ReactNode {
    const formItemLayout = {
      labelCol: { span: 10 },
      wrapperCol: { span: 14, offset: 1 },
    };

    const { stimulusTemplate } = this.props;

    return (
      <Form
        className="stimulus-template-form"
        size="large"
        style={{ height: 'calc(100% - 30px)' }}
        initialValues={this.getInitialValue(stimulusTemplate)}
        ref={this.formRef}
        onValuesChange={this.handleChangeValues}
        labelAlign={'left'}
        {...formItemLayout}
      >
        {this.renderFormHeader()}
        {this.renderTemplateTypeItem()}
        {this.renderFormItem()}
      </Form>
    );
  }

  private renderFormHeader = () => {
    const { intl, stimulusTemplate } = this.props;

    let renderPulseTotalContent = stimulusTemplate?.pulseTotal ?? '--';
    let renderTreatmentTimeContent = stimulusTemplate?.treatmentTime ?? '--';

    const { isValid: isPulseTotalValid } = this.validatePulseTotal(stimulusTemplate);
    const { isValid: isTreatmentTimeValid } = this.validateTreatmentTime(stimulusTemplate);
    const errorMessage = intl.formatMessage({ id: 'point.cureProject.params.rules.limit' });
    const renderMessageContent = <span className={styles.errorMsg}>{errorMessage}</span>;

    if (!isPulseTotalValid) {
      renderPulseTotalContent = renderMessageContent;
    }
    if (!isTreatmentTimeValid) {
      renderTreatmentTimeContent = renderMessageContent;
    }

    return (
      <div className={styles.formHeader}>
        <div className={classNames(styles.pulseTotal)}>
          <span className={styles.label}>
            {this.renderLabel(StimulusParamFormFieldEnum.PulseTotal)}
          </span>
          {renderPulseTotalContent}
        </div>

        <div className={classNames(styles.treatmentTime)}>
          <span className={styles.label}>
            {this.renderLabel(StimulusParamFormFieldEnum.TreatmentTime)}
          </span>
          {renderTreatmentTimeContent}
        </div>
      </div>
    );
  };

  /**
   * 渲染表单元素 label 内容
   */
  private renderLabel = (key: string) => {
    const { stimulusTemplate } = this.props;
    const stimulateType = stimulusTemplate?.stimulateType;

    let label = '';

    if (stimulateType === StimulateTypeEnum.iTBS) {
      label = this.props.intl.formatMessage({ id: `point.cureProject.params.iTBS.${key}` });
    } else if (stimulateType === StimulateTypeEnum.cTBS) {
      label = this.props.intl.formatMessage({ id: `point.cureProject.params.cTBS.${key}` });
    } else if (stimulateType === StimulateTypeEnum.rTMS) {
      label = this.props.intl.formatMessage({ id: `point.cureProject.params.rTMS.${key}` });
    }

    return `${label} :`;
  };

  /**
   * 渲染表单元素
   */
  private renderFormItem = () => {
    const { renderFields } = this.state;
    const { stimulusTemplate } = this.props;

    return renderFields.map((field) => {
      const { key: fieldKey, rules: fieldRules, dependencies: fieldDependencies } = field;
      const { step, precision } = fieldRules;

      // 如果脉冲串数为 1 时，不展示 刺激间隔参数
      if (
        stimulusTemplate.strandPulseCount === 1 &&
        fieldKey === StimulusParamFormFieldEnum.IntermissionTime
      ) {
        return <></>;
      }

      // let disabled = false;
      // 刺激时长 和 总脉冲数 不展示 输入框
      if (
        [StimulusParamFormFieldEnum.TreatmentTime, StimulusParamFormFieldEnum.PulseTotal].includes(
          fieldKey,
        )
      ) {
        // disabled = true;
        return <></>;
      }

      return (
        <Item
          className={styles.mgb4}
          name={fieldKey}
          key={fieldKey}
          label={this.renderLabel(fieldKey)}
          dependencies={fieldDependencies}
          validateFirst
          rules={this.getInputRules(field)}
        >
          <InputNumber
            className={styles.max_width}
            // disabled={disabled}
            step={step}
            placeholder={this.getInputValueLimitMsg(field)}
            precision={precision}
          />
        </Item>
      );
    });
  };

  /**
   * 获取输入框校验规则
   */
  private getInputRules = (fieldInfo) => {
    // 所有输入框通用的规则
    const baseRules = [
      {
        required: true,
        message: this.getInputRequiredMsg(fieldInfo),
      },
      {
        validator: this.inputValueLimitValidator(fieldInfo),
      },
    ];
    // 当前输入框的规则
    const curFiledRules = [];

    // 是否是 总脉冲数 计算公式中的 成员参数
    const isPulseTotalMemberParams = [
      StimulusParamFormFieldEnum.InnerStrandPulseCount,
      StimulusParamFormFieldEnum.StrandPulseCount,
      StimulusParamFormFieldEnum.PlexusCount,
      StimulusParamFormFieldEnum.PlexusInnerPulseCount,
    ].includes(fieldInfo.key);
    if (isPulseTotalMemberParams) {
      curFiledRules.push((formInstance) => ({
        validator: this.pulseTotalMemberParamsValidator(formInstance),
      }));
    }

    // 是否是 刺激时长 计算公式中的 成员参数
    const isTreatmentTimeMemberParams = [
      StimulusParamFormFieldEnum.InnerStrandPulseCount,
      StimulusParamFormFieldEnum.StrandPulseFrequency,
      StimulusParamFormFieldEnum.StrandPulseCount,
      StimulusParamFormFieldEnum.IntermissionTime,
      StimulusParamFormFieldEnum.PlexusCount,
      StimulusParamFormFieldEnum.PlexusInnerFrequency,
      StimulusParamFormFieldEnum.PlexusInterFrequency,
      StimulusParamFormFieldEnum.PlexusInnerPulseCount,
    ].includes(fieldInfo.key);
    if (isTreatmentTimeMemberParams) {
      curFiledRules.push((formInstance) => ({
        validator: this.treatmentTimeMemberParamsValidator(formInstance),
      }));
    }

    return [...baseRules, ...curFiledRules];
  };

  /**
   * 输入框 输入限制 校验器（所有输入框通用）
   * @param fieldInfo
   */
  private inputValueLimitValidator =
    (fieldInfo: StimulusParamFormField) =>
    async (_: any, value: number): Promise<string | void> => {
      return new Promise((resolve, reject) => {
        // 输入内容超出限制时
        const numberValue = Number(value);
        const minValue = fieldInfo.rules.min;
        const maxValue = fieldInfo.rules.max;
        if (numberValue < minValue || numberValue > maxValue) {
          return reject(this.getInputValueLimitMsg(fieldInfo));
        }

        resolve();
      });
    };

  /**
   * 总脉冲数 相关参数校验器（总脉冲数 计算公式 中的 成员参数）
   * @param formInstance
   */
  private pulseTotalMemberParamsValidator =
    (formInstance) =>
    async (_rule: RuleObject, value: any): Promise<string | void> => {
      return new Promise((resolve, reject) => {
        const formValues = formInstance.getFieldsValue();
        const { isValid, message } = this.validatePulseTotal(formValues);

        if (!isValid) {
          return reject(message);
        }

        resolve();
      });
    };

  /**
   * 校验 总脉冲数 => 是否超限
   */
  private validatePulseTotal = (formValues) => {
    const { intl } = this.props;
    const { renderFields } = this.state;
    const pulseTotalField = renderFields.find(
      (it) => it.key === StimulusParamFormFieldEnum.PulseTotal,
    );
    const min = pulseTotalField.rules.min;
    const max = pulseTotalField.rules.max;
    // 公式计算出来的 总脉冲数
    const pulseTotal = calculatePulseTotal(formValues);
    // 超出限制
    const isValid = pulseTotal <= max && pulseTotal >= min;

    return {
      isValid,
      message: !isValid
        ? intl.formatMessage(
            { id: 'point.cureProject.params.rules.pulseTotalLimit' },
            {
              minValue: min,
              maxValue: max,
            },
          )
        : '',
    };
  };

  /**
   * 刺激时长 相关参数校验器（刺激时长 计算公式 中的 成员参数）
   * @param formInstance
   */
  private treatmentTimeMemberParamsValidator =
    (formInstance) =>
    async (_rule: RuleObject, value: any): Promise<string | void> => {
      return new Promise((resolve, reject) => {
        const formValues = formInstance.getFieldsValue();
        const { isValid, message } = this.validateTreatmentTime(formValues);
        if (!isValid) {
          return reject(message);
        }

        resolve();
      });
    };

  /**
   * 校验 刺激时长 => 是否超限
   */
  private validateTreatmentTime = (formValues) => {
    const { intl } = this.props;
    const { renderFields } = this.state;
    const treatmentTimeField = renderFields.find(
      (it) => it.key === StimulusParamFormFieldEnum.TreatmentTime,
    );
    const min = treatmentTimeField.rules.min;
    const max = treatmentTimeField.rules.max;
    // 公式计算出来的 刺激时长
    const treatmentTime = calculateTreatmentTime(formValues);
    // 超出限制
    const isValid = treatmentTime <= max && treatmentTime >= min;

    return {
      isValid,
      message: !isValid
        ? intl.formatMessage(
            { id: 'point.cureProject.params.rules.treatmentTimeLimit' },
            {
              minValue: min,
              maxValue: max,
            },
          )
        : '',
    };
  };

  /**
   * 获取输入框必填项提示信息
   * @param fieldInfo
   */
  private getInputRequiredMsg = (fieldInfo: StimulusParamFormField) => {
    const { intl, stimulusTemplate } = this.props;
    const stimulateType = stimulusTemplate?.stimulateType;
    let type;
    if (stimulateType === StimulateTypeEnum.iTBS) {
      type = 'iTBS';
    } else if (stimulateType === StimulateTypeEnum.cTBS) {
      type = 'cTBS';
    } else if (stimulateType === StimulateTypeEnum.rTMS) {
      type = 'rTMS';
    }
    const label = intl.formatMessage({ id: `point.cureProject.params.${type}.${fieldInfo.key}` });

    return intl.formatMessage({ id: 'point.cureProject.params.rules.mustWrite' }, { value: label });
  };

  /**
   * 获取当前输入框 输入限制 提示信息
   * @param fieldInfo
   */
  private getInputValueLimitMsg = (fieldInfo: StimulusParamFormField) => {
    const { rules: fieldRules } = fieldInfo;
    const { precision, min, max } = fieldRules;

    if (min === undefined || max === undefined) {
      return;
    }

    let handledMin = min,
      handledMax = max;
    // 如果当前字段精度大于 0,则需要将整数转化为小数的形式，如： 25 => 25.0
    if (precision > 0) {
      // @ts-ignore
      handledMin = handledMin.toFixed(1);
      // @ts-ignore
      handledMax = handledMax.toFixed(1);
    }

    const { intl } = this.props;
    return intl.formatMessage(
      { id: 'point.cureProject.params.rules.valueLimit' },
      { minValue: handledMin, maxValue: handledMax },
    );
  };

  /**
   * 刺激类型下拉框
   */
  private renderTemplateTypeItem = (): JSX.Element => {
    return (
      <Item
        label={<FormattedMessage id="point.cureProject.params.type.label" />}
        labelAlign="left"
        name="stimulateType"
        className={styles.mgb4}
      >
        <Select getPopupContainer={() => document.querySelector('.stimulus-template-form')} onChange={this.changeTemplateType}>{this.getStimulateSelectOptions()}</Select>
      </Item>
    );
  };

  /**
   * 获取刺激类型下拉选项
   * https://mqhfidmks7.feishu.cn/sheets/NUgjsmHcrhQCYOt2BcFcUgZ9nLe
   */
  private getStimulateSelectOptions = () => {
    const { diseaseType } = this.props;

    return StimulateSelectOptions.map((it) => {
      const { value, text } = it;

      // // 抑郁症只支持使用一种刺激参数类型 iTBS
      // if ([DiseaseTypeEnum.Depression].includes(diseaseType)) {
      //   if (value !== StimulateTypeEnum.iTBS) {
      //     return;
      //   }
      // }
      // 失语、运动障碍、抑郁症支持使用两种刺激参数类型 iTBS、cTBS
      if (
        [
          DiseaseTypeEnum.Aphasia,
          DiseaseTypeEnum.MotorDeficits,
          DiseaseTypeEnum.Depression,
        ].includes(diseaseType)
      ) {
        if (![StimulateTypeEnum.iTBS, StimulateTypeEnum.cTBS].includes(value)) {
          return;
        }
      }
      // 自闭症支持使用三种刺激参数类型 iTBS、cTBS、rTMS
      else if (
        [
          DiseaseTypeEnum.Autism,
          DiseaseTypeEnum.Parkinson,
          DiseaseTypeEnum.CerebralPalsy,
          DiseaseTypeEnum.Alzheimer,
          DiseaseTypeEnum.ADHD,
        ].includes(diseaseType)
      ) {
        if (
          ![StimulateTypeEnum.iTBS, StimulateTypeEnum.cTBS, StimulateTypeEnum.rTMS].includes(value)
        ) {
          return;
        }
      }

      return (
        <Option
          value={value}
          key={value}
        >
          {text}
        </Option>
      );
    }).filter(Boolean);
  };

  /**
   * 切换刺激参数类型
   * @param stimulateType
   */
  private changeTemplateType = (stimulateType: StimulateTypeEnum) => {
    const { changeStimulusTemplate } = this.props;

    changeStimulusTemplate(stimulateType);
  };

  /**
   * 如果仅切换刺激参数类型，则重置表单内容
   * @param newStimulateType
   */
  private changeStimulateTypeOnly = (newStimulateType) => {
    let renderFields;
    if ([StimulateTypeEnum.iTBS, StimulateTypeEnum.cTBS].includes(newStimulateType)) {
      renderFields = tbsFields;
    } else if ([StimulateTypeEnum.rTMS].includes(newStimulateType)) {
      renderFields = rTMSFields;
    }

    this.setState(
      {
        renderFields: renderFields,
      },
      () => {
        this.resetForm(newStimulateType);
      },
    );
  };

  /**
   * 处理 iTBS、cTBS 相关刺激参数的值
   */
  private handeTBSValues = (params) => {
    const {
      tempFields,
      curPlexusInnerFrequency, // 丛内频率(Hz)
      curPlexusInterFrequency, // 丛间频率(Hz)
      curPlexusInnerPulseCount, // 丛内脉冲数（个）
    } = params;

    const newPlexusInnerFrequency = curPlexusInnerFrequency;
    const newPlexusInnerPulseCount = curPlexusInnerPulseCount;

    // https://mqhfidmks7.feishu.cn/sheets/NUgjsmHcrhQCYOt2BcFcUgZ9nLe
    // 如果 丛间频率 有值，则 从内频率 的最小值需要实时计算
    let minPlexusInnerFrequency = curPlexusInterFrequency * 2;
    minPlexusInnerFrequency = minPlexusInnerFrequency < 1 ? 1 : Math.round(minPlexusInnerFrequency);

    // 如果 丛内频率、丛间频率 有值，则 丛内脉冲数 的最大值需要实时计算
    let maxPlexusInnerPulseCount = Math.round(
      newPlexusInnerFrequency / curPlexusInterFrequency - 1,
    );
    maxPlexusInnerPulseCount = maxPlexusInnerPulseCount > 10 ? 10 : maxPlexusInnerPulseCount;

    // 获取 从内频率 的规则
    const plexusInnerFrequencyField = tempFields.find((it) => it.key === 'plexusInnerFrequency');
    // 修改 从内频率 的规则
    plexusInnerFrequencyField.rules.min = minPlexusInnerFrequency;
    // 获取 丛内脉冲数 的规则
    const plexusInnerPulseCountField = tempFields.find((it) => it.key === 'plexusInnerPulseCount');
    // 修改 丛内脉冲数 的规则
    plexusInnerPulseCountField.rules.max = maxPlexusInnerPulseCount;

    return {
      plexusInnerFrequency: newPlexusInnerFrequency,
      plexusInnerPulseCount: newPlexusInnerPulseCount,
    };
  };

  /**
   * 处理 rTMS 相关刺激参数的值
   */
  private handeRTMSValues = (params) => {
    const {
      tempFields,
      curStimulateType,
      curStrandPulseCount,
      curInnerStrandPulseCount, // 串内脉冲数（个）
      changedInnerStrandPulseCount, // 当前修改的 串内脉冲数 的值
      changedStrandPulseCount, // 当前修改的 脉冲串数 的值
    } = params;

    // 新的 串内脉冲数 的值
    const newInnerStrandPulseCount = curInnerStrandPulseCount;
    // 新的 脉冲串数 的值
    const newStrandPulseCount = curStrandPulseCount;

    // // 获取 串内脉冲数 的规则
    // const innerStrandPulseCountField = tempFields.find(
    //   (it) => it.key === 'innerStrandPulseCount',
    // );
    // // 获取 脉冲串数 的规则
    // const strandPulseCountField = tempFields.find((it) => it.key === 'strandPulseCount');

    // // 当前修改的是 串内脉冲数 输入框的值
    // if (changedInnerStrandPulseCount) {
    //   let max = innerStrandPulseCountField.rules.max;
    //   // 如果 脉冲串数 有值时，需要重新计算最大值
    //   if (curStrandPulseCount) {
    //     max = getMaxMultiplierInPulseTotal(PulseTotalMultiplier.InnerStrandPulseCount, {
    //       stimulateType: curStimulateType,
    //       strandPulseCount: curStrandPulseCount,
    //     });
    //   }
    //   innerStrandPulseCountField.rules.max = max;
    //   // 如果当前修改的值大于最大值，则自动修正数据
    //   newInnerStrandPulseCount =
    //     changedInnerStrandPulseCount > max ? max : changedInnerStrandPulseCount;
    // }
    // // 当前修改的是 脉冲串数 输入框的值
    // else if (changedStrandPulseCount) {
    //   let max = strandPulseCountField.rules.max;
    //   // 如果 串内脉冲数 有值时，需要重新计算最大值
    //   if (curInnerStrandPulseCount) {
    //     max = getMaxMultiplierInPulseTotal(PulseTotalMultiplier.StrandPulseCount, {
    //       stimulateType: curStimulateType,
    //       innerStrandPulseCount: curInnerStrandPulseCount,
    //     });
    //   }
    //   strandPulseCountField.rules.max = max;
    //   // 如果当前修改的值大于最大值，则自动修正数据
    //   newStrandPulseCount = changedStrandPulseCount > max ? max : changedStrandPulseCount;
    // }

    return {
      strandPulseCount: newStrandPulseCount,
      innerStrandPulseCount: newInnerStrandPulseCount,
    };
  };

  /**
   * 表单值变化事件
   * @param changeValues
   * @param values
   */
  private handleChangeValues = (changeValues: any, values: any) => {
    console.log('changeValues', changeValues);
    console.log('values', values);
    const { diseaseType, handleChangeValues } = this.props;

    const newStimulateType = changeValues.stimulateType;
    // 仅切换刺激参数类型
    if (newStimulateType) {
      this.changeStimulateTypeOnly(newStimulateType);
      return;
    }

    // 修改 刺激参数类型 之外的值
    let newValues;
    let tempFields;
    const {
      stimulateType: curStimulateType,
      plexusInnerFrequency: curPlexusInnerFrequency, // 丛内频率(Hz)
      plexusInterFrequency: curPlexusInterFrequency, // 丛间频率(Hz)
      plexusInnerPulseCount: curPlexusInnerPulseCount, // 丛内脉冲数（个）
      innerStrandPulseCount: curInnerStrandPulseCount, // 串内脉冲数（个）
      strandPulseCount: curStrandPulseCount, // 脉冲串数（串）
    } = values;

    if ([StimulateTypeEnum.iTBS, StimulateTypeEnum.cTBS].includes(values.stimulateType)) {
      tempFields = lodash.cloneDeep(tbsFields);

      const handledValue = this.handeTBSValues({
        tempFields,
        curPlexusInnerFrequency,
        curPlexusInnerPulseCount,
        curPlexusInterFrequency,
      });
      newValues = Object.assign({}, values, handledValue);
    } else if ([StimulateTypeEnum.rTMS].includes(values.stimulateType)) {
      tempFields = lodash.cloneDeep(rTMSFields);

      // 当前修改的 串内脉冲数 的值
      const changedInnerStrandPulseCount = changeValues?.innerStrandPulseCount;
      // 当前修改的 脉冲串数 的值
      const changedStrandPulseCount = changeValues?.strandPulseCount;
      const handledValue = this.handeRTMSValues({
        tempFields,
        curStimulateType,
        curStrandPulseCount,
        curInnerStrandPulseCount,
        changedInnerStrandPulseCount,
        changedStrandPulseCount,
      });
      newValues = Object.assign({}, values, handledValue);
    }

    /* ----------------------- 以下存放 rTMS、iTBS、cTBS 公共的处理逻辑 ----------------------- */
    // https://neuralgalaxy.pingcode.com/pjm/items/649bdb0ed0030c5c926c823c?#LFY-897
    // 当脉冲串数为 1 时，刺激间隔 应为 空值
    if (curStrandPulseCount === 1) {
      delete newValues.intermissionTime;
    } else if (curStrandPulseCount !== 1 && newValues.intermissionTime === undefined) {
      // const defaultStimulusTemplate = getDefaultStimulusTemplate(values.stimulateType, diseaseType);
      // 如果脉冲串数不为 1，且刺激间隔值不存在，则给一个默认值
      // newValues.intermissionTime = defaultStimulusTemplate.intermissionTime;
      // newValues.intermissionTime = 1;
      newValues.intermissionTime = undefined;
    }

    // 注意：公式计算一定要放在最后，否则计算出来的值会有问题
    // 公式计算出来的 总脉冲数
    const pulseTotal = calculatePulseTotal(newValues);
    newValues.pulseTotal = pulseTotal;

    // 公式计算出来的 刺激时长
    const treatmentTime = calculateTreatmentTime(newValues);
    newValues.treatmentTime = treatmentTime;

    /* ----------------------- 以上存放 rTMS、iTBS、cTBS 公共的处理逻辑 ----------------------- */

    console.log('newValues', newValues);

    // 调整表单控件相关配置信息
    this.setState({
      renderFields: tempFields,
    });

    // 使用 requestAnimationFrame 延迟更新，避免阻塞 UI
    requestAnimationFrame(() => {
      this.formRef.current?.setFieldsValue({
        ...newValues,
      });
      // 更新完表单数据后，重新校验一遍表单（避免当前字段值改变时，相关的字段没有触发校验）
      this.formRef.current?.validateFields();
    });

    // 移除 forceUpdate()，让 React 自然更新，避免强制重渲染
    handleChangeValues(newValues);
  };

  private getInitialValue = (template: StimulateTemplateModel): Store => {
    return { ...template };
  };

  private resetForm = (stimulateType: StimulateTypeEnum) => {
    const { diseaseType } = this.props;

    const defaultStimulusTemplate = getDefaultStimulusTemplate(stimulateType, diseaseType);
    this.formRef.current.setFieldsValue({
      ...defaultStimulusTemplate,
    });
    // 重置完表单数据后，重新校验一遍表单，避免部分输入框还呈现错误状态
    this.formRef.current.validateFields();
  };
}

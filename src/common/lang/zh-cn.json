{"app": {"mailToPre": "可向", "mailToLast": "发送邮件以解决此问题", "restart": "重启", "restartImmediately": "立即重启", "close": "关机", "NeuralGalaxy": "优脑银河", "NeuralGalaxyApp": "优脑银河客户端", "NeuralGalaxyFancy": "<b>优脑银河</b>", "name": "个体精准脑功能图谱系统", "version": "版本", "publishVersion": "发布版本", "uploadData": "上传数据", "uploadTip": "您可以选择用文件夹或者压缩文件形式上传数据，同时可以将此上传数据进行分组，这会对数据分析很有帮助", "uploadSingleTip": "您可以选择用文件夹或者压缩文件形式上传数据", "surgeUploadMsg": "若上传数据中存在Bold数据，请填写以下信息:", "uploadStartNotification": "上传时，请勿离开本页面，否则数据将会丢失", "noData": "没有数据", "noUploadData": "受试者没有数据，点击\"上传数据\"按钮上传", "refreshData": "刷新处理数据", "dataAnalysis": "数据分析", "downloadImg": "下载图片", "downloadData": "下载数据", "downloadTitle": "数据下载", "downloadLimit": "您下载的文件过大，请使用优脑银河客户端进行下载。", "downloadTemplateData": "下载文件模板", "downloadTemplateCont1": "上传填写完成的序列信息文件", "downloadTemplateCont2": "点击区域选择文件", "uploadTitle": "数据上传", "uploadLimit": "您上传的文件已超过300MB，请使用优脑银河客户端进行上传。", "selectDownloadCont": "下载数据", "selectUploadCont": "上传数据", "uploadFileMode": "上传文件方式", "folder": "文件夹", "local": "本地文件", "compressed": "压缩文件", "uploadSingle": "单个上传", "uploadMore": "批量上传", "nuclearMagnetic": "核磁共振机器获取", "uploadAllRun": "处理扫描数据中所有的序列", "uploadCheckRun": "处理扫描数据中选中的序列", "uploadAllRunTask": "上传的扫描数据中有EVS文件", "uploadCheckRunTask": "需要上传序列信息文件", "seeSubjectInfo": "查看受试者详情", "downloadJob": "若要分项下载受试者的处理结果，", "checkEmailApp": "请检测默认电子邮件程序的设置", "click": "点击", "inprogress": "传输进度", "downloadAllData": "按钮以下载该类别的处理结果", "clientDownloadTip": "请使用客户端下载数据", "viewSubjectDetail": "进入该受试者详情页下载该受试者分项数据", "downloadNoteTitle": "下载提示", "downloadNoteContent": "您选中的数据已进入下载流程", "uploadNoteTitle": "上传提示", "uploadNoteContent": "您选中的数据已进入上传流程", "description": "脑神经成像", "officialWebsite": "https://www.neuralgalaxy.com/zh", "switchLanguage": "切换语言为English", "switchApp": "切换至{name}", "switchBoxServer": "切换服务器为磁共振影像数据处理软件", "switchCloudServer": "切换服务器为云医学影像数据处理软件", "uploadStatusTooltip": "上传状态", "downloadStatusTooltip": "下载状态", "shrinkage": "收缩", "homeTooltip": "首页", "uploadTooltip": "上传数据", "uploadState": "上传状态", "downloadState": "下载状态", "downloadTooltip": "下载数据", "analysisTooltip": "数据分析", "settingsTooltip": "设置", "userTooltip": "用户", "deleteSubjectTooltip": "删除受试者", "cannotDeleteSubjectTooltip": "没有删除受试者的权限", "editSubjectTooltip": "编辑受试者信息", "browserTip": "不支持当前浏览器，请用 Chrome 或 Firefox 使用此产品", "hour": "小时", "minute": "分", "second": "秒", "hours": "小时", "minutes": "分", "seconds": "秒", "itemsPerPage": "/ 页", "noConnent": "网络状态出现异常，请检查网络连接", "switchAppMsg": "请等待数据传输完成后切换产品!", "partitionTitle": "组水平功能图谱", "downloadPdf": {"func": "下载当前分区数的解剖位置(模板)", "bold": "下载当前分区数的解剖位置(模板)"}, "viewPdf": {"func": "查看当前分区数的解剖位置(模板)", "bold": "查看当前分区数的解剖位置(模板)"}, "downloadPdfTitle": "分区模板", "error": {"400": "请求无效", "404": "找不到你要找的", "403": "禁止访问", "connection": "链接错误，请稍后重试", "serverError": "内部服务器错误，请稍后重试", "expired": "许可过期", "defendError": "为了能够提供更加丰富、便捷、安全的服务，优脑银河正对系统进行维护。", "httpErrorCode": {"SV13004": "用户名已存在", "PNT_RP10001": "无图文报告下载权限，请联系管理员开通", "PNT_RP10002": "该病症数据无法下载图文报告", "PNT_RP10003": "该病症数据无法下载图文报告", "PNT_P3409": "该方案已被申请新方案"}}, "booleanValue": {"true": "是", "false": "否"}, "buttons": {"userArgument": "用户协议", "close": "关闭", "closeApp": "关闭系统", "add": "添加", "back": "返回", "cancelUpload": "取消上传", "upload": "上传", "uploadFailed": "上传失败", "cancelDownload": "取消下载", "download": "下载", "downloadFailed": "下载失败", "compress": "压缩", "viewProcessingResults": "查询处理结果", "edit": "编辑", "save": "保存", "saveAndExit": "保存并退出", "saveAndUpload": "保存并上传", "noSave": "不保存", "search": "搜索受试者", "eusure": "确定", "cancel": "取消", "deactivate": "冻结", "active": "激活", "generatePwdToken": "发送重置密码链接", "update": "更新", "restart": "重启", "confirm": "确定", "filter": "筛选", "reset": "重置", "manage": "管理", "delete": "删除", "agreement": "同意", "recallOpera": "撤回操作", "filterOne": "从当前处理结果中下载", "filterMore": "在多个处理结果中下载", "previous": "上一步", "next": "下一步", "tryAgain": "重试", "abort": "终止", "triggerDesc": "点击降序", "triggerAsc": "点击升序", "cancelSort": "取消排序", "perPage": " /页", "items_per_page": "/页", "prevPage": "上一页", "nextPage": "下一页", "noFilters": "没有过滤条件", "selectionAll": "全选所有", "selectInvert": "仅选中当前页", "use": "使用"}, "desktop": {"paused": "暂停", "timeLeft": "剩余时间", "downloadingFile": "正在下载文件", "uploadingFile": "正在上传文件", "downloadingErrorFile": "下载失败文件", "uploadingErrorFile": "上传失败文件", "downloadFileStopError": "网络状态不佳，当前下载任务已暂停，请检查网络连接后点击“开始”按钮重新下载(下载失败的文件，可在”传输完成“中重新下载)", "uploadFileStopError": "网络状态不佳，当前上传任务已暂停，请检查网络连接后点击“开始”按钮重新上传(上传失败的文件，可在”传输完成“中重新上传)", "showAll": "查看全部", "putAway": "收起详情", "reUploadErrorFiles": "重新上传错误文件", "reDownloadErrorFiles": "重新下载错误文件", "viewProcessingResults": "查询处理结果", "downloadProjectData": "下载项目数据", "dataTransferStatus": "数据传输状态", "downloadAllSubjectData": "下载受试者所有数据", "deleteAllSubjectData": "删除受试者所有数据", "downloadProcessingData": "下载处理的数据", "deleteProcessingData": "删除处理的数据", "uploadDiskNotEnoughSpace": "请确认对下载目录有操作权限及足够磁盘空间,以免因磁盘空间不足导致上传失败", "downloadDiskNotEnoughSpace": "请确认对下载目录有操作权限及足够磁盘空间,以免因磁盘空间不足导致下载失败", "transferDiskNotEnoughSpace": "因{path}存储空间不足,下载任务已暂停，请清理存盘确认有足够空间后继续下载", "windowsTransferDiskNotEnoughSpace": "因{path}盘存储空间不足,下载任务已暂停，请清理存盘确认有足够空间后继续下载", "uploadingTransferDiskNotEnoughSpace": "因{path}存储空间不足,下载任务已暂停，请清理存盘确认有足够空间后继续上传", "uploadingWindowsTransferDiskNotEnoughSpace": "因{path}盘存储空间不足,下载任务已暂停，请清理存盘确认有足够空间后继续上传", "editSubjectInformation": "编辑受试者信息", "viewProcessingData": "查看处理的数据", "downloadSelectedData": "下载选中的文件", "showMoreFiles": "显示更多文件", "showDefaultFiles": "显示默认文件", "addSubject": "添加受试者", "chooseSubject": "选择受试者", "uploadedFileName": "上传数据文件名", "uploadTime": "上传时间", "checkForUpdate": "检查更新", "forceUpdateMsg": "注意：本次更新无法中途退出", "updateNow": "立即更新", "updateInfo": "新版本更新程序已下载完成。", "noUpdate": "已经在最新版本，目前没有更新。", "updating": "正在更新", "menuLearnMore": "查看更多", "aboutUs": "关于我们", "menuContactUs": "联系我们", "sureToDeleteSubject": "你确定你想删除这个受试者信息么？", "sureToDeleteJob": "你确定你想删除这条实验数据么?", "calculating": "计算中", "downloadProgress": "下载进度", "uploadProgress": "上传进度", "Transferring": "正在传输  ({ num })", "uploading": "正在上传 ({ num })", "downloading": "正在下载 ({ num })", "transferCompleted": "传输结果 ({ num })", "clearRecording": "清除记录", "transferCompletionIime": "传输完成时间: ", "takeUpTime": "耗时: ", "totalData": "数据总量: ", "activation": "开始/暂停", "cancelTransfer": "取消传输", "clientMoreData": "您下载的文件过大，请使用优脑银河客户端进行下载。", "clientDownloadInfo": "网页端仅可以下载总量 1GB 以内的文件，下载更大量的文件请使用", "confirmInfo": "有未传输完成的任务，是否要重现开始传输？", "confirmTitle": "传输任务", "downloadFilePath": "查看文件", "dataBeingProcessed": "数据正在处理中，请稍后查看处理结果文件", "dataFaile": "数据处理失败，没有产生处理结果文件", "nodata": "暂无数据", "cantOpen": "无法打开文件目录", "cantFindPath": "无法找到文件目录", "updateReminderModal": {"newVersion": "发现新版本", "updateAnnouncement": "更新公告", "cancel": "取消", "updateNow": "立即更新", "exitApp": "退出应用", "noMorePrompts": "不再提示", "automaticRestart": "后自动重启", "updating": "正在更新中", "updateError": "更新失败，请重试。", "forceUpdateError": "强制更新失败，请联系管理员。", "getUpdateInfoError": "获取更新信息失败。"}, "updatePreviewModal": {"newVersion": "发现预发布版本", "updateAnnouncement": "预发布公告", "updateNow": "了解"}}}, "home": {"search": {"placeholder": "请输入项目名称或受试者ID", "placeholderWithFile": "请输入文件名称或受试者ID"}}, "layout": {"header": {"menuUpload": "数据上传", "menuSubject": "受试者数据库", "linkSettings": "设置", "linkLogout": "登出", "userPrompt": "用户名", "dropDownProject": "全部", "downloadDesktop": "下载", "downloadWin": "Windows", "win32Bit": "32位操作系统版", "win64Bit": "64位操作系统版", "downloadLinux": "Linux", "downloadMac": "<PERSON>", "addOrg": "添加机构", "downloadDesktopTip": "下载客户端", "downloadRule": "所需硬件环境最低配置如下：", "downloadCpu": "CPU Intel i5 CPU Dual Core及以上", "downloadMemory": "内存大于等于4G", "downloadDisk": "硬盘100G及以上，至少300MB可用空间", "downloadVga": "视频采集卡支持3D,至少64MB RAM", "upload": "上传", "download": "下载", "analysis": "数据分析", "accountSettings": "帐户设置", "myOrganization": "我的机构", "downloadDesktopApp": "下载桌面应用程序", "message": "消息", "connecctedCloud": "连接至 : “云”医学影像数据处理软件", "connecctedBox": "连接至 : 磁共振影像数据处理软件", "endPointWaringContent": "您现在正在处理数据。如果切换，数据传输过程将终止", "endPointWaringTitletoCloud": "确定切换到云平台?", "endPointWaringTitletoBox": "确定切换到磁共振影像数据处理软件?", "switchEndpointWarning": "切换终端警告", "myAccount": "我的账户"}, "notification": {"notification": "通知", "downloadSuccessfully": "自动下载成功", "downloadFailed": "自动下载失败", "dataId": "数据ID", "fileName": "文件名", "downloadIPAddress": "下载地址", "reDownload": "重新下载", "boxDetail": "查看扫描数据以获取详细信息", "cloudDetail": "该扫描数据的处理结果在“云”医学影像数据处理软件中", "deleteTip": "删除此条通知"}, "settingNav": {"linkProfile": "我的资料", "linkSystem": "系统", "linkOrganizations": "组织", "linkProjects": "项目", "linkUsers": "用户"}, "footer": {"version": "版本", "copyright": "版权所有 &copy; 2020&nbsp;", "reserved": " "}}, "register": {"message": "注册优脑银河账户:", "resetpwdmessage": "重置密码", "form": {"firstNameFieldName": "名", "firstNamePlaceHolder": "请输入名", "lastNameFieldName": "姓", "lastNamePlaceHolder": "请输入姓", "userNamePlaceHolder": "请输入用户名,第一位必须是字母", "usernameRepeat": "用户名已存在", "uniqueIdRepeat": "唯一标识已存在", "passwordFieldName": "密码", "passwordPlaceHolder": "请输入密码", "confirmPasswordFieldName": "确认密码", "confirmPasswordPlaceHolder": "请确认密码", "buttonRegister": "注册", "buttonResetPwd": "重置密码", "userName": "用户名", "email": "邮箱: ", "contactSupport": "联系支持", "waringDesktopVersion": "客户端当前版本与“云”医学影像数据处理软件不兼容。 请升级客户端到{version}版本", "waringBoxversion": "磁共振影像数据处理软件当前版本与“云”医学影像数据处理软件端版本不兼容，无法切换。"}, "updateUserNameModal": {"title": "请输入用户名", "des": "当前账户没有设置用户名,请设置用户名,再次登录时可使用用户名登录,密码与邮箱方式登录密码一致."}, "error": {"firstNameRequired": "名不能为空", "firstNameNotMatch": "名不正确, 1-10 位数字/字母/中文/空格/_ /-", "lastNameRequired": "姓不能为空", "lastNameNotMatch": "姓不正确, 1-10 位数字/字母/中文/空格/_ /-", "passwordLength": "密码必须介于8到20个字符之间", "passwordRequired": "密码不能为空", "passwordNotMatch": "确认密码不正确", "userNameRequired": "用户名不能为空", "userNameNotMatch": "用户名不正确, 3-20 位数字/字母/_ /-, 第一位必须是字母", "oldPwdTrue": "请确认旧密码输入正确", "oldNotNew": "请确认旧密码与新密码不同"}, "invalidToken": "访问令牌无效或者已经过期。请联系系统管理员重新发送注册邀请", "loginInstruction": "已经注册, ", "loginLink": "登录.", "alreadyRegistered": "已经注册过？登录", "success": "优脑银河账户注册成功", "resetPwdSuccess": "成功重置密码", "loginLink2": "登录"}, "reqPwdResetToken": {"message": "请输入你注册时使用的邮箱地址，重置密码邮件会发送到你的邮箱", "button": "发送", "loginLink": "登录", "emailNotFound": "邮箱地址没有找到，请联系系统管理员", "success": "邮件已经发送至注册邮箱，请按照邮件里的步骤重置密码", "error": "重置密码链接发送失败，请稍后重试。"}, "login": {"adminMessage": "管理员登录", "message": "登录", "staySignedIn": "&nbsp;保持登录", "forgotPassword": "忘记密码", "licenseError": " 您的许可权限禁用{app}终端，要使用此功能请{ contact }更新您的许可信息。", "contact": "联系我们", "desktop": "客户端", "client": "网页端", "form": {"emailFieldName": "邮箱地址", "emailPlaceHolder": "请输入邮箱地址或用户名", "emailOnlyPlaceHolder": "请输入邮箱地址", "accountPlaceHolder": "请输入本地管理员账号", "account": "账号", "passwordFieldName": "密码", "passwordPlaceHolder": "请输入密码", "buttonLogin": "登录", "choseEndpoint": "连接终端", "cloud": "云医学影像数据处理软件", "box": "磁共振影像数据处理软件", "boxSystem": "磁共振影像数据处理系统"}, "error": {"validEmail": "邮箱地址格式不正确", "emailRequired": "邮箱地址或者用户名不能为空", "adminAccoutRequired": "本地管理员账号不能为空", "passwordRequired": "密码不能为空", "authFailed": "账号或密码错误,请重新输入。如果忘记密码请联系管理员重置。", "sessionTimeout": "无操作时间过长，身份认证失败，请重新登录", "offline": "本地网络异常", "networkError": "网络连接错误", "timeout": "网络请求超时"}}, "kiosk": {"resetPassword": "请访问以下网址置密码: ", "emailPrompt": "请发送邮件至 <EMAIL>"}, "account": {"orgianzation": "机构", "username": "用户名", "name": "名称", "firstName": "名", "lastName": "姓", "email": "邮箱", "password": "密码", "userConfig": "用户配置", "originalPassword": "请输入旧密码", "newPassword": "请输入新密码", "reconfirmNewPassword": "请确认密码", "pwdPlaceholder": "8-20个字符，必须包含大小写字母和数字", "projects": "项目", "role": "角色", "status": "状态", "table": {"projectName": "项目名称", "myRole": "我的角色", "projectAdmins": "项目管理员", "subjects": "受试者数量", "scans": "扫描数据数量", "groups": "组数量", "plans": "规划数量", "status": "状态", "sync": "项目信息同步", "automatic": "自动下载"}}, "settings": {"org": {"title": "机构", "btnNew": "创建新机构", "orgFormPageTitle": "机构信息", "orgDetail": "机构详情", "orgMembers": "机构成员", "form": {"nameFieldName": "名称", "namePlaceHolder": "机构名称", "contactEmailFieldName": "联系电子邮件", "contactEmailPlaceHolder": "机构联系电子邮件", "isActiveFieldName": "状态", "valueActive": "激活", "valueInactive": "未激活", "nameRequired": "机构名称不能为空", "nameRepeat": "机构名称不能重复", "contactEmailRequired": "机构联系电子邮件不能为空", "validContactEmail": "电子邮件格式无效"}, "orgCreated": "机构已创建", "orgUpdated": "机构信息已更新", "list": {"searchPlaceholder": "搜索机构", "noDataIndicator": "目前系统中没有机构, 点击\"创建新机构\"创建"}, "raList": {"orgRaListTitle": "顾问", "removeOrgRaList": "删除顾问", "searchPlaceholder": "搜索顾问"}}, "project": {"title": "项目", "btnNew": "创建新项目", "taskTitle": "任务设计", "projectFormPageTitle": "项目信息", "newProjectFormPageTitle": "创建项目信息", "projectMemberPageTitle": "项目成员", "emptyTaskDesign": "当前没有任务设计, 点击按钮创建", "emptyCondition": "当前没有任务条件, 点击按钮创建", "choicePrjectForJob": "选择项目以下载处理结果", "refreshTip": "刷新项目", "editTip": "编辑项目信息", "uploadTip": "上传数据", "downloadTip": "下载数据", "downloadDataImgTip": "下载图片和数据", "form": {"taskName": "项目类型", "orgIdFieldName": "机构", "nameFieldName": "名称", "namePlaceHolder": "项目名称", "contactEmailFieldName": "联系电子邮件", "contactEmailPlaceHolder": "项目联系电子邮件", "isActiveFieldName": "状态", "valueActive": "激活", "valueInactive": "未激活", "nameRequired": "项目名称不能为空", "contactEmailRequired": "项目联系联系电子邮件不能为空", "validContactEmail": "电子邮件格式无效", "orgIdRequired": "机构不能为空", "orgIdPlaceHolder": "选择机构", "timeofRepetition": "扫描TR(秒)", "skip": "移除起始帧数", "timeofRepetitionRequired": "重复时间必须在0.2 - 10秒之间数字", "skipRequired": "移除起始帧数必须为非负整数，最大长度为10", "taskOptions": {"resting": "静息态", "task": "任务态", "tip": "请选择项目类型", "restingAndTask": {"title": "静息态/任务态", "subTitle": "按照静息态处理的组:", "allRun": "所有组", "restingState": "标记为静息态的组"}}}, "newTask": {"createTitle": "请创建实验任务", "createButton": "任务", "name": "名称", "description": "描述", "title": "任务设计", "addTask": "创建任务设计", "condition": "任务条件", "selectConditions": "请选择任务条件", "startTime": "开始时间 (秒)", "durationTime": "持续时长 (秒)", "startTimePH": "例如 2.5", "durationTimePH": "例如 3.6", "tip": "任务条件不能为空, 请填写完整", "taskConditionsEmpty": "任务条件不能为空", "timeTip": "开始时间必须大于等于0, 持续时长必须大于0", "deleteTitle": "点击\"确定\"删除", "deleteSubtitle": "存在于以下已创建的任务中，删除此条件同时会删除使用此条件的任务。", "nameEmpty": "任务名称不能为空", "namePlaceholder": "最多20个字符，可使用字母、数字、下划线，中横线，如(a-4)"}, "newConditions": {"nameEmpty": "任务名称不能为空", "createTitle": "请先创建任务条件", "title": "任务条件", "condition": "名称", "addCondition": "创建任务条件", "nameRepeat": "任务条件名称不能重复", "namePlaceholder": "最多10个字符，可使用字母、数字", "description": "描述", "desPlaceholder": "请输入描述..."}, "projectCreated": "项目已创建", "projectTypeError": "请选择项目类型", "projectUpdated": "项目信息已更新", "list": {"searchPlaceholder": "搜索项目", "noDataIndicator": "目前没有项目, 点击\"创建项目\"创建"}}, "user": {"raUserOrgPageTitle": "用户所在的机构", "title": "用户", "btnNew": "邀请用户", "userFormPageTitle": "用户信息", "userProjectMemberPageTitle": "用户所在的项目", "inviteUsersTitle": "邀请用户", "inviteUserInput": {"inputPrompt": "输入电子邮件列表，用逗号(,) / 空格( ) / 分号(;) / 或者竖线(|)分开, 最多50个电子邮件。", "duplicateMessage": "下面的电子邮件重复", "moreThanMaxMessage": "电子邮件过多", "asyncValidationFailMessage": "列表中展示的账户已经被邀请过，请您更换其他邮件", "invalidFormatMessage": "电子邮件格式无效", "validateButton": "添加用户", "removeAllButton": "删除所有邮件", "submitButton": "邀请", "maxItemTip": "最多50个电子邮件"}, "userRole": {"projectAdminOrMemberFieldLabel": "项目管理员或成员", "ngRAFieldLabel": "顾问", "pointNgRAFieldLabel": "技术支持", "ngRAjoinOrgTittle": "请选择顾问要加入的机构", "pointNgRAjoinOrgTittle": "请选择技术支持要加入的机构", "orgFieldLabel": "机构", "orgFieldPlaceholder": "请选机构", "projectFieldLabel": "项目", "projectFieldPlaceholder": "请选项目", "ngAdminFieldLabel": "系统管理员", "orgAdminFieldLabel": "机构管理员", "ngAdminCannotBeNGRA": "系统管理员不能同时为顾问", "projectAdminFieldLabel": "项目管理员", "projectMemberFieldLabel": "项目成员", "selectedProjectTip": "选择项目为项目成员, 勾选为项目管理员", "ngAdminCannotBeOrgAdmin": "系统管理员不能同时为机构管理员", "ngAdminCannotHaveOrg": "系统管理员不能同时为机构成员", "ngAdminCannotBeProjectAdmin": "系统管理员不能同时为项目管理员", "ngAdminCannotHaveProject": "系统管理员不能同时为项目成员", "orgAdminMustHaveOrg": "机构管理员必须是机构成员", "projectAdminMustHaveOrg": "项目管理员必须是机构成员", "projectAdminMustHaveProject": "项目管理员必须是项目成员", "userMustHaveOrg": "普通用户必须是一个机构的成员", "userMustHaveProject": "普通用户必须是至少一个项目的成员", "ngRACannotBeOrgAdmin": "顾问不能同时为机构管理员", "ngRACannotHaveOrg": "顾问不能同时为机构成员", "ngRACannotBeProjectAdmin": "顾问不能同时为项目管理员", "ngRACannotHaveProject": "顾问不能同时为项目成员"}, "form": {"nameFieldName": "姓名", "roleFieldName": "角色", "orgIdFieldName": "机构", "projectIdFieldName": "项目", "firstNameFieldName": "名", "firstNamePlaceHolder": "名", "lastNameFieldName": "姓", "actionPoint": "开通优点使用权限", "lastNamePlaceHolder": "姓", "emailFieldName": "电子邮件", "emailPlaceHolder": "电子邮件", "isActiveFieldName": "状态", "valueActive": "激活", "valueInactive": "未激活", "adminLabel": "(管理员)", "NGAdminLabel": "系统管理员", "firstNameRequired": "名不能为空", "lastNameRequired": "姓不能为空", "emailRequired": "电子邮件不能为空", "emailNotAvailable": "电子邮件已在系统中存在", "emailAvailable": "电子邮件可用", "cannotCheckEmailAvailability": "目前不能检查电子邮件可用性", "validEmail": "电子邮件格式无效", "orgName": "机构名称"}, "userUpdated": "用户信息已更新", "profileUpdated": "信息已更新", "tokenSent": "密码令牌已发送", "list": {"searchPlaceholder": "搜索用户", "noDataIndicator": "目前没有用户, 点击\"邀请用户\"添加用户", "editUserLink": "编辑"}}, "projectMember": {"isMemberHeader": "成员", "projectMemberHeader": "项目成员", "isAdminHeader": "管理员", "touchedHeader": "已编辑", "memberHeader": "成员", "adminHasToBeInProject": "项目管理员必须是项目成员", "userProjectPrompt": "管理用户的参与的项目，在\"成员\"和\"管理员\"列中做必要的更改，然后点击\"保存\"", "projectMemberPrompt": "管理项目成员\"成员\"和\"管理员\"列中做必要的更改，然后点击\"保存\"", "pointProjectMemberPrompt": "管理治疗师和\"管理员\"列中做必要的更改，然后点击\"保存\"", "userHasNoProject": "用户不在任何项目中", "projectHasNoUser": "项目目前没有成员", "noUserIndicator": "没有额外的用户可以添加", "adminHeader": "管理员", "noProjectAdmin": "请至少设置一个用户为管理员", "notModifySelf": "不能编辑自己"}, "templateManagement": {"modalTitle": "模板管理", "importBtnTitle": "导入", "confirmDeleteTitle": "确定删除模板？", "alreadyExists": "模板管理器中已存在“{name}”模板， 是否替换？", "deleteFailed": "删除模板失败", "checkFailed": "导入失败，模板文件格式错误。", "uploadFailed": "导入失败，模板文件格式错误。"}}, "scanGroup": {"list": {"name": "组名称", "description": "描述", "action": "操作", "action2": "查看数据分组详情"}, "hidePointList": "默认隐藏患者和任务列表", "clearFilter": "清空过滤器", "subject": "受试者", "groupListTitle": "数据分组", "newButton": "新建", "newGroup": "创建分组", "newGroupDes": "当前项目没有对数据进行分组,请点击按钮", "selectJobs": "选择数据", "addJobs": "选择数据", "preTitle": "数据分组名称", "selectProjectJobs": "选择扫描文件以下载其处理结果", "selectDataErrorMsg": "已选中的数据，没有处理结果文件可供下载", "uploadAddGroup": "数据分组对数据的分析有很大的帮助,请选择/输入组名为此数据分组", "newGroupFrom": {"title": "组名称", "description": "描述", "modalHeader": "新建组", "editHeader": "编辑组", "titlePlaceholder": "一些常用的数据组名供参考:对照组/控制组", "descriptionPlaceholder": "请输入组的描述...", "nameEmpty": "组名称不能为空", "nameRepeat": "此名称已存在, 请重新输入", "nameError": "仅允许输入字母、数字、 -、_、@、中文及空格"}, "deleteGroup": {"warnningTitle": "确认要删除这个分组吗?", "warnningDes": "删除这个分组，相应的数据也会被取消划分."}, "editCard": {"selectedGroup": "选中的分组", "tipEnter": "输入新建分组名称后键盘'Enter'创建", "tipSelected": "请在下方选择分组"}, "noJobs": "没有数据划分在当前分组中,点击按钮选择数据并划分在组中", "noCurrentGroup": "选择分组,查看被划分的数据", "customGroup": "新创建的自定义组", "scangroupJobs": {"allData": "全部数据分组", "selectData": "已选中的数据分组 ({ num })", "allScanData": "所有扫描数据", "selectScanData": "已选中的扫描数据 ({ num })", "searchPH": "搜索扫描数据文件名或受试者ID", "table": {"fileName": "扫描数据文件", "subject": "受试者ID", "status": "数据状态", "anaysislyStatus": "参与分析", "createdAt": "上传时间", "createdDate": "创建时间", "finishedAt": "处理完成时间", "filterHourDate": "{ num } 小时内", "filterDaysDate": "{ num } 天内", "startTime": "从", "endTime": "至", "endLessStartError": "结束时间不可以早于开始时间", "today": "今日", "group": "分组"}}}, "subject": {"btnNew": "创建受试者", "subjectBe": "受试者已存在", "subjectBeFill": "使用已有信息", "subjectCreated": "受试者已创建", "subjectUpdated": "受试者已更新", "requery": "点击重试", "searchSubject": "搜索受试者", "subjectInfoTip": "点击查看此受试者下的所有处理结果", "processingResults": "处理结果", "uploadedFiles": "已上传文件", "downloadFiles": "下载文件", "downloadAllFiles": "下载全部文件", "uploadNotAllowed": "不允许上传", "subjectToTeTransfered": "待传输的受试者数据", "subjectBeingTransfered": "正在传输的受试者数据", "failedTransfered": "传输失败的文件", "transferStatus": "传输状态", "beingTransfered": "待传输的文件", "subjectCustId": "受试者 :", "mySubject": {"title": "我的受试者"}, "helpDocument": {"reference": "参考文献", "volumeOperTips": "Shift - 点击拖拽容积视图", "parcellation": "功能分区", "languageLaterality": "语言偏侧性", "SNR": "信噪比", "motionCorrection": "运动校正参数", "ACC": "前扣带回", "PCC": "后扣带回", "Motor": "运动区", "vmPFC": "腹内侧前额叶皮层", "anatomicalParcellation": "结构分区", "corticalThickness": "皮层厚度", "sulcalDepth": "沟回深度", "myelin": "皮层髓鞘"}, "dates": {"createdAt": "创建时间", "startedAt": "处理开始时间", "finishedAt": "处理完成时间", "readyAt": "上传时间"}, "rotateToggle": {"on": "开", "off": "关"}, "laterality": {"latLeft": "左侧优势", "latBoth": "双侧性", "latRight": "右侧优势"}, "lateralityType": {"latLanguage": {"func": "语言偏侧性指数 (-1 ~ +1):", "bold": "BOLD分区不对称性指数 (-1 ~ +1):"}, "latMemory": "记忆偏侧性指数 (-1 ~ +1):", "latFormula": "(左-右)/(左+右) (-1 ~ +1)"}, "upload": {"helpPdf": "优脑银河数据上传指南", "downloadHelpPdf": "下载优脑银河数据上传指南", "viewHelpPdf": "数据上传指南", "refreshWarning": "请不要刷新页面，若刷新现存信息将会丢失", "noDataIndicator": "受试者目前没有上传数据", "uploadStatus": "上传状态", "logicalName": "文件名", "uploadRules": "上传文件夹的根目录和子目录及文件只能包含ASCII字符。", "selectScanData": "选择上传扫描数据范围", "uploadRunInfo": "上传序列信息文件", "uploadResult": "序列信息文件检查结果展示", "selectUploadType": "选择上传方式", "support": "为了获得更好的上传体验，请尝试", "desktopApp": "客户端", "preUploadWarning": "请正确填写序列信息文件里的内容后上传扫描数据", "downloadTemplate": "下载文件模板 {version}", "restingTip": "序列信息文件可指定不同的序列应如何处理，应将其作为静息态处理或跳过不处理", "taskMainTip": "序列信息文件可指定不同的序列应如何处理，应将其作为静息态处理、任务态处理、甚至跳过不处理", "taskTip": "在任务态项目中，应为每次运行指定条件、开始时间、每个条件的持续时间", "restingError": "当前文件中没有标明要以静息态方式处理的扫描数据，请重新填写序列信息", "reupload": "重新上传文件", "runNumber": "序列号", "runNumberError": "序列号必须由三位数字组成", "evs": "EVS", "conditionError": "请修改条件，与项目中的条件保持一致", "condition": "条件", "start": "开始时间", "duration": "持续时间", "uploadedAllError": "请填写所有任务态数据在本地的位置", "uploadedAllInfo": "点击“条件文件“区域，选择文件在本地的位置", "uploadedEmptyError": "序列信息文件内容为空，请填写后重新上传", "confirm": "确认信息", "uploadZipText": "拖拽单个压缩文件到此区域，或者点击此区域选择压缩文件上传", "uploadZipSubText": "目前仅支持“zip”格式压缩文件", "uploadRunInfoTextOne": "上传填写完成的序列信息文件", "uploadRunInfoTextTwo": "拖拽文件到此区域或者点击区域选择文件", "uploadGroupLabel": "对上传数据分组：", "uploadNoteLabel": "对上传数据备注：", "allowedUploadNumber": "剩余上传数量: {numberRemaining}个", "delDicom": "你确定要删除这些文件吗？", "all": "所有文件"}, "job": {"jobStatus": "当前状态", "errorInfo": {"header": "数据处理异常信息", "colSeverity": "严重性", "colFile": "文件路径", "colMsg": "信息"}, "errorInfoMsg": {"discover_nifti": "无法找到文件，可能是文件结构不对、文件未上传成功等问题", "check_nifti": "文件格式检查未通过", "anat_reorient": "结构像重定向失败", "bold_reorient": "BOLD像重定向失败", "reorient_qc": "MRI质量分析失败", "recon1": "大脑皮层结构重建处理失败", "recon2_lh": "左脑结构重建失败", "recon2_rh": "右脑结构重建失败", "recon3": "大脑皮层结构重建表面生成处理失败", "preprocess": "预处理失败", "preprocess_qc": "预处理质量控制处理失败", "parcellate": "18分区处理失败", "functional_connectivity_step": {"func": "18分区功能连接处理失败", "bold": "18分区BOLD相关处理失败"}, "parc92": "92分区处理失败", "functional_connectivity_parc92": {"func": "92分区功能连接处理失败", "bold": "92分区BOLD相关处理失败"}, "parc152": "152分区处理失败", "functional_connectivity_parc152": {"func": "152分区功能连接处理失败", "bold": "152分区BOLD相关处理失败"}, "parc213": "213分区处理失败", "functional_connectivity_parc213": {"func": "213分区功能连接处理失败", "bold": "213分区BOLD相关处理失败"}, "tms_target": "TMS处理失败", "tms_target_xw_aphasia": "TMS处理失败", "project_T1_template": "结构像投射到模板空间失败", "project_residuals_2mm_fs_mni": "功能像残差投射到模板空间失败", "volumetric_seed_atlas": "容积视图功能连接运行失败", "snm_task": "脑功能分区-任务态fMRI运算中遇到问题"}, "syncJob": "正在同步", "syncJobFaild": "同步失败", "downloadJobPrompt": "下载本次实验数据", "clientDownloadJobTip": "不能下载本次实验数据, 请下载客户端", "rerunJobPrompt": "重新处理实验数据", "note": "数据备注", "showJobInfoPrompt": "本次处理信息", "deleteJobPrompt": "删除本次处理", "cannotDeleteJobPrompt": "没有删除数据的权限", "cannotDelRunning": "运行中，不可删除", "cannotDel": "不可删除", "noAuthcannotDel": "无删除权限", "modifyTaskPrompt": "修改实验数据任务配置", "noRerunJobPrompt": "请等待本次处理结束再重新处理", "noModifyTaskPrompt": "无须修改数据任务配置", "noShowJobInfoPrompt": "本次处理没有报错", "descriptOfTasks": "实验数据任务配置", "rerunJobConfirm": "点击 \"确定\" 重新处理数据", "task": {"title": "任务态详情", "errorType": "错误", "resting": "静息态", "task": "任务态", "addTask": "添加任务设计", "condition": "任务条件", "startTime": "开始时间 (秒)", "durationTime": "持续时长 (秒)"}}, "invalidFileReason": {"10": "mri_info 获取文件信息失败", "20": "保护的操作系统文件", "30": "上传文件中发现NIFTI文件，跳过非NIFTI文件"}, "list": {"searchPlaceholder": "搜索受试者", "noDataIndicator": "目前没有受试者, 点击\"创建受试者\"", "viewSubjectResultLink": "详情"}, "notes": {"newNotePlaceholder": "请输入报告“", "textNote": "文本报告", "imgNote": "图片报告"}, "subjectFormPageTitle": "填写受试者信息", "subjectScanPageTitle": "磁共振扫描", "subjectPageTitle": "处理结果", "subjectJobPageTitle": "处理结果", "form": {"ageFieldName": "出生年", "genderFieldName": "性别", "genderPlaceHolder": "性别", "birthYearFieldName": "出生年", "birthYearPlaceHolder": "出生年", "invalidBirthYear": "出生年不正确", "handednessFieldName": "利手性", "handednessPlaceHolder": "利手性", "handednessScoreFieldName": "利手性分数", "handednessScorePlaceHolder": "利手性分数", "invalidHandednessScore": "利手性分数应为在-100到100之间的整数", "validHandednessScorePrompt": "-100到100之间的整数 ", "ethnicityFieldName": "民族", "ethnicityPlaceHolder": "民族", "occupationFieldName": "职业", "occupationPlaceHolder": "职业", "educationFieldName": "教育程度", "educationPlaceHolder": "教育程度", "maritalStatusFieldName": "婚姻状况", "maritalStatusPlaceHolder": "婚姻状况", "projectIdFieldName": "项目", "projectIdPlaceHolder": "项目", "newSubjectPageTitle": "上传数据", "subjectCustIdFieldName": "受试者ID", "subjectCustIdPlaceHolder": "受试者ID", "subjectCustIdRequired": "受试者ID不能为空", "subjectCustIdNotMatch": "受试者ID不匹配", "invalidSubjectCustId": "3-18个字符，可使用字母、数字、中横线、下划线，如(a-4)", "specialCharactersNotMatch": "不允许输入特殊字符", "projectIdRequired": "项目不能为空", "notes": "书写报告", "latestJobStatus": "最新处理工作状态", "latestJobCreatedAt": "最新处理工作创建时间", "latestJobFinishedAt": "最新处理工作结束时间"}, "newSubjectWizard": {"run": "序列", "addRunFile": "添加序列", "selectTaskDesign": "请选择任务设计", "runTypeError": "至少一个序列必须是任务态的序列", "addRunError": "序列的任务设计不能为空", "runMatchError": "序列不匹配, 请重试", "nextButton": "下一步", "backButton": "上一步", "finalNextButton": "开始处理工作", "stepVerify": "确认受试者", "stepInput": "选择数据文件", "stepJobStatus": "处理工作状态", "verifyInstruction": "请输入受试者ID进行确认", "inputInstruction": "拖拽单个数据包文件到下面的区域， 或者点击下面的区域选择数据文件", "jobStatusInstruction": "页面会自动更新显示处理工作状态。 或者点击\"受试者概览\"至扫描页面", "linkSubject": "受试者概览", "filenameLabel": "数据包文件名", "jobStatusLabel": "处理工作状态", "buttonCheckStatus": "更新处理工作状态", "fileNameError": "上传的文件名称只能是英文、数字、下划线", "eusure": "确认创建"}, "joberror": {"errorTitle": "修正实验数据任务配置", "errorTip": "之前指定的序列的数目和数据文件里序列的数目不符合"}, "runAdditionalOptions": {"excludedRun": "错误 序列", "restingRun": "静息态 序列"}, "fcJobStatus": {"30": "失败", "40": "成功"}, "jobStatus": {"0": "已创建", "5": "已提交", "10": "在工作队列中", "20": "运行中", "30": "处理失败", "40": "处理完成", "50": "待确认", "400": "同步失败", "500": "同步待处理", "1010": "处理中", "1020": "处理完成", "1030": "处理失败", "1040": "可用于规划"}, "errorSeverity": {"0": "信息", "10": "警告", "20": "错误"}, "errorPrompt": "查看处理失败原因", "errorCode": {"opreview_rejected": "患者信息审核未通过", "empty_file": "空文件夹", "user_recon_result_missing_file": "用户上传的reconall zip缺少处理必要文件", "bad_file": "无效数据文件", "bad_orientation": "无效方向文件", "ignored_file": "排除隐藏文件", "skipframes_error": "项目设置skipframes超出功能像总帧数", "run_info_not_match_data": "序列信息文件或者EVS目录中的序列号与上传数据包中的文件命名号码不一致", "not_raw_nifti": "发现原始NIFTI数据文件, 排除其他数据文件", "job_failed": "处理工作失败", "query_failed": "获取受试者信息失败", "not_checked_download_files": "没有选择要下载的文件", "missing_file_dir": "未找到数据文件夹", "inconsistent_trs": "数据中序列的TR不一致", "bad_tr": "数据中的TR无效，TR应当在0.2秒至10秒之间", "bad_tr_json": "项目设置TR无效，TR应当在0.2秒至10秒之间", "bad_tr_match": "项目设置TR和数据中的TR不匹配", "inconsistent_dims": "不同的功能像的三维维度不一致", "bold_frames": "功能像中的总帧数少于40", "bold_totaltime": "功能像的总时长小于120秒", "internal_error": "内部错误", "job_timeout": "处理超时", "extra_anat": "结构像的数目大于1", "no_bld_runs": "没有找到功能像", "no_task_runs": "数据中未设置任务态的序列", "no_rest_runs": "数据中未设置静息态的序列", "design_value_error": "任务设计数值有误", "design_totaltime_error": "任务设计时长超出功能像总时长", "no_good_task_runs": "没有可处理的任务态数据", "no_good_tr": "项目设置TR和数据中的TR均无效", "no_found_evs": "EVS目录不存在", "not_supported_condition_encoding": "无法识别EVS文件格式，只支持编码格式为UTF-8, GB-2312及ASCII的.txt和.csv文件", "not_supported_condition_version": "EVS版本号错误，请使用最新版本的EVS模板", "not_found_run_info_in_evs": "EVS目录内容为空，请填写后重新上传", "condition_no_match_with_project": "EVS文件中的任务条件与项目中已存在的条件不符，请保持一致", "found_anonymous": "检测到敏感信息", "single_bold_size_exceeds_limit": "BOLD单个文件大小超出限制(NIFTI: 600MB, DICOM: 1500MB)，任务有可能会失败", "series_number_changed": "序列号被更改", "no_bold_file": "没有找到BOLD文件，请重新上传", "session_size_exceeds_limit": "BOLD总体文件大小超出限制(NIFTI: 800MB, DICOM: 2000MB)，任务有可能会失败", "abnormal_heatmap_value": "异常heatmap值", "using_default_target_candidate_region": "使用默认模板区域", "bad_hc_templ": "健康对照组数据缺失", "bad_age_gender": "患者年龄和性别信息缺失或错误", "wrong_bold_res": "BOLD分辨率超出限制，应大于1毫米小于5毫米", "multi_nii_in_one_dir": "同一文件夹下发现多个nifti文件", "unknown_file_format": "发现未知文件格式", "wrong_directory_hierachy": "用户未按规范命名文件夹", "fail_dicom_to_nifti": "DICOM转NIfTI失败", "no_raw_t1wi": "未发现有效的t1wi数据", "candidate_target_region_was_empty": "目标靶点区域为空", "missing_task_run": "未发现您定义的任务态功能像run，请重新设置或重新上传", "missing_rest_run": "未发现您定义的静息态功能像run，请重新设置或重新上传", "bld_pixdim_mismatch": "BOLD图像无效，不同run的pixdim差异超出限制0.1mm"}, "uploadStatus": {"0": "等待上传", "10": "已上传", "20": "数据无效", "30": "数据验证完毕"}, "sideNav": {"linkOverview": "概览", "linkJob": "受试者数据", "Parcellation": "功能分区", "Laterality": {"func": "偏侧性", "bold": "BOLD分区不对称性"}, "AnatFeatures": "结构特征", "FuncFeatures": "功能特征", "AMUTMSProject": "安徽医科大学TMS", "XWHTMSProject": "宣武医院TMS", "Intermediate": "原始数据", "DataQC": "数据质量控制", "ConnectivityQC": "连接性质量控制", "TaskfMRI": "任务态功能核磁", "Task": "任务态功能核磁", "Functional": {"func": "功能", "bold": "BOLD"}, "Anatomical": "结构", "ScanInfo": "扫描数据信息", "AllFiles": "所有文件", "QC": "质量控制", "AbnormDetect": "异常指数"}, "menuTip": {"maxZoom": "最大化视图", "normalZoom": "常规化视图", "expandControl": "展开控制面板", "foldControl": "收起控制面板"}, "vizScanInfo": {"runNumber": "序列号", "label": "标签", "frames": "帧数", "dimensions": "数据维度", "time": "扫描日期", "runType": "序列类型", "unknown": "未知"}, "topTab": {"downLoadResult": "下载异常指数结果", "volumetric": "容积视图", "surface": "皮层表面视图", "surfaceNative": "皮层表面个体空间", "surfaceFS6": "皮层表面FsAverage6空间", "surfaceFS4": "皮层表面FsAverage4空间", "volumeNative": "容积个体空间", "volume1mm": "容积MNI1mm空间", "volume2mm": "容积MNI2mm空间", "bold": "BOLD扫描", "boldRaw": "原始数据", "boldRes": "配准结果", "anat": "结构扫描", "corticalThickness": "皮层厚度", "sulcalDepth": "沟回深度", "myelin": "皮层髓鞘", "rawInput": "原始输入数据", "snr": {"func": "BOLD扫描信噪比", "bold": "BOLD扫描信噪比"}, "allMeanSnr": "所有序列平均信噪比 : ", "meanSnr": "平均信噪比 : ", "mc": "运动校正参数", "connMat": {"func": "功能连接矩阵", "bold": "BOLD相关矩阵"}, "funcFeat": "功能特征", "anatFeat": "结构特征", "amuTmsProject": "安徽医科大学TMS", "xwhTmsProject": "宣武医院TMS", "taskfMRI": "任务态功能核磁", "statistics": "统计", "connectivity": "连接", "intro": "简介", "FsAverage4Surfac": "FsAverage4 模版空间功能连接", "NativeSurface": "个体空间功能连接", "FsAverage4SurfacTitle": "组水平", "totalPeopleCount": "总人数", "age": "年龄", "maleToFemale": "性别比例", "male": "男", "female": "女", "NativeSurfaceTitle": "个体", "groupLevel": "组水平", "native": "个体"}, "tableName": {"networkSize": "功能网络面积", "roiSize": "ROI面积", "roiCenterOfMass": "ROI质心", "meanCorticalThickness": "功能网络平均皮层厚度", "meanSulcalDepth": "功能网络平均沟回深度", "icv": "估计颅内容量", "amuTms": {"target": "TMS靶点坐标", "patch": "TMS区块中心点", "distance": "距离", "distanceText": " 中心点到靶点:", "targetScreenshot": "TMS靶点截图"}, "xwhTms": {"target": "TMS靶点坐标", "patch": "TMS区块中心点", "distance": "距离", "distanceText": " 中心点到靶点:", "targetScreenshot": "TMS靶点截图"}}, "tableHeader": {"corticalThickness": "皮层厚度 (mm)", "sulcalDepth": "沟回深度 (mm)", "size": "面积 (mm2)"}, "scanInfo": {"TR": "TR", "TE": "TE", "res": "像素大小", "resUnit": "毫米", "flipAngle": "翻转角度", "trteUnit": "毫秒", "flipAngleUnit": "度"}, "result": {"artifactNotAvailable": "没有可查看的处理结果，请重新运行处理", "loading": "结果加载中", "loadingClickTip": "结果加载中, 请加载完成之后操作", "Network": "分区", "Region": "分区"}, "surfaceView": {"lateralLeft": "左", "medialRight": "右", "lateral": "外", "medial": "内", "superior": "上", "inferior": "下", "anterior": "前", "posterior": "后"}, "connectivity": {"connectivity": "连接", "analyses": "数据分析", "des": {"func": "优脑银河支持基于您选择的任何种子点， 通过点击交互的方式进行功能连接分析", "bold": "优脑银河支持基于您选择的任何种子点， 通过点击交互的方式进行BOLD相关分析"}, "analysesDes": "对基于如下种子的分析可以在30秒内计算出来。结果会显示在当前的“{connectivity}“标签页:", "offlineAnalysesDes": "对其他类型种子的分析可能需要10分钟的计算时间。 请将这些种子保存为ROI， 然后转到“{connectivity}“页做功能连接分析:", "offlineAnalysesDesBold": "对其他类型种子的分析可能需要10分钟的计算时间。 请将这些种子保存为ROI， 然后转到“{connectivity}“页做BOLD相关分析:", "surface": {"anatomical": "结构皮层表面视图的顶角", "functional": {"func": "功能皮层表面视图的顶角", "bold": "BOLD皮层表面视图的顶角"}, "region": {"func": "功能皮层表面视图的分区", "bold": "BOLD皮层表面视图的分区", "anat": "结构皮层表面视图的分区"}, "abnormal": "异常指数功能皮层表面视图的顶角", "abnormalFs4": "模版空间异常指数功能皮层表面视图的顶角"}, "volume": {"anatomical": "结构容积视图的体素", "functional": {"func": "功能容积视图的体素", "bold": "容积视图的BOLD体素"}, "region": {"func": "功能容积视图的分区", "bold": "BOLD容积视图的分区", "anat": "结构容积视图的分区"}}, "task": {"des": {"func": "在任务态功能核磁可视化，你可以通过指定位置和点击的方式选择一个种子点去执行功能连接分析。", "bold": "在任务态BOLD核磁可视化，你可以通过指定位置和点击的方式选择一个种子点去执行BOLD相关分析。"}, "analysesDes": "使用下面任何种子点进行连接性分析大约需要10分钟。请选择下面的种子点类型，将种子保存为ROI，然后进入“{connectivity}”页面做连接性分析。", "analysesDesBold": "使用下面任何种子点进行相关分析大约需要10分钟。请选择下面的种子点类型，将种子保存为ROI，然后进入“{connectivity}”页面做相关分析。", "option1": "结构皮层表面视图的顶角", "option2": "结构容积视图的体素", "option3": {"func": "任务态功能核磁个体空间皮层表面视图的顶角", "bold": "任务态BOLD核磁个体空间皮层表面视图的顶角"}, "option4": {"func": "任务态功能核磁个体空间容积视图的体素", "bold": "任务态BOLD核磁个体空间容积视图的体素"}, "option5": {"func": "任务态功能核磁个体空间皮层表面视图的分区", "bold": "任务态BOLD核磁个体空间皮层表面视图的分区"}, "option6": {"func": "任务态功能核磁个体空间容积视图的分区", "bold": "任务态BOLD核磁个体空间容积视图的分区"}, "option7": {"func": "任务态功能核磁FsAverage6皮层表面视图的顶角", "bold": "任务态BOLD核磁FsAverage6皮层表面视图的顶角"}, "option8": {"func": "任务态功能核磁MNI1mm容积视图的体素", "bold": "任务态BOLD核磁MNI1mm容积视图的体素"}, "option9": {"func": "任务态功能核磁FsAverage6皮层表面视图的分区", "bold": "任务态BOLD核磁FsAverage6皮层表面视图的分区"}, "option10": {"func": "任务态功能核磁MNI1mm容积视图的分区", "bold": "任务态BOLD核磁MNI1mm容积视图的分区"}}}, "control": {"control": "控件", "seeds": "最近查看的种子", "viewControl": "视图控制器", "anatomicalTitle": "结构 - 皮层表面视图控制器", "title": {"func": "功能 - 皮层表面视图控制器", "bold": "BOLD - 皮层表面视图控制器"}, "taskTitle": "任务 - 皮层表面视图控制器", "volumeTitle": {"func": "功能 - 容积视图控制器", "bold": "BOLD - 容积视图控制器"}, "anatVolumeTitle": "结构 - 容积视图控制器", "taskVolumeTitle": "任务 - 容积视图控制器", "functionalConnectivityAnalysis": {"func": "功能数据分析", "bold": "BOLD数据分析"}, "startAutoPlay": "开始自动播放", "stopAutoPlay": "暂停自动播放", "volumeView": "视图", "colormap": "颜色图样式", "colormapDefault": "默认", "colormapSpectral": "光谱", "colromapThermal": "热度", "nativeSpace": "个体空间", "1mmSpace": "MNI152 1mm空间", "native": "个体空间", "group": "模板空间", "1mm": "MNI1mm空间", "sliceShowAll": "展示全部", "slicesNumber": "视图数量", "slicesTitle": {"coronal": "冠状位视图", "axial": "轴状位视图", "saggital": "矢状位视图"}, "surface": {"viewChange": {"autoRotate": "自动旋转视图", "grid": "网格"}, "pointModal": {"visualization": "可视化", "putAway": "收起", "putUp": "展开", "title": "指针模式", "titleRegion": "指定分区间的功能连接", "surfRASXYZCoordinates": "<PERSON>, Y, Z 坐标 (surfRAS):", "volRASXYZCoordinates": "X, Y, Z 坐标 (volRAS):", "shortSurfRASXYZCoordinates": "<PERSON>, Y, Z 坐标 (surfRAS):", "shortVolRASXYZCoordinates": "X, Y, Z 坐标 (volRAS):", "VertexNumber": "顶角编号:", "corticalThickness": "皮层厚度:", "zvalue": "Z值:", "clusterID": "团块", "clusterIDLabel": "团块:", "cluster": "团块", "sulcalDepth": "沟回深度:", "parcelLabel": "结构分区:", "connectParcelLabel": "结构分区:", "connectFineParcelLabel": "细粒度结构分区:", "connectParcelLabelTip": "", "connectFineParcelLabelTip": "", "parcelLabelTip": "", "abnormPatchLabelTip": "功能分区 (18)", "abnormPatchLabel": "功能分区 (18):", "networkLabel": {"func": "功能分区:", "bold": "BOLD分区:"}, "networkLabelTip": {"func": "", "bold": ""}, "regionLabel": {"func": "功能区域:", "bold": "BOLD分区:"}, "regionLabelTip": {"func": "", "bold": ""}, "parcelAnatDetailLabel": "结构分区:", "parcelAnatDetailLabelTip": "", "taskAnatDetailLabel": "结构分区:", "taskAnatDetailLabelTip": "", "patchAnatDetailLabel": "结构分区:", "patchAnatDetailLabelTip": "", "networkAnatDetailLabel": "结构详情:", "networkAnatDetailLabelTip": "选中分区的结构详情", "regionAnatDetailLabel": "结构详情:", "regionAnatDetailLabelTip": "选中区域的结构详情", "myelinScore": "皮层髓鞘:", "anatomicalDetailsforCluster": "团块详情", "anatomicalDetailsforNetwork": "分区详情", "anatomicalDetailsforRegion": "区域详情", "anatomicalPatchNameNetWork": "选中分区的结构详情:", "anatomicalPatchNameRegion": "选中区域的结构详情:", "anatomicalPatchName": "结构分区:", "functionalConnectivityfor": {"func": "查看功能连接:", "bold": "查看BOLD相关:"}, "vertex": "顶角", "region": "分区", "network": "分区", "regionId": "分区ID", "navigate": "导航", "selectVertexandRegion": "选择顶点和区域", "selectVoxelAndRegion": "选择体素和区域", "regionText": "长按鼠标旋转查看视图,点击“{tip}”以查看详细信息", "nativeT1": "个体T1空间坐标(volRAS):", "mni1mm": "MNI1mm坐标空间(volRAS)", "mni1mmShort": "MNI1mm坐标空间(volRAS):", "voxel": "体素坐标", "voxelNumberLabel": "体素坐标(T1空间):", "voxelNumber": "体素坐标(T1空间)", "voxelNumberShort": "体素坐标(T1空间):", "voxelNumber1mm": "体素坐标(MNI1mm空间)", "voxelNumber1mmShort": "体素坐标(MNI1mm空间):", "patchId": "分区ID", "patchName": "分区名称", "showSlices": "显示多层视图", "coronal": "冠状位", "axial": "轴状位", "saggital": "矢状位", "anatomicalatlas": "结果模板", "proportion": "比例", "clusterId": "团块ID", "clusterIdLabel": "团块:", "showThresholdRange": "信号显示的阈值范围", "thresholdRange": "阈值范围", "selectConditions": "选择条件对比", "selectSpace": "选择空间", "atlas": "模板", "taskSaveSelectedSeed": "保存种子点", "regionHighlightSelected": "高亮选择分区", "networkHighlightSelected": "高亮选择分区", "parcelHighlightSelected": "高亮选择分区", "colorBar": "颜色图", "snapshot": "4图快照", "4waysnapshot": "4图快照", "8waysnapshot": "8图快照", "nativeFs4": "个体FS4", "groupFs4": "组FS4", "selectSnapshot": "查看快照", "emptyVertet": "请选择顶点和区域", "patchCorticalThickness": "皮层厚度(mm):", "patchCorticalThicknessTip": "皮层厚度(mm):", "patchSulcalDepth": "沟回深度(mm):", "patchSulcalDepthTip": "沟回深度(mm):", "patchCorticalMyelin": "皮层髓鞘:", "patchAvgCorticalThickness": "分区平均厚度:", "patchAvgCorticalThicknessTip": "分区平均皮层厚度(mm)", "patchAvgSulcalDepth": "分区平均深度:", "patchAvgSulcalDepthTip": "分区平均沟回深度(mm)", "patchAvgCorticalMyelin": "分区平均髓鞘:", "patchAvgCorticalMyelinTip": "分区平均皮层髓鞘", "patchAnatDetails": "结构分区:", "parcelAvgCorticalThickness": "分区平均厚度:", "parcelAvgCorticalThicknessTip": "分区平均皮层厚度(mm)", "parcelAvgSulcalDepth": "分区平均深度:", "parcelAvgSulcalDepthTip": "分区平均沟回深度(mm)", "parcelAvgCorticalMyelin": "分区平均髓鞘:", "parcelAvgCorticalMyelinTip": "分区平均皮层髓鞘", "parcelAnatDetails": "结构分区:", "networkAvgCorticalThickness": "分区平均厚度:", "networkAvgCorticalThicknessTip": "分区平均皮层厚度(mm)", "networkAvgSulcalDepth": "分区平均深度:", "networkAvgSulcalDepthTip": "分区平均沟回深度(mm)", "networkAvgCorticalMyelin": "分区平均髓鞘:", "networkAvgCorticalMyelinTip": "分区平均皮层髓鞘", "networkAnatDetails": "选中分区的结构详情:", "regionAvgCorticalThickness": "分区平均厚度:", "regionAvgCorticalThicknessTip": "分区平均皮层厚度(mm)", "regionAvgSulcalDepth": "分区平均深度:", "regionAvgSulcalDepthTip": "分区平均沟回深度(mm)", "regionAvgCorticalMyelin": "分区平均髓鞘:", "regionAvgCorticalMyelinTip": "分区平均皮层髓鞘", "regionAnatDetails": "选中区域的结构详情:", "tryAgainTip": "请5秒后再试", "volumeParcel": "分区体积(mm³):", "volumeParcelTip": "分区体积(mm³)", "parcelPercentICV": "分区体积比例:", "parcelPercentICVTip": "分区体积占估计颅内容量的比例", "saveVoxel": "将体素保存为种子", "saveVertex": "将顶角保存为种子", "saveRegion": "将分区保存为种子", "surfaceInputVertexError": "您输入的顶角不存在", "line": {"Line": "直线", "Select": "选择", "Measure": "测量", "Polyline": "折线", "length": "长度", "Coordinate": "坐标:", "area": "面积", "Rectangle": "多边形", "startCoordinate": "开始坐标:"}, "anatomiacalParcel": "结构分区", "connectivityVal": "功能连接数值", "Region": "分区名称", "Parcel": "分区名称", "AbnormalityScore": "异常指数:", "nativeFcValue": "个体功能连接指数", "normalPeopleFcValue": "组水平功能连接指数"}, "seeds": {"seed": "种子:", "viewConnectivity": "查看连接性", "saveSeedROI": "保存种子点", "saveSeedROIForSel": "将选中的体素或者区域保存为种子:", "scanID": "数据ID", "vertexNumber": "顶角编号", "selectedVertexNumber": "已选择顶角编号", "addSeed": "保存种子点", "addSeedSuccess": "种子点保存成功", "newSeedDes": "为了适配其他受试者，种子点会以标准空间坐标保存。坐标和顶角数会从个体皮层空间转换到FS4空间", "newVolSeedDes": "为了适配其他受试者，种子感兴趣区会标准空间坐标保存。坐标和体素坐标会从个体容积空间转换到MNI2mm空间", "newVolSeedDes1mm": "为了适配其他受试者，种子感兴趣区会标准空间坐标保存。当前团块ID对应的分区将从MNI152 1mm空间转换到MNI2mm空间", "newVolSeedDesCluster": "为了适配其他受试者，种子感兴趣区会标准空间坐标保存。当前团块ID对应的分区将从个体容积空间转换到MNI2mm空间", "newVolSeedDesRegion": "为了适配其他受试者，种子感兴趣区会标准空间坐标保存。当前区域ID对应的分区将从个体容积空间转换到MNI2mm空间", "description": " 描述", "name": "名称", "namePlaceholder": "请填写种子点名称", "nameEmpty": "名称不能为空", "nameError": "仅允许输入字母、数字、 -、_、@、.、,、:、中文及空格", "radius": "半径", "radiusPlaceholder": "0-100", "radiusEmpty": "半径不能为空", "radiusError": "仅允许输入0-100数字", "desPlaceholder": "请填写种子点描述", "resolution": "分区个数", "resolutionNo": "分区编号", "ROI": "分区", "space": {"VertexFS4": "FS4 皮层", "VertexFS6": "FS6 皮层", "VertexNative": "个体皮层", "VoxelMNI1mm": "MNI1mm 容积", "VoxelMNI2mm": "MNI2mm 容积", "VoxelNative": "个体容积", "ROI": "感兴趣区", "Anat": "感兴趣区", "TaskCluster": "任务态团块"}, "type": {"Vertex": "顶角", "Voxel": "体素", "Region": "区域", "anatRegion": "结构分区", "funcRegion": "功能分区"}, "func": {"func": "功能分区", "bold": "BOLD分区"}}, "countDown": {"countDownDes": "请不要离开当前页面，请耐心等待处理结果", "countDownFailedDes": {"func": "功能连接处理失败", "bold": "BOLD相关处理失败"}, "taskCountDownDes": "请不要离开当前页面，请耐心等待处理结果", "taskCountDownFailedDes": "计算失败"}, "task": {"surfacefs6": "Free Surfer FsAverage6 表面空间", "surfaceNative": "Free Surfer 个体空间", "volumTemplate": "MNI152  1mm 空间"}}}, "statistics": {"tabs": {"metricsVolume": "指标(容积)", "metricsVolumeLH": "指标(左脑容积)", "metricsVolumeRH": "指标(右脑容积)", "metrics": "指标(全脑皮层)", "metricsLH": "指标(左脑皮层)", "metricsRH": "指标(右脑皮层)", "rOIsMapROIs": "结构详情(全脑皮层)", "rOIsMapROIsLH": "结构详情(左脑皮层)", "rOIsMapROIsRH": "结构详情(右脑皮层)", "rOIsMapROIsVolume": "结构详情(容积)", "rOIsMapROIsVolumeLH": "结构详情(左脑容积)", "rOIsMapROIsVolumeRH": "结构详情(右脑容积)", "aparcWB": "aparc(全脑)", "aparcLH": "aparc(左脑)", "aparcRH": "aparc(右脑)", "a2009sWB": "a2009s(全脑)", "a2009sLH": "a2009s(左脑)", "a2009sRH": "a2009s(右脑)", "aseg": "aseg"}, "table": {"taskClusterNo": "团块", "cerebralCortexArea": "大脑皮层面积", "network_mean_cortical_thickness": "皮层厚度平均值", "network_mean_sulcal_depth": "沟回深度平均值", "meanMyelinScore": "皮层髓鞘平均值", "minMyelinScore": "皮层髓鞘最小值", "maxMyelinScore": "皮层髓鞘最大值", "center": "质心", "anatomicalPatchName": "结构分区", "percent": "占比 (%)", "std_myelin_score": "皮层髓鞘标准差"}, "selectresolution": "选择分区个数"}, "viz": {"anatStatistics": {"from": "从", "to": "至", "anatomical_atlas": "结构模板", "anat_patch_name": "结构分区", "surf_area": "大脑皮层面积", "surf_area_percent": "大脑皮层占比", "grayvol": "灰质体积", "grayvol_percent": "灰质体积占比", "surf_mass_center": "大脑皮层质心", "mean_cortical_thickness": "皮层厚度平均值", "max_cortical_thickness": "皮层厚度最大值", "min_cortical_thickness": "皮层厚度最小值", "std_cortical_thickness": "皮层厚度标准差", "mean_sulcal_depth": "沟回深度平均值", "max_sulcal_depth": "沟回深度最大值", "min_sulcal_depth": "沟回深度最小值", "std_sulcal_depth": "沟回深度标准差", "mean_myelin_score": "皮层髓鞘平均值", "min_myelin_score": "皮层髓鞘最小值", "max_myelin_score": "皮层髓鞘最大值", "std_myelin_score": "皮层髓鞘标准差", "volume": "体积", "volume_percent": "体积占比", "tipMessage": "结束值不能小于开始值", "No": "分区", "patchName": "结构分区", "percentage": "占比", "groupFirstValue": "平均值（mm）", "groupFirstName": "分组名称", "groupSecondValue": "平均值（mm）", "groupSecondName": "分组名称", "p": "未校正P值", "method": "假设检验方法", "atlas": "模板", "parcelName": "分区名称", "metric": "指标", "mask": "区域", "BonferroniCorrectedPValue": "Bonferroni校正P", "FdrCorrectedPValue": "FDR校正P值"}, "taskStatistics": {"clusterVolume": "团块体积 (mm³)", "maxZValue": "最大Z值", "maxZValueVertexIndex": "最大Z值个体空间顶角", "meanZValue": "平均Z值", "peakZValueNativeCoor": "最大Z值个体空间坐标", "NVoxels": "体素个数", "noVertice": "顶角个数", "SpaceTypeMNI1mm": "MNI 1mm", "SpaceTypeNative": "个体"}, "viewer": {"download": "下载", "vertex": "顶角", "clickInstuct": "Shift点击皮层表面获取信息", "view": "视图", "surface": "皮层表面", "volume": "容积", "overlay": "分区个数", "fileContent": "文本文件", "Acc": "前扣带回", "Pcc": "后扣带回", "Motor": "运动区", "VMPFC": "腹内侧前额叶皮层", "comboCondition": "选择组合条件", "Threshold": "阈值", "BeginThreshold": "最小", "EndThreshold": "最大", "BeginPValue": "最小", "EndPValue": "最大", "HidePos": "隐藏正值", "HideNeg": "隐藏负值", "MsgBeginThreshold": "请输入最小阈值!", "MsgEndThreshold": "请输入最大阈值!", "MsgBeginPValue": "请输入最小P值!", "MsgEndPValue": "请输入最大P值!", "MsgIllegal": "值不合法"}, "run": "序列", "contrast": "条件对比", "T1": "T1 个体空间", "FS2mm": "FS2mm 空间", "MNI2mm": "MNI2mm 空间"}, "surfaceViewerValue": {"value": "值", "SurfParc": "分区ID", "SurfParc92": "分区ID", "SurfParc152": "分区ID", "SurfParc213": "分区ID", "CorticalThickness": "皮层厚度", "SulcalDepth": "沟回深度"}, "arti": {"WBPialSurf": "内膜表面", "LHPialSurf": "左脑内膜表面", "RHPialSurf": "右脑内膜表面", "WBWhiteMatterSurf": "白质表面", "LHWhiteMatterSurf": "左脑白质表面", "RHWhiteMatterSurf": "右脑白质表面", "LHInflatedSurf": "左脑膨胀表面", "RHInflatedSurf": "右脑膨胀表面", "SurfParc": "功能分区", "SurfParc18": "18", "SurfParc92": "92", "SurfParc152": "152", "SurfParc213": "213", "SurfParcHighRes": "高清功能分区", "CorticalThickness": "皮层厚度", "SulcalDepth": "沟回深度", "Volume": "容积", "VolParc": "功能分区", "RawAnat": "原始结构信息", "RawBold": "原始血氧信息", "ImgLateralityLanguage": "语言偏侧性", "ImgLateralityMemory": "记忆偏侧性", "ImgAnatInput": "结构数据 序列", "ImgAnatT2Input": "结构数据 t2w 序列", "ImgBoldInput": "功能数据 序列", "ImgBoldInputSubmission": "序列", "ImgAllBold": "总帧数", "ImgAllBoldCurrent": "当前帧数", "ImgMcDisp": "运动校正 - 位移", "ImgMcRotation": "运动校正 - 旋转", "ImgMcTrans": "运动校正 - 相移", "ImgSnr": "BOLD扫描信噪比", "ImgMeanSnr": "功能数据切片平均信噪比", "JsonMeanSnr": "平均信噪比", "ImgLhAccSurf": "种子相关分析：左脑前扣带回种子 - 皮层视图", "ImgPTWBMotorVol": "右脑运动区种子点", "ImgNTWBMotorVol": "左脑运动区种子点", "ImgRhAccSurf": "种子相关分析：右脑前扣带回种子 - 皮层视图", "ImgLhPccSurf": "种子相关分析：左脑后扣带回种子 - 皮层视图", "ImgRhPccSurf": "种子相关分析：右脑后扣带回种子 - 皮层视图", "ImgLhMotorSurf": "种子相关分析：左脑运动区种子 - 皮层视图", "ImgRhMotorSurf": "种子相关分析：右脑运动区种子 - 皮层视图", "JsonAnatFeatures": "", "JsonFuncConnectivityMatrix": "", "JsonFuncFeatures": "", "JsonAMUTMSProject": "TMS 研究项目", "DownloadAMUTMSProject": "TMS 下载", "JsonScanInfo": "", "Laterality": "", "ImgAMUTMSProject": "", "SurfAnatAparcAseg": "结构分区", "SurfAnatAparcA2009sAseg": "细粒度结构分区", "FreeSurferFs4": "FreeSurfer FsAverage4 表面空间", "FreeSurfer2mm": "FreeSurfer 非线性2mm空间", "JsonGroupResult": "组内分析", "JsonGroupCompare": "组间分析"}, "gender": {"1": "男", "2": "女", "3": "其他"}, "handedness": {"1": "右手", "-1": "左手", "0": "双手"}, "maritalStatus": {"1": "单身", "2": "在关系中", "3": "已婚", "4": "离异", "5": "丧偶"}, "processingType": {"0": "数据上传", "1": "结构扫描", "2": "功能扫描", "3": "分区处理", "5": "偏侧性"}, "scans": {"scanType": "扫描类型", "noDataIndicator": "受试者目前没有扫描数据"}, "scanType": {"0": "结构扫描", "1": "功能扫描"}, "sliceImage": {"total": "视图总数:", "start": "起始位置", "finish": "结束位置", "and": "显示", "end": "张视图.", "confirm": "确定", "eachRow": "每行显示", "totalLabel": "数量:"}}, "batchUpload": {"projects": {"headerTitle": "选择项目以上传扫描数据", "onceUpload": "单次上传", "uploadMore": "批量上传"}, "subjects": {"headerTitle": "选择受试者以上传扫描数据"}, "scanData": {"headerTitle": "{ projectname } 上传包装文件", "desOne": "包装文件提供批量上传所需要的必要信息，它应该是TEXT文件(通常情况下被保存为.csv格式)，如果您要创建使用这个文件，我们建议您{ format }，使用Excel编辑后另存为.csv文件上传。", "desformat": "下载包装文件模板", "downloadTemplate": "下载文件模板 V{version}", "uploadTip": "上传填写完成的包装文件 点击区域选择文件"}, "validate": {"onlyOneTask": "序列 信息中EVS至少有一个为任务态", "fileName": "文件名只能.csv 和 .txt", "pathExist": "路径不存在", "nofilesPath": "路径下没有文件", "subjectIDEmpty": "受试者ID 不能为空", "genderDifferenError": "相同受试者的性别请保持一致", "birthYearDifferentError": "相同受试者的出生年请保持一致", "birthYearError": "出生年1900 - 至今", "genderError": "仅允许输入: 输入男/女, male/female", "maritalStatusError": "仅允许输入: 单身/在关系中/已婚/离异/丧偶/single/married/divorced/inrelationship/windowed", "handednessError": "仅允许输入: 双手/左手/右手/ambidextrous/left/right", "handednessScoreError": "仅允许输入: -100～+100以内的整数", "mRIScanDateError": "扫描日期仅允许输入1990.1.1到今日的日期", "ageAtScanError": "扫描数据的年份-出生年(正负1)", "runInfoEmpty": "任务态序列信息不能为空", "runinfoCondition": "任务态下任务条件 不能为空", "conditionNameError": "任务条件与项目设置中的任务条件不符", "envelopeFileVersion": "请检查 EnvelopeFile 中版本号是否正确, 格式是否正确", "runinfoVersion": "请检查 { path } 中版本号是否正确, 文件格式是否正确", "conditionVersion": "请检查 { path } 中版本号是否正确, 文件格式是否正确", "checkVersionError": "请检查文件中的以下错误", "readingFileFailed": "读取 {path} 失败", "fileNotFound": "{path} 目录下没有找到 .csv .txt 文件", "evsNotFound": "第{num}行中找不到EVS", "evsSameRunNumber": "第 {num} 行中 EVS必须和 序号相同", "evsEmpty": "{path} 未找到evs文件夹", "onlyOneResting": "序列 信息中EVS至少有一个为静息态", "lineEmpty": "第 {num} 行任务条件为空", "isEmpty": "为空", "fileEmpty": "路径不能为空", "samePath": "路径不能相同", "fileCondingError": "读取失败, 文件为不支持的编码格式", "runNumberError": "第 {num} 行中序列号必须由三位数字组成", "uploadButtonTip": "上传成功后，请至此项目详情页刷新以查看最新上传数据"}, "showTable": {"note": "备注", "title": "包装文件检查结果展示", "allUploadData": "本次上传总数: { num }", "noErrorUploadData": "检查正确数量: { num }", "errorUploadData": "检查错误数量: { num } (请更正错误信息,然后重新上传.)", "clearFilters": "清空过滤器", "errorContent": "错误内容 : ", "mRIScanData": "核磁共振成像扫描文件路径", "ageAtScan": "扫描时年龄", "mRIScanDate": "核磁共振成像扫描日期", "runInfo": "序列配置信息文件", "assessmentData": "评估量表数据文件路径", "reUpload": "重新上传文件", "index": "序号", "startUpload": "开始上传", "leavePageTitle": "确认要中断批量上传吗？", "leavePageContent": "若中断批量上传流程，将抹除所有已经选择/填写的内容。", "runInfoTable": {"title": "序列配置信息", "runNumber": "序列号", "condition": "条件文件"}, "conditionTable": {"condition": "条件", "startTime": "开始时间 (秒)", "durationTime": "持续时间"}}, "modal": {"chooseUploadType": "选择上传类型", "chooseDownloadType": "选择下载类型"}}, "singleUpload": {"runInfoErrorTitle": "EVS文件检查结果展示", "evsFileErrorTip": "EVS 文件有误, 请重新上传", "filePathEmpty": "请选择文件", "runinfoCondition": "任务条件不能为空", "runinfoConditionCantEmpty": "Run信息文件不能为空", "restingDownloadTemplateDescript": "序列信息文件可指定不同的序列应如何处理，应将其作为静息态处理或跳过不处理", "taskDownloadTemplateDescriptOne": "序列信息文件可指定不同的序列应如何处理，应将其作为静息态处理、任务态处理 、甚至跳过不处理", "taskDownloadTemplateDescriptTwo": "在任务态项目中，应为每次运行指定条件、开始时间、每个条件的持续时间", "noTasks": "项目设置中没有任务", "radioButton": "填写任务态配置信息"}, "createAnalysis": {"scanCompDes": "个体水平功能连接分析", "groupConnectomeDes": "组水平功能连接分析", "createBtns": {"analysis": " 结构指标分析", "groupComp": "组水平的结构指标分析", "scanComp": "功能连接分析", "IndividualConnectome": "个体功能连接分析", "groupConnectome": "组功能连接分析", "task": "任务分析", "SeedToBrain": "种子点的全脑功能连接", "Coonectome": "功能连接矩阵"}, "analysisType": {"IndividualConnectome": "个体水平功能连接矩阵分析", "IndividualSeedToBrain": "个体水平种子点的全脑功能连接", "groupConnectome": "组水平功能连接矩阵分析", "ConnectomeSeedToBrain": "组水平种子点的全脑功能连接"}, "homeTitle": "选择分析类型", "stepOneTitle": "创建组水平的结构指标分析", "stepOneTitleConnectome": "创建组水平功能连接矩阵分析", "selJobsWarning": "请选择扫描数据", "createGroupTip": "分析中将只使用处理完成的扫描数据。", "createGroupMsg1": "当前所选项目没有数据分组，你可以", "createGroupMsg2": "创建一个新的数据分组", "createGroupMsg3": "或者返回上一步选择一个其他的项目。", "groupChangeWaring": "数据分组 {tips} 中的数据已改变", "groupsLimit": "请选择10个以内数据分组。"}, "vizAnalysis": {"filter": {"selectHemisphere": "选择半球", "valueOperation": "数值选项", "downTableTip": "下载表格数据", "The1stGroup": "分组1", "The2ndGroup": "分组2", "SwitchTo": "切换至"}, "table": {"groupName": "分组名称", "groupValue": "(mm)", "metric": "指标", "mask": "区域", "atlas": "模板", "parcelName": "分区名称", "pValue": "P值", "viewChart": "查看图表", "viewMyelin": "查看皮层髓鞘", "viewMyelinTitle": "皮层髓鞘", "total": "共 {total} 条", "uncorrectedPValue": "未校正 P 值", "fdrCorrectedPValue": "FDR 校正 P 值", "bonferroniCorrectedPValue": "Bonferroni 校正 P 值"}, "method": {"title": "假设检验方法", "ttest_ind": "双总体独立样本t检验", "ranksums": "Wilcoxon秩和检验"}, "tabs": {"customize": "自定义查看分析结果", "statistical": "显著性差异分析结果", "sliderTitle": "P 值", "seeMore": "查看更多", "foldUp": "收起"}, "brain": {"operation": "选择半球", "wb": "所有半球", "lhrh": "所有半球", "lh": "左半球", "rh": "右半球", "wbHemi": "所有半球", "lhrhHemi": "所有半球", "lhHemi": "左半球", "rhHemi": "右半球"}, "group": {"controlTitle": "比较分组", "separateTitle": "分别展示"}, "metric": {"label": "指标", "corticalThickness": "皮层厚度", "sulcalDepth": "沟回深度", "corticalMyelin": "皮层髓鞘", "surfaceArea": "大脑皮层面积", "grayMatterVolume": "灰质体积", "parcelVolume": "分区体积", "corticalThicknessUnit": "皮层厚度(mm)", "sulcalDepthUnit": "沟回深度(mm)", "corticalMyelinUnit": "皮层髓鞘", "surfaceAreaUnit": "标准化皮层表面积", "grayMatterVolumeUnit": "标准化灰质体积", "parcelVolumeUnit": "标准化分区体积"}, "mask": {"label": "区域", "anat": "结构分区", "func": "功能分区", "brain": "半球"}, "atlas": {"aparc": "aparc", "a2009s": "aparc.a2009s", "aseg": "aseg", "18": "18", "92": "92", "152": "152", "213": "213", "label": "模板", "allAtlas": "全部", "selectedParcels": "选中分区 { checkedCount }"}, "percent": {"title": "添加显示", "add": "添加"}, "alert": {"switchMaskTitle": "确定要切换区域吗？", "switchMaskContent": "如果切换了区域，已选择的目标将会被清空。", "switchMetricTitle": "确定要切换指标吗？", "switchMetricContent": "如果切换了指标，已选择的目标内容将会被清空。", "inputErrMsg": "请输入有效数字"}, "chart": {"scansLabel": "数量", "downPNGTip": "下载PNG"}, "math": {"avg": "平均值", "avgNoEnd": "平均", "p25": "第25个百分位", "p75": "第75个百分位", "max": "最大值", "min": "最小值", "pattern": "第{percent}个百分位", "end": "个百分位", "median": "中位数"}, "viz": {"uncorrected": "未校正 P 值", "FDR": "FDR 校正 P 值", "Bonferroni": "Bonferroni 校正 P 值", "returnWb": "返回全脑结果", "multipleComparisonsCorrection": "多重检验校正"}}, "dataAnalysis": {"home": {"title": "数据分析", "newAnalysis": "新建数据分析", "existingAnalyses": "现有分析", "seedROIs": "种子点", "scans": "扫描数据"}, "common": {"analysisName": "数据分析名称", "project": "项目", "projectPlaceHolder": "选择一个项目", "projectName": "项目名称", "projectReq": "请选择项目", "des": "描述", "desPlaceholder": "输入分析描述", "create": "创建人", "space": "空间", "type": "类型", "status": "状态", "start": "开始时间", "finish": "完成时间", "action": "操作", "viewResultTip": "查看分析结果", "groupsDataTip": "显示组的数据信息", "errorTip": "查看警告及错误信息", "delWarning": "请选择数据分析", "selectGroup": "选择数据分组", "analysisType": "分析类型"}, "actions": {"save": "保存", "delete": "删除", "new": "新建", "next": "下一步", "previous": "上一步", "back": "返回", "cancel": "取消", "clear": "清空过滤器", "confirm": "确定", "runAnalysis": "运行分析", "confirmDel": "确认删除"}, "title": {"connectivity": {"func": "创建个体水平种子点的全脑功能连接", "bold": "创建BOLD相关数据分析"}, "IndividualConnectome": "创建个体水平功能连接矩阵分析", "GroupConnectome": "创建组水平种子点的全脑功能连接分析", "exsiting": "现有数据分析", "exception": "异常信息", "delAnalysis": "删除数据分析", "seed": {"func": "功能连接种子点", "bold": "BOLD相关种子点"}, "newSeed": "新建种子点", "editSeed": "编辑种子点", "newAnalysis": "新建数据分析"}, "vizTab": {"seed": "种子点"}, "exception": {"scanId": "数据ID", "seedName": "种子点名称", "severity": "严重性", "path": "文件路径", "msg": "错误信息"}, "delete": {"sure": "确定要删除这些种子点吗？", "need": "你可以删除的种子点:", "donot": "你没有权限删除下面的种子点，它们将不会被删除:", "sureAnalysis": "确定要删除这些数据分析吗？", "needAnalysis": "你可以删除的数据分析:", "donotAnalysis": "你没有权限删除下面的数据分析，它们将不会被删除:"}, "connectivity": {"name": "名称", "namePlaceHolder": "输入分析名称", "analysisName": "名称", "nameReq": "请输入名称", "nameLen": "输入字符超过限制，仅允许输入20个字符", "nameError": "仅允许输入字母、数字、 -、_、@、.、,、:、中文及空格", "selectData": "选择扫描数据", "selectGroup": "选择分组", "selectROIs": "选择种子点", "org": "机构", "empty": "数据分析为空, 点击按钮创建", "goBackDes": "返回{analysisName}分析详情页 ?", "viewData": "查看数据 ", "viewSeed": "查看种子 ", "dataUsedInAnalysis": "参与分析的数据 ", "deleteMsg": "此数据不存在已于{time}被删除", "nameRepeat": "分析名称不能重复"}, "seed": {"name": "名称", "nameReq": "请输入种子点名称!", "location": "种子点位置", "new": "新建种子点", "type": "类型", "typeReq": "请选择种子点类型!", "radius": "半径(mm)", "radiusReq": "请输入0-100范围半径(mm)!", "vertex": "顶角", "vertexReq": "请输入顶角!", "vertexFormat": "顶角格式错误!", "mni": "MNI2mm 坐标", "mniReq": "请输入 MNI2mm 坐标!", "mniFormat": "MNI2mm 坐标格式错误!", "addMore": "添加更多种子点", "empty": "种子点数据为空, 点击按钮创建", "desPlaceholder": "这是此分析的描述", "space": "空间", "seedType": "种子类型", "fs4Location": "FS4 位置", "fs6Location": "FS6 位置", "createAt": "创建时间", "createBy": "创建人", "mni2": "MNI2mm", "mni1": "MNI1mm", "des": "描述", "seedInfo": "种子信息", "atlas": "模板", "atlasReq": "选择模板", "parcelName": "分区名称", "parcelNameReq": "选择分区", "Network": "分区", "resolution": "分区个数", "NativeVertex": "个体顶角", "FS4Vertex": "FS4 顶角", "NativeVoxel": "个体体素", "MNI2mmVoxel": "MNI2mm 体素", "FS6Vertex": "FS6 顶角", "MNI1mmVoxel": "MNI1mm 体素", "FunctionalRegion": "功能分区", "AnatomicalParcel": "结构分区", "ViewSeedInfo": "查看种子", "nativeSurface": "皮层表面FsAverage4空间", "mni2mm": "容积MNI2mm空间", "groupDifferent": "组间差异", "scanGroupResult": "数据分组结果展示", "pleaseInputDesc": "请输入种子点描述!"}, "scanVisualizationInfo": {"des": "描述: ", "createdBy": "创建者: ", "startTime": "开始时间: ", "finishTime": "结束时间: ", "status": "状态: "}}, "httpError": {"networkError": "服务器响应错误,请稍后再试", "SV11003": "受试者ID已存在, 请选择一个新的ID"}, "install": {"linux": {"1": "支持的Linux操作系统：", "2": "Linux安装使用方法", "3": "安装wget", "4": "(若已安装过，请忽略此步骤)", "5": "检验是否安装过wget方法：", "6": "打开终端输入以下内容：", "7": "进入安装目录", "7explain": "(建议安装在桌面，安装后会在桌面展示产品入口，桌面的目录通常是 $HOME/桌面)", "8": "[安装目录]", "9": "运行Neural Galaxy 桌面端", "10": "在Linux上运行-根据安装位置不同，有2种运行方式，请根据情况操作：", "11": "在桌面点击 NeuralGalaxy 图标(仅限安装目录在桌面的情况下)", "12": "在Linux上卸载", "13": "在Linux上安装本产品共需3步，请先确保硬件条件符合要求后，区分情况按照如下步骤进行操作：", "14": "安装Neural Galaxy 桌面端"}}, "control": {"zoom": "缩放", "parcBorder": "查看功能分区边界", "setWH": "调节对比度"}, "organization": {"organizationTitle": "机构信息", "organizationName": "机构名称", "licenseAndUsage": "许可和使用", "robot": "优点机器人", "workSpace": "空间", "licenseTemplates": "许可模板", "name": "名称", "pointName": "用户名", "uniqueId": "唯一标识", "productOfNum": "产品数量", "role": "角色", "createDate": "创建时间", "status": "状态", "advisorsStatus": "顾问状态", "pointAdvisorsStatus": "技术支持状态", "numberOfadmin": "管理员数量", "numberOfUsers": "用户数量", "numberOfProjests": "项目数量", "numberOfSubjects": "受试者数量", "numberOfScans": "扫描数据数量", "numberOfDataGroups": "组数量", "numberOfPlans": "规划数量", "numberOfProduct": "产品数量", "lastLogin": "最近登录", "valueActive": "激活", "valueInactive": "未激活", "projects": "项目", "projectRole": "项目角色", "projectStatus": "项目状态", "newProject": "创建项目", "user": "用户", "lastLoginIn": "最近一次登录", "action": "操作", "email": "邮件", "endpoint": "磁共振影像数据处理软件", "pointEndpoint": "硬件列表", "dicom": "DICOM 节点管理", "projectInfo": "该机构共有 { total } 个项目,可以看到 { show }个项目.", "deleteMsg": "{name}机构已成功删除。", "point": {"userCount": "用户数", "patientCount": "患者数", "uploadIntakeCountTotal": "数据处理总次数", "uploadIntakeCountMonth": "当月数据次数", "treamentCountTotal": "治疗总次数", "treamentCountMonth": "当月治疗次数", "robotCount": "机器人设备数"}, "robotList": {"id": "机器人ID", "endpointId": "机器人编号", "type": "机器人型号", "registTime": "注册时间", "connectCloudTime": "最近于云端通讯时间"}, "deviceList": {"id": "设备ID", "endpointId": "设备编号", "type": "设备型号", "registTime": "注册时间", "connectCloudTime": "最近于云端通讯时间"}, "table": {"orgMembers": "机构成员", "projectAdmins": "项目管理员", "ngAdmins": "系统管理员", "noProject": "不属于任何项目", "advisors": "顾问", "pointAdvisors": "技术支持", "ngAdvisors": "系统顾问", "pointNgAdvisors": "系统技术支持", "delete": "删除", "projectName": "项目名称", "projectRole": "项目角色", "inviteUser": "邀请用户", "reViewer": "审核员"}, "form": {"nameFieldName": "名称", "namePlaceHolder": "机构名称", "contactEmailFieldName": "联系电子邮件", "contactEmailPlaceHolder": "机构联系电子邮件", "isActiveFieldName": "状态", "orgType": "机构类型", "enableExportPDF": "允许生成图文报告", "clinicalInstitutions": "临床机构", "scientificResearchInstitution": "科研机构", "isReview": "审核治疗方案", "valueActive": "激活", "valueInactive": "未激活", "nameRequired": "机构名称不能为空", "uniqueIdRequired": "唯一标识不能为空", "nameFactor": "输入字符超过限制，仅允许输入20个字符", "nameError": "输入内容不符合规范，仅允许输入数字，英文 ，中文，_， - ", "contactEmailRequired": "机构联系电子邮件不能为空", "validContactEmail": "电子邮件格式无效", "createDate": "创建时间", "alreadyMsg": "该名称已存在，请重新输入。", "uniqueIdAlreadyMsg": "唯一标识已存在，请重新输入。", "uniqueIdError": "唯一标识不正确,3-20数字，英文 ，_， -。"}, "viewInfo": "查看详情"}, "product": {"productTitle": "产品 {value}", "product": "产品"}, "advisors": {"modalTitle": "删除顾问", "editTitle": "管理顾问", "pointEditTitle": "管理技术支持", "pointReView": "管理审核员", "delete": "删除", "ensure": "确定", "cancel": "取消", "name": "用户名", "email": "邮箱", "status": "状态", "lastLogin": "最近登录", "projectName": "项目名称", "projectStatus": "项目状态"}, "inviteUsers": {"title": "邀请用户", "organization": "机构", "project": "项目", "userEmails": "用户邮箱", "inviteRole": "邀请角色", "inputPrompt": "输入电子邮件列表， 用逗号(, ) / 空格() / 分号(;) / 或者竖线( | ) 分开,最多50个电子邮件。 ", "organizationAdmin": "机构管理员", "editInfo": "编辑信息", "therapist": "治疗师", "afterSalesMaintenance": "技术支持", "resetPwd": "重置密码", "updatePwd": "修改密码", "resetPwdMsg": "是否确定重置密码?", "projectAdmin": "项目管理员", "projectMember": "项目成员", "ngReviewer": "审核员", "user": "用户", "advisors": "顾问", "ngAdmin": "系统管理员", "back": "返回", "invite": "邀请", "moreThanMaxMessage": "电子邮件过多", "onEmails": "没有正确的邮件", "userMustHaveProject": "普通用户必须是至少一个项目的成员", "errorEmailMessage": "邮箱信息存在错误，请修改。", "asyncValidationFailMessage": "邮箱已被邀请过，无法再次邀请 : { emails } ", "inviteLicenseMsg": "还可以邀请用户数量为 : {numberRemaining}  （每年总计 {total} 个) ", "modal": {"title": "用户信息与密码", "projectName": "项目名称", "clipboard": "密码已复制到剪切板", "role": "角色", "userName": "用户名", "password": "密码", "copyPassword": "复制用户名及密码"}}, "projectInfo": {"title": "项目信息", "organization": "机构", "createDate": "创建时间", "createBy": "创建人", "type": "项目类型", "status": "项目状态", "scanTR": "扫描重复时间 (TR)", "scanTRTip": "根据扫描实际参数设定 (秒)", "skip": "移除起始帧数", "skipTip": "受扫描起始状态磁场不稳定性的影响，前几帧影像数据可能存在噪声，可选择移除前几帧数据", "sync": "项目信息同步", "syncStrategy": {"off": "关", "on": "开", "boxToCloud": "从Box到Cloud", "onlyProject": "仅限项目信息（扫描数据不同步）", "twoWay": "在Cloud和Box之间"}, "subjectsMenu": "受试者", "scansMenu": "扫描数据", "dataGroupsMenu": "数据分组", "settingMenu": "项目设置", "userMenu": "用户", "name": "名称", "nameRequired": "项目名称不能为空", "nameRule": "可输入20位数字，英文 ，中文，_ ，-，空格", "timeofRepetitionRequired": "间隔时间须在 0.2-10 秒范围内", "skipRequired": "移除起始帧数必须为非负整数，最大长度为10", "resting": "静息态", "task": "任务态", "isActive": "状态", "valueActive": "激活", "need": "是", "notNeed": "否", "valueInactive": "未激活", "subjects": {"total": "受试者数量 : { total }", "subjectId": "受试者ID", "birthDay": "出生年", "sanNumber": "扫描数据数量", "handedness": "利手性"}, "scans": {"total": "扫描数据数量 : {total} ", "scanId": "数据ID", "fileName": "文件名称", "dataGroups": "数据分组", "createDataGroups": "新建数据分组", "clickButtonCreateDataGroup": "当前项目下没有数据分组，点击按钮创建", "runInfo": "任务态详情", "runInfoTips": "查看任务态详情", "subject": "受试者ID", "status": "状态", "createAt": "创建时间", "finishedAt": "完成时间", "returnDataGroup": "返回数据分组详情页", "returnAnalysis": "返回数据分析结果页面", "runInfoModal": {"titile": "序列配置信息 - {id}", "runNumber": "序列数量", "evs": "EVS"}}, "dataGroups": {"total": "数据分组数量 : {total} ", "dataGroups": "数据分组", "action": "操作", "numberOfScans": "扫描数据数量"}, "users": {"total": "总用户数 : {total} ", "name": "名称", "role": "角色", "email": "邮件", "lastLoingIn": "最近登录时间", "status": "状态"}, "setting": {"conditions": "任务条件", "newCondition": "创建任务条件", "taskDesign": "任务设计", "newTaskDesign": "创建任务设计", "startTime": "开始时间 (秒)", "durationTime": "持续时长 (秒)", "uploadedEVS": "已经上传 EVS", "sameTaskNameMsg": "该任务设计名称已存在，请重新填写。", "conditionPage": {"total": "任务条件总数: {total}", "conditionName": "条件名称", "description": "描述", "opera": "操作", "deleteTitle": "确认删除此任务条件吗？", "deleteContent": "{name}任务设计中的此条件会同时被删除。", "empty": "任务条件为空, 点击按钮创建新的任务条件."}, "taskDesignPage": {"total": "总任务设计数: {total}", "conditions": "任务条件", "startTime": "开始时间 (秒)", "durationTime": "持续时长 (秒)", "deleteContent": "点击\"确定\"删除:{name}", "empty": "可以在这里输入任务设计信息，或者通过上传EVS文件的方式直接输入。点击下面的按钮输入新的任务设计:"}}}, "ngContianer": {"overviewStatistics": "概述统计", "numOgOrganization": "机构数量", "numOfUsers": "用户数量", "numOfproject": "项目数量", "numOfSubject": "受试者数量", "numOfScans": "任务数量", "numOfDataGrounps": "组的数量", "newOrganizaiton": "创建机构", "table": {"title": "机构", "name": "名称", "createDate": "创建时间", "status": "状态", "license": "许可证", "activateProduct": " 激活产品", "usagelable": "操作", "licenseTemplateTable": {"new": "新建许可模板", "licenseTemplate": "许可模板", "description": "描述", "action": "操作", "deleteTip": "确定要删除许可模板么？"}, "usage": {"info": {"name": "授予的许可名称:", "licenseGrantedTo": "许可授予:", "effective": "许可起始时间", "expires": "许可到期日期", "purchased": "购买许可:", "expiresOn": "到期日期", "effectiveOn": "起始时间", "userBtn": "当前许可", "purchasedBtn": "待激活许可", "expiredBtn": "过期许可", "create": "创建许可证"}, "table": {"licenseItem": "项目许可", "usageStatistics": "使用情况统计", "licenseLimits": "许可限制"}, "form": {"overageRate": "超额率", "note": "备注", "item": "项目", "pricing": "价格(人民币)"}}}, "modal": {"deleteTitle": "你确定删除该机构么?", "content": "如果机构被删除，机构下的项目、受试者,数据等也会同时被删除。", "conditions": "请输入要删除的机构名称", "placeholder": "输入机构名称"}}, "special": {"info": {"title": " {name} 信息", "numOfOrgMembers": "机构成员数量", "numOfNGAdmin": "系统管理员数量", "numOfAdvisors": "顾问数量", "pointNumOfAdvisors": "技术支持数量", "crateDate": "创建时间", "lastLogin": "最近登录"}}, "license": {"empty": "当前许可信息为空，点击按钮新建许可。", "createBtn": "创建许可证", "explain": "若有疑问或者想更新许可证, ", "support": "请与我们联系", "allow": "允许", "forbidden": "不可用", "navigator": "磁共振影像数据处理软件", "boxSystem": "磁共振影像数据处理系统", "cloud": "云医学影像数据处理软件", "surgical": "手术规划", "readOnly": "许可过期，此功能禁用", "template": {"modal": {"title": "许可证模板", "name": "许可证模板名称 ", "nameMsg": "请填写许可证模板名称", "description": "描述", "web": "网页端", "desktop": "客户端", "navigator": "脑图谱仪", "navigatorIsTrue": "是否有磁共振影像数据处理软件", "endpointsAllowed": "授权终端", "scanPerProcessig": "扫描处理费", "stroagePerScanPerYesr": "单个扫描数据存储的年费", "appCn": "额外用户帐户的年费", "license": "项目", "client": "客户端", "value": "数量", "batchUpload": "批量上传", "batchDownload": "批量下载", "batchDownloadAndbatchUpload": "批量上传下载", "newWorkFileServer": "文件服务", "maxAccounts": "可创建用户数量", "scansToUploadPerYear": "每年可处理扫描数据数量", "scansToUploadPerDay": "每天可处理扫描数据数量", "scansToStorePerYear": "每年可储存扫描数据数量 ", "scansToUploadPerMonthUser": "每个用户每月可处理扫描数据数量", "overageRate": "超额费率", "nolimit": "不限制", "item": "项目", "pricing": "价格", "ensure": "确定", "cancel": "取消", "switch": "开关", "endpoint": "磁共振影像数据处理软件", "storage": "存储", "automatic": "自动下载"}}, "licenseTimeError": {"1": "许可证开始时间不能大于结束时间", "2": "许可证有效期间不能与同产品许可证有效期有交集"}, "modal": {"updateLicenseTitle": "编辑许可证", "createLicenseTitle": "创建许可证", "note": "备注", "moreProdunct": "添加更多产品", "morePermit": "添加更多许可", "fromTemplate": "从许可证模板新建", "nameOfLicense": "许可名称", "grantedTo": "授予", "effectiveOn": "起始日期", "expiresOn": "结束日期", "deleteTitle": "删除此产品许可"}}, "batch": {"upload": {"modal": {"title": "批量上传", "explain": "您还没有开通批量上传功能，若要使用此功能，请  { contact } 更新您的许可信息。", "contact": "联系我们"}}, "download": {"modal": {"title": "批量下载", "explain": "您还没有开通批量下载功能，若要使用此功能，请 { contact } 修改您的许可信息。", "contact": "联系我们"}}, "limit": {"modal": {"title": "上传限制", "reRunTitle": "处理限制", "explain": "上传数据数量超出限制，您可以继续上传，需要支付额外费用，确定继续上传吗?", "reRunExplain": "可处理扫描数据数量超过许可限制，您无法继续处理，需要支付额外费用，确定继续处理吗？", "noTime": "上传扫描数据数量超过许可限制，您无法继续上传。", "reRunNoTime": "可处理扫描数据数量超过许可限制，您无法继续处理。"}}}, "connectome": {"zValue": "Z 值", "rValue": "R 值"}, "newProject": {"conditions": {"title": "任务条件", "explain": "在任务态项目中的任务条件是必填项。可以用当前界面定义任务配置或者在上传过程中用EVS文件定义任务配置（在EVS文件中的任务条件需与项目中的任务条件保持一致)。", "buttonBefore": " 请点击下面的按钮创建任务条件:"}, "task": {"title": "任务设计", "haveTitle": "任务设计", "explain": " 如果任务配置比较简单并且项目中多数扫描数据使用同样的任务配置，你可以在这里创建任务设计。如果任务配置比较复杂或者不同扫描数据使用不同的任务配置，请使用EVS文件。", "firstExplain": "如果任务配置比较简单并且项目中多数扫描数据使用同样的任务配置，你可以在这里创建任务设计。如果任务配置比较复杂或者不同扫描数据使用不同的任务配置，请使用EVS文件。", "buttonBefore": "请点击下面的按钮创建任务条件", "errorInfo": "请检测下任务条件"}}, "inviteUser": {"modal": {"title": "邀请用户", "explain": "邀请用户数量超过限制，您可以继续邀请，需要支付额外的费用，您确定要继续邀请吗?", "limit": "邀请用户数量超过限制，请 { contact } 更新您的许可信息。 ", "contact": "联系我们", "number": "允许邀请数量 : {value}"}}, "storageAndDownload": {"helpPdf": "优脑银河数据存储下载指南", "title": "存储和下载 ", "fileStorage": "我的文件存储 ", "totalSpace": "总计空间", "used": "已使用", "available": "剩余", "help": "存储和下载指南", "description": "优脑银河为您提供数据存储空间以存储你的数据下载处理结果。您可以用挂载存储空间的形式把文件存储变成您的电脑文件系统的一部分。", "downloadDirectory": "处理结果自动下载至目录", "downloadNotify": "自动下载成功后通知 ", "newDirectory": "新建目录", "delete": "删除", "licenseMsg": "因许可限制无法查看“储存和下载”内容,若有问题请{contact}", "netMsg": "你目前连接“云”医学影像数据处理软件，“储存和下载”内容无法查看。如果你的机构购买了磁共振影像数据处理软件，请访问磁共振影像数据处理软件的网页端或者更换客户端的访问终端查看“存储和下载”的内容", "contact": "联系我们", "hiddenLabel": "显示隐藏文件", "deleteModal": {"title": "您确定要删除这些文件么？", "content": "请在输入框中输入“删除文件”。", "limitMsg": "请选择文件.", "deleteTitle": "无法删除此文件目录", "deleteContent": "当前目录被设置成结果文件自动下载目录，无法删除。", "okText": "取消"}, "chooseDirectoryModal": {"title": "选择目录"}, "newModal": {"title": "创建目录", "name": "名称", "nameRequired": "文件目录名称不能为空,并且长度不能超过20", "errorInfo": "创建文件失败，查看是否有相同文件名。", "nameRule": "请使用字母，数字，—和_创建目录，用 / 来创建子目录"}, "directory": {"name": "名称", "size": "大小", "time": "时间"}}, "endpoints": {"client": "客户端", "desktop": "桌面端", "navigator": "导航仪", "box": "磁共振影像数据处理软件", "pointBox": "脑功能分析软件", "pacs": "磁共振设备连接节点", "inActive": "未激活", "active": "激活", "banned": "禁止", "status": {"pending": "等待", "submitted": "提交", "canceled": "取消", "inProgress": "进行中", "failed": "失败", "finished": "完成"}, "table": {"uniqueId": "终端ID", "type": "类型", "ip": "IP地址", "version": "版本", "note": "注释", "status": "状态", "action": "操作"}, "edit": {"title": "修改终端", "note": "注释"}, "update": {"title": "选择版本", "select": "选择版本"}, "warn": {"title": "更新信息", "name": "名称", "version": "版本", "status": "状态", "description": "描述", "time": "时间", "reason": "原因"}}, "common": {"scansT1": "扫描 (T1)", "login": {"message": "您将登录{value}产品，登录后可以切换产品。", "research": "银河云服务", "surgical": "手  术  规  划", "dataServer": "云  医  学  影  像  数  据  处  理  软  件", "box": "磁  共  振  影  像  数  据  处  理  软  件", "point": "抑郁症优点疗法软件", "point100": "脉  冲  磁  刺  激  仪  管  控  软  件", "pointTherapy": "优 点 疗 法 云 软 件", "boxSystem": "磁  共  振  影  像  数  据  处  理  系  统", "dataServerAboutUs": "云医学影像数据处理软件", "boxAboutUs": "磁共振影像数据处理软件", "boxSystemAboutUs": "磁共振影像数据处理系统", "surgicalAboutUs": "手术规划", "version": "发布版本: {version}", "httpErrorCode": {"endpointTypeInvalid ": "非法访问", "surgeryLicenseInvalid": "没有{value}产品使用权限，请选择其他产品登录", "surgeryUserInvalid": "没有产品使用权限,请联系机构管理员", "onfoundLicense": "没有产品使用权限", "notFoundRAOrg": "该用户不属于任何机构", "authBoxLimited": "没有此产品使用权限", "licenseDenied": "该用户不能登录{value}.", "endpointTypeInvalidOfRA": "顾问不能登录手术规划产品", "accountInvalid": "账号未激活", "orgInvalid": "机构未激活", "PNT_US0008": "用户IP权限配置不能为空", "PNT_US0009": "用户IP受限，请联系系统管理员", "PNT_US0010": "用户IP权限规则错误"}}, "header": {"seeg": "手术规划", "connectedSeeg": "链接至手术规划", "research": "银河云服务", "connecteSresearch": "链接至银河云服务"}}, "surgery": {"home": {"home": "首页", "patientList": "患者列表", "homeTooltip": "首页", "subjects": "受试者", "subjectsTooltip": "受试者", "surgery": "手术规划", "surgeryTooltip": "手术规划", "seegEmpty": "项目未创建,请点击\"创建新项目\"按钮创建", "surgeEmpty": "手术规划未创建,请点击\"创建手术规划\"按钮创建"}, "createNewPlan": "无手术规划方案，点击按钮新建"}, "seeg": {"showMsg": "请输入受试者ID", "moreShow": "加载更多", "plan": {"distance": {"entry": "进入点", "target": "靶点", "vesselDisLabel": "距离最近血管(mm)", "targetDisLabel": "距离靶点(mm)", "navigate": "导航"}, "download": {"title": "导出规划结果", "exporting": "正在导出", "exportComplete": "导出完成", "failureNoExport": "规划失败无法导出", "tip": "规划结果导出中,请稍后下载", "downloadFiles": "下载已导出结果", "tipDes": "正在准备下载文件，可稍后至首页、受试者详情页、手术规划概览页、下载页，点击下载此文件", "subjectCustId": "受试者", "planType": "规划类型", "createAt": "创建时间", "status": "状态", "action": "下载", "resectionExportTitle": "选择要导出的关键功能区", "resection": "切除损毁", "sEEG": "自动规划路径", "baseTip": "导出在T1空间的规划结果", "downTip": "下载已导出的规划结果", "rerun": "结果导出失败", "rerunTip": "重新导出结果", "empty": "手术规划完成后可导出结果"}, "resection": {"riskValue": "风险指数", "riskColor": "风险系数", "anat": "结构分区", "func": "功能分区", "showFunc": "显示功能分区", "coords": "坐标", "majorFun": "关键功能风险系数", "majorFunShow": "已选中关键功能区", "langHemiLR": "语言偏侧性-双侧化", "langHemiRight": "语言偏侧性-右侧化", "langHemiLeft": "语言偏侧性-左侧化", "literature": "文献", "languageLateralizationIndex": "语言偏侧化指数", "languageDominanceHemisphereLR": "语言优势半球-双侧", "languageDominanceHemisphereR": "语言优势半球-右侧", "languageDominanceHemisphereL": "语言优势半球-左侧", "languageDominanceDes": "语言偏侧化指数范围是-1~1，大于0.1代表左侧化，小于-0.1代表右侧化，-0.1~0.1之间代表双侧化，该偏侧化指数由静息态功能磁共振呈像计算而来，该指数可能受到数据质量的影响，影响数据质量的原因包括扫描时被试者的头动、扫描数据的时长", "riskTitle": "选择点的风险系数", "allBrainTitle": "选择点的信息", "riskTitleTip": "当前选择点切除/损毁可能造成的功能损伤的风险系数", "allBrainFunctionAtlas": "全脑功能图谱"}, "menu": {"plan": "规划", "target": "靶点", "trajectory": "轨迹", "download": "下载", "export": "导出", "selectTargets": "添加靶点", "editTrajectory": "修改电极植入位置", "seeResult": "查看结果"}, "target": {"targetNum": "已添加{value}条轨迹", "addedTarget": "已添加靶点", "electrode": "电极长度(mm)", "anat": "结构分区", "anatShort": "结构分区", "nativeT1": "新靶点坐标", "riskLabel": "风险指数 (风险值大于0.7为高风险 ｜ 0 - 1)", "note": "备注", "addNote": "添加备注", "point": "靶点坐标", "risk": "风险指数", "addTarget": "添加靶点", "addTargetSuccess": "成功添加靶点", "targetConflict": "与已添加靶点位置冲突，请重新选择靶点位置", "targetRoi": "该区域不可添加靶点", "targetRoiTooltip": "血管为高危区域，脑室、小脑、脑外为无效区域，均不可添加靶点", "riskWarning": "该靶点风险值较高，请谨慎选择", "riskMaskWarning": "该区暂无风险预判，请谨慎填加", "dragInfo": "可拖拽调整靶点排序", "dragInfoTip": "自动规划后，候选轨迹较少的靶点将排列在前，当候选轨迹数量相同时，将根据添加靶点的顺序排列", "plan": "开始规划", "delTitle": "确认删除此靶点？", "dragTitle": "请确认靶点排序顺序", "dragSubTitle": "可拖拽调整靶点排序", "warning": "警告", "successTitle": "手术规划创建成功", "successInfo": "规划结果生成需要花费一定时长，可在当前页面等待或返回首页。", "successToHome": "受试者详情", "place": "在容积视图上点击/输入坐标", "notePlace": "在容积视图上点击/输入", "empty": "请在”靶点“的标签页下添加靶点，添加完成后点击“开始规划”按钮，20分钟后可查看规划结果", "leavePageTitle": "是否保存已添加靶点？", "nan": "未知", "notEmpty": "不能为空"}, "selectedFile": {"fileSelected": "此规划包含的扫描数据", "skull": "颅骨", "vessel": "血管", "scalp": "头皮", "cortex": "皮层", "riskMap": "风险图", "anatomical": "结构分区", "opacity": "调节图像不透明度", "rectangle": "仅显示轨迹", "goBack": "恢复视图", "lackT1": "缺少T1", "lackBold": "缺少bold", "lackVessel": "缺少血管", "lackSkull": "缺少颅骨", "fusionWarn": "扫描数据融合中，请稍后查看", "createPlan": "创建sEEG规划", "createResaction": "创建切除损毁规划", "selectFiles": "选择文件", "info": {"title": "使用扫描文件", "status": "状态", "dataId": "数据ID", "type": "类型", "scanName": "扫描名称"}}, "selectedBrainStatus": {"title": "脑部现状信息", "normal": "正常", "damage": "存在脑部变形", "leftBrain": "左脑", "rightBrain": "右脑", "frontal": "额叶", "central": "中央前后回", "parietal": "顶叶", "temporal": "颞叶", "occipital": "枕叶", "notification": "下面这些选择方式会导致规划结果不准确：选择5个以上的变形区域，同时选择双侧的枕叶，或同时选择双侧中央前后回。 请至少保留一个区域用于规划。"}, "trajectory": {"nativeT1": "靶点坐标", "riskLabel": "靶点风险值", "nativeT1Anat": "靶点结构分区", "all": "全部", "left": "左半球", "right": "右半球", "leavePageTitle": "确认退出当前页面？", "leavePageContent": "你的修改将不会被保存", "entryPoint": "进入点", "trajectoryRisk": "轨迹风险指数", "trajectoryRiskShort": "轨迹风险指数", "angle": "电极植入角度", "angleShort": "电极植入角度", "length": "长度（mm）", "lengthShort": "长度（mm）", "overlappedFunctionalNetworks": "相交功能网络", "overlappedFunctionalNetworksShort": "相交功能网络", "cantFunctional": "请调节皮层透明度小于100%后查看相交功能网络", "hideTrajectory": "隐藏轨迹", "showTrajectory": "显示轨迹", "throughTemporalLobe": "经过颞叶", "throughTemporalLobeShort": "经过颞叶", "yes": "是", "no": "否", "editTarget": "编辑靶点", "cancelEdit": "取消编辑", "savePlanAs": "方案另存", "cannotLocked": "没有选中轨迹, 无法锁定", "modificationcompleted": "修改完成", "lockInOrder": "请依次锁定", "modifyPlan": "修改规划", "lookTip": "请从上到下，依次锁定选中的轨迹", "entryPointInformation": "进入点信息", "temporalMuscleThickness": "颞肌厚度:(mm)", "temporalMuscleThicknessShort": "颞肌厚度", "skullThickness": "颅骨厚度:(mm)", "skullThicknessShort": "颅骨厚度", "noMatch": "无匹配进入点", "lookDeleteTip": "锁定中不能删除", "finshTrajectorysTip": "靶点的轨迹已修改, 应该先锁定再保存", "trajectorysTip": "为保证规划的完整性,以依照靶点候选轨迹的数量从少到多排列轨迹, 请从上到下一次锁定选中的轨迹,将依顺序过滤掉与已锁定轨迹有冲突的候选轨迹", "deleteTrajectoryTip": "确认删除此条轨迹?", "noTrajectoryMessage1": "该靶点与血管距离太近 ", "noTrajectoryMessage2": "该靶点与脑室距离太近 ", "noTrajectoryMessage3": "该靶点与小脑太近 ", "noTrajectoryMessage4": "该靶点候选轨迹长度大于电极长度 ", "noTrajectoryMessage5": "该靶点位于海马和杏仁核附近,无法在颞叶位置找到安全进入点 ", "noTrajectoryMessage6": "该靶点候选轨迹进入角度过大（大于30度） ", "noTrajectoryMessage7": "无法找到与血管保持安全距离的候选轨迹 ", "noTrajectoryMessage8": "无法找到与脑室保持安全距离的候选轨迹 ", "noTrajectoryMessage9": "无法找到与小脑保持安全距离的候选轨迹 ", "noTrajectoryMessage10": "与已有轨迹冲突", "noTrajectoryMessage11": "轨迹在颞叶的穿行厚度超出了颞肌平均厚度", "noTrajectoryMessage12": "轨迹经过了耳朵", "anat": "进入点结构分区"}}, "subject": {"subjectData": "受试者数据", "subjectId": "受试者ID: {value}", "createAt": "创建时间: {value}", "scanData": "上传数量: {value}", "loadMore": "加载更多...", "cantDelete": "不能删除", "cantDeleteOfJob": "受试者有正在准备中的数据,不能删除！", "cantRerun": "不能重新运行", "deleteUpload": "确定要删除此次上传吗?", "newSeegPlan": "新建规划", "all": "全部", "upload": "扫描数据", "data": "数据", "seegPlan": "手术规划", "display": "显示", "computing": "规划中，请稍后查看", "failedOpenContainer": "规划失败，没有结果可供查看", "delPlanTitle": "确定删除此数据?", "delTitleTwoWay": "若删除此数据，在“云”医学影像数据处理软件和磁共振影像数据处理软件中会同时删除", "delSubjectTitleTwoWay": "若删除此受试者，在“云”医学影像数据处理软件和磁共振影像数据处理软件中会同时删除", "notDataBts": "请上传扫描数据后新建规划", "notSeegBts": "点击按钮新建手术规划", "readySeegBts": "数据未准备完成，请稍后新建规划", "cantDeleteOfJobs": "关联任务正在运行,暂时无法删除", "seeg": "自动规划路径", "safetySeeg": "手动规划路径", "resection": "切除损毁", "notification": {"title": "提示", "samePlanStatus110": "该切除损毁规划已创建且处理完成", "samePlanStatus20": "手术规划重复创建", "samePlanStatus100": "手术规划重复创建", "successInfo20": "该切除损毁规划已创建且正在处理中，请稍后查看", "successInfo100": "该切除损毁规划已创建且处理失败", "successInfo0": "规划结果生成需要花费一定时长"}}, "selectType": {"registered": "扫描数据融合中，请稍后查看", "lack": "缺少{type}数据，请上传{value}后新建规划", "T1": "T1", "Bold": "Bold", "Vessel": "血管", "Skull": "颅骨", "lackVesselType": "CTA, MRA, MRV", "lackSkullType": "CT, CTA", "seegInfo": "sEEG电极植入手术规划，需要MRI T1、CT/CTA/MRA数据，有MRI BOLD更好", "resactionInfo": "肿瘤/癫痫灶切除或损毁手术的风险规划，需要MRI T1、BOLD数据，CT数据可选", "seeg": "自动规划路径", "safe": "手动规划路径", "resaction": "切除/损毁手术规划", "title": "选择规划类型", "selectScan": "选择扫描数据", "doPlanning": "做规划"}, "seeg": {"newSeeg": "创建规划", "title": "手术规划", "surgeryPlanningType": "手术规划类型: {value}", "fileType": "文件类型: {value}", "resection": "切除/损毁", "seeg": "自动规划路径", "seegTableTitle": "自动规划路径", "saseeg": "手动规划路径", "safety": "手动规划路径", "canDeleteTip1": "文件准备中, 无法删除", "planningcanDeleteTip2": "规划中, 无法删除", "canDownloadTip": "文件准备中，请稍后下载", "canreDownloadTip": "文件准备中，请稍后重新处理"}, "upload": {"datId": "数据ID: {value}", "fileName": "文件名称: {value}", "uploadedAt": "创建时间: {value}", "fileType": "文件类型: {value}", "stauts": "状态: {value}", "data": "数据", "plan": "规划", "modalTitle": "选择查看数据目的", "modalFuseTitle": "查看扫描数据融合状态", "fuseEmpty": "融合数据为空", "uploadFailure": "当前数据无处理结果可查看", "uploadProgerss": "扫描数据正在融合中，请稍后新建规划", "uploadModalLabel": "选择扫描类", "tableFileName": "文件名", "dataId": "数据ID", "createdAt": "创建时间", "scanType": "扫描类", "jobStatus": "融合状态"}, "viewTool": "查看详情", "tool": {"10": "规划中,请稍后导出结果", "15": "规划中,请稍后导出结果", "20": "规划中,请稍后导出结果", "100": "无规划结果可供导出", "110": "导出在T1空间的规划结果", "120": "规划结果导出中,请稍后下载", "130": "下载已导出结果", "140": "导出失败,无法下载"}, "deleteTool": {"10": "规划中,可以删除", "15": "规划中,无法删除", "20": "规划中,无法删除", "100": "删除规划", "110": "删除规划", "120": "规划结果导出中,无法删除", "130": "删除规划", "140": "删除规划"}, "rerunTool": {"10": "规划中,请稍后重新处理", "15": "规划中,请稍后重新处理", "20": "规划中,请稍后重新处理", "100": "重新处理", "110": "重新处理", "120": "规划结果导出中,请稍后重新处理", "130": "重新处理", "140": "重新处理"}, "status": {"10": "规划中", "15": "规划中", "20": "规划中", "100": "规划失败", "110": "规划完成", "120": "导出中", "130": "导出完成", "140": "导出失败", "400": "同步失败", "500": "同步待处理"}, "resection18etWorkName": {"0": "空", "1": "外侧视觉网络", "2": "内侧视觉网络", "3": "肢体感觉运动区", "4": "面部感觉运动区", "5": "背侧注意网络A", "6": "背侧注意网络B", "7": "腹侧注意网络A", "8": "腹侧注意网络B", "9": "颞叶边缘网络", "10": "额叶边缘网络", "11": "内顶网络", "12": "执行控制网络A", "13": "执行控制网络B", "14": "语言网络B", "15": "情景记忆", "16": "默认网络", "17": "语言网络A", "18": "上肢感觉运动区"}, "seeg18etWorkName": {"0": "空", "1": "外侧视觉网络", "2": "内侧视觉网络", "3": "下肢感觉运动区", "4": "面部感觉运动区", "5": "背侧注意网络A", "6": "背侧注意网络B", "7": "腹侧注意网络A", "8": "腹侧注意网络B", "9": "颞叶边缘网络", "10": "额叶边缘网络", "11": "内顶网络", "12": "执行控制网络A", "13": "语言网络A", "14": "语言网络B", "15": "情景记忆", "16": "默认网络", "17": "执行控制网络B", "18": "上肢感觉运动区"}, "job18etWorkName": {"0": "空", "1": "外侧视觉网络", "2": "内侧视觉网络", "3": "下肢感觉运动区", "4": "面部感觉运动区", "5": "背侧注意网络A", "6": "背侧注意网络B", "7": "腹侧注意网络A", "8": "腹侧注意网络B", "9": "颞叶边缘网络", "10": "额叶边缘网络", "11": "内顶网络", "12": "执行控制网络A", "13": "执行控制网络B", "14": "语言网络B", "15": "情景记忆", "16": "默认网络", "17": "语言网络A", "18": "上肢感觉运动区"}, "riskMapName": {"0": "空", "1": "语言", "2": "肢体运动", "3": "肢体运动", "4": "面部运动", "5": "视觉"}, "surgePlanTable": {"subjectId": "受试者ID", "planType": "规划类型", "createdAt": "创建时间", "scanType": "扫描类型", "status": "状态", "action": "操作", "msg": {"download": "文件准备中，请稍后下载", "delete": "确定要删除这个规划吗？", "rerun": "文件准备中，请稍后重新处理", "prompt": "提示", "downloadContent": "规划结果已开始导出,请稍后下载"}}, "subjectsTable": {"scanData": "扫描数据数量", "planData": "规划数量", "latePlanStatus": "最新规划状态", "uploadTip": "上传数据", "msg": {"delete": "确定要删除这个受试者吗？", "prompt": "提示", "dataBeingProcessed": "受试者下存在正在处理的数据。"}}}, "safety": {"appendTrajectory": "已添加轨迹：", "exportData": "导出规划", "controlTitle": "轨迹信息:", "titlePlaceHold": "请输入轨迹名称", "next": "下一步", "done": "完成", "riskValue": "风险指数", "cancel": "取消修改", "delAlert": "确定要删除这条轨迹吗？", "suefaceClickWarn": "下拉或在容积视图上点击选择靶点区域", "riskLabel": "风险值大于0.7为高风险，0风险最低，1风险最高，风险值会根据已添加的轨迹实际计算", "invalidRegion": "无效区域，请重新选择", "card": {"title": "轨迹名称", "targetRegion": "靶点区域", "target": "靶点", "entryRegion": "皮层进入点区域", "entry": "皮层进入点", "targetPlaceholder": "下拉或在容积视图上点击选择靶点区域", "entryPlaceholder": "下拉或在视图上选择皮层进入点区域", "coordsPlaceholder": "在容积视图上点击/输入坐标"}, "cortexWarn": "请在皮层上选择进入点"}, "routeError": {"goHome": "返回首页", "goLogin": "返回登录页面", "404": "对不起,您访问的页面不存在。"}, "patchName": {"Unknown": "未知", "G_and_S_frontomargin": "额缘回和沟", "G_and_S_occipital_inf": "枕下回和沟", "G_and_S_paracentral": "中央旁小叶和沟", "G_and_S_subcentral": "中央下回和沟", "G_and_S_transv_frontopol": "额极横回和沟", "G_and_S_cingul-Ant": "扣带回前部和沟", "G_and_S_cingul-Mid-Ant": "扣带回中前部和沟", "G_and_S_cingul-Mid-Post": "扣带回中后部和沟", "G_cingul-Post-dorsal": "扣带回后背部", "G_cingul-Post-ventral": "扣带回后腹部", "G_cuneus": "楔叶", "G_front_inf-Opercular": "额下回盖部", "G_front_inf-Orbital": "额下回眶部", "G_front_inf-Triangul": "额下回三角部", "G_front_middle": "额中回", "G_front_sup": "额上回", "G_Ins_lg_and_S_cent_ins": "岛长回和岛中央沟", "G_insular_short": "岛短回", "G_occipital_middle": "枕中回", "G_occipital_sup": "枕上回", "G_oc-temp_lat-fusifor": "梭状回", "G_oc-temp_med-Lingual": "舌回", "G_oc-temp_med-Parahip": "海马旁回", "G_orbital": "眶回", "G_pariet_inf-Angular": "角回", "G_pariet_inf-Supramar": "缘上回", "G_parietal_sup": "顶上小叶", "G_postcentral": "中央后回", "G_precentral": "中央前回", "G_precuneus": "楔前叶", "G_rectus": "直回", "G_subcallosal": "胼胝体下", "G_temp_sup-G_T_transv": "赫氏回前", "G_temp_sup-Lateral": "颞上回外侧面", "G_temp_sup-Plan_polar": "颞上回极平面", "G_temp_sup-Plan_tempo": "颞上回颞平面", "G_temporal_inf": "颞下回", "G_temporal_middle": "颞中回", "Lat_Fis-ant-Horizont": "外侧裂前段水平支", "Lat_Fis-ant-Vertical": "外侧裂前段垂直支", "Lat_Fis-post": "外侧裂后支", "Medial_wall": "内侧壁", "Pole_occipital": "枕极", "Pole_temporal": "颞极", "S_calcarine": "距状沟", "S_central": "中央沟", "S_cingul-Marginalis": "扣带沟缘支", "S_circular_insula_ant": "岛环状沟前段", "S_circular_insula_inf": "岛环状沟下段", "S_circular_insula_sup": "岛环状沟上段", "S_collat_transv_ant": "侧副沟横前部", "S_collat_transv_post": "侧副沟横后部", "S_front_inf": "额下沟", "S_front_middle": "额中沟", "S_front_sup": "额上沟", "S_interm_prim-Jensen": "<PERSON>中间沟", "S_intrapariet_and_P_trans": "顶内沟和顶横沟", "S_oc_middle_and_Lunatus": "枕中沟和月状沟", "S_oc_sup_and_transversal": "枕上沟和枕横沟", "S_occipital_ant": "枕前沟", "S_oc-temp_lat": "枕颞外侧沟", "S_oc-temp_med_and_Lingual": "侧副沟和舌沟", "S_orbital_lateral": "眶外沟", "S_orbital_med-olfact": "眶内沟", "S_orbital-H_Shaped": "眶沟", "S_parieto_occipital": "顶枕沟", "S_pericallosal": "胼周沟", "S_postcentral": "中央后沟", "S_precentral-inf-part": "中央沟下部", "S_precentral-sup-part": "中央沟上部", "S_suborbital": "眶下沟", "S_subparietal": "顶下沟", "S_temporal_inf": "颞下沟", "S_temporal_sup": "颞上沟", "S_temporal_transverse": "颞横沟", "unknown": "未知", "bankssts": "颞上沟后部", "caudalanteriorcingulate": "前扣带回尾部", "caudalmiddlefrontal": "额中回尾部", "corpuscallosum": "胼胝体", "cuneus": "楔叶", "entorhinal": "嗅皮质", "fusiform": "梭状回", "inferiorparietal": "顶叶下回", "inferiortemporal": "颞下回", "isthmuscingulate": "扣带回峡部", "lateraloccipital": "枕叶外侧", "lateralorbitofrontal": "外侧眶额", "lingual": "舌回", "medialorbitofrontal": "内侧眶额", "middletemporal": "颞中回", "parahippocampal": "海马旁回", "paracentral": "中央旁小叶", "parsopercularis": "额下回后部 ", "parsorbitalis": "额下回眶部", "parstriangularis": "额下回三角部", "pericalcarine": "距状旁回", "postcentral": "中央后回", "posteriorcingulate": "扣带回后部", "precentral": "中央前回", "precuneus": "楔前叶", "rostralanteriorcingulate": "前扣带回头部", "rostralmiddlefrontal": "额中回头部", "superiorfrontal": "额上回", "superiorparietal": "顶叶上回", "superiortemporal": "颞上回", "supramarginal": "缘上回", "frontalpole": "额极", "temporalpole": "颞极", "transversetemporal": "颞横回", "insula": "脑岛", "Left-Lateral-Ventricle": "左侧脑室", "Left-Inf-Lat-Vent": "左侧脑室颞角", "Left-Cerebellum-White-Matter": "左小脑白质", "Left-Cerebral-White-Matter": "左脑白质", "Left-Cerebellum-Cortex": "左小脑皮质", "Left-Thalamus-Proper": "左丘脑", "Left-Caudate": "左尾状核", "Left-Putamen": "左壳核", "Left-Pallidum": "左苍白球", "3rd-Ventricle": "第三脑室", "4th-Ventricle": "第四脑室", "Brain-Stem": "脑干", "Left-Hippocampus": "左海马体", "Left-Amygdala": "左杏仁核", "CSF": "脑脊液", "Left-Accumbens-area": "左伏隔核", "Left-VentralDC": "左脑室憩室", "Left-vessel": "左脑血管", "Left-choroid-plexus": "左脑脉络丛", "Right-Lateral-Ventricle": "右侧脑室", "Right-Inf-Lat-Vent": "右侧脑室颞角", "Right-Cerebellum-White-Matter": "右小脑白质", "Right-Cerebral-White-Matter": "右脑白质", "Right-Cerebellum-Cortex": "右小脑皮质", "Right-Thalamus-Proper": "右丘脑", "Right-Caudate": "右尾状核", "Right-Putamen": "右壳核", "Right-Pallidum": "右侧苍白球", "Right-Hippocampus": "右侧海马", "Right-Amygdala": "右杏仁核", "Right-Accumbens-area": "右伏隔区", "Right-VentralDC": "右脑室憩室", "Right-vessel": "右脑血管", "Right-choroid-plexus": "右脑脉络丛", "5th-Ventricle": "透明隔/第五脑室", "WM-hypointensities": "白质信号", "Left-WM-hypointensities": "左脑白质信号", "Right-WM-hypointensities": "右脑白质信号", "non-WM-hypointensities": "灰质信号", "Left-non-WM-hypointensities": "左脑灰质信号", "Right-non-WM-hypointensities": "右脑灰质信号", "Optic-Chiasm": "视交叉", "CC_Posterior": "胼胝体后部", "CC_Mid_Posterior": "胼胝体中后部", "CC_Central": "胼胝体中部/胼胝体干", "CC_Mid_Anterior": "胼胝体前中部", "CC_Anterior": "前胼胝体"}, "aboutUs": {"productionName": "产品名称", "productModel": "产品型号", "boxAboutUs": "磁共振影像数据处理软件", "boxSystemAboutUs": "磁共振影像数据处理系统", "dataServerAboutUs": "云医学影像数据处理软件", "manufacturer": "注册人/生产企业/售后服务单位", "productionAddress": "注册人/生产企业住所/生产地址", "telephone": "联系电话", "email": "电子邮箱", "version": "完整版本", "website": "公司网址", "downloadLog": "导出日志", "modelNumberMRI": "pBFS-Analyzer", "modelNumberCloud": "PNT-100SW", "modelNumberSystem": "PTC", "manufacturerContent": "优脑银河（湖南）科技有限公司", "productionAddressContent": "湖南省长沙市高新开发区谷苑路229号海凭园1栋12楼1201—1208房", "contactUs": "联系我们"}, "ipLimit": {"title": "访问限制", "range": "IP范围", "description": "备注", "action": "操作", "add": "新增访问限制", "addTitle": "添加访问限制", "editTitle": "编辑访问限制", "ipType": "类型", "ip": "IP地址", "from": "从", "to": "至", "delTitle": "你确定要删除该访问限制吗？", "type": {"assign": "指定地址", "match": "IP模糊指定", "range": "IP范围"}, "login": {"title": "需要设置IP访问限制", "reject": "拒绝并退出", "setting": "去设置"}, "reject": "IP限制，请联系机构管理员", "alert": {"ip": "请输入合法的IP", "range": "IP范围错误"}, "info": {"title": "说明", "1": "指定地址 需要按照IP格式输入允许访问的特定IP，如*************", "2": "IP模糊指定，后几位可以为*，如123.126.82.*，即允许************-**************。\n如123.126.*.*, 即允许***********-***************, \n如果是*.*.*.*，即允许所有IP.", "3": "IP范围输入，输入起始IP，终止IP\n*************-*************"}}, "point": {"exit": "安全退出硬件", "header": {"home": "患者列表", "treatment": "治疗方案", "person": "患者", "newPerson": "新建治疗", "stimulateTemplate": "刺激模板", "processBar": "传输进度"}, "jumpToMotion": "导航追踪运动区靶点", "thresholdLabel": "请在下方输入运动阈值，或者通过导航追踪运动区靶点的方式测量运动阈值", "jumpToMotionPlaceholder": "在视图上高亮展示该患者的运动脑区，并确定运动区靶点，通过导航追踪靶点位置并观察波幅以得到运动阈值", "downloadLog": {"fileName": "文件名称", "outputPath": "导出路径", "success": "已完成", "error": "出错了", "errorInfo": "错误信息：{err}", "info": "请拔出U盘，将日志文件发送给技术支持"}, "upload": {"headTitle": "选择数据上传方式", "scan": "扫描数据", "reselect": "重选文件", "selected": "选中数据", "success": "数据上传成功", "successInfo": "处理结果将在24小时内展示", "home": "首页", "patient": "患者详情页", "emptyTitle": "警告", "emptyContent": "数据规格不符，请选择存在T1类型数据的文件目录", "screeningLabel": "筛查", "screening1": "有过癫痫发作", "screening2": "有过中风", "screening3": "有设备植入", "screening4": "有头部损伤", "screening5": "正服用精神或神经活性药物", "diseaseInfo": "说明", "diseaseInfoPlaceholder": "请填写说明内容", "remark": "方案备注名", "remarkPlaceholder": "请填写方案备注名", "loading": "正在加载文件，可能需要几分钟，请耐心等待...", "overSize": "您选择的数据已超过上传限制，请减少选择文件数量（DICOM文件1.7G/NIFTI文件800M）"}, "disease": {"label": "疾病", "other": "其他", "alert": "请至少选择一项疾病", "Aphasia": "失语症", "Depression": "抑郁症", "Insomnia": "失眠", "Hemiplegia": "偏瘫", "Parkinson": "帕金森", "1": "失语症", "2": "抑郁症", "3": "请输入疾病类型"}, "createSource": {"localUpload": "本地上传", "pacsGain": "磁共振获取", "reprocess": "重新处理", "retreatment": "再次治疗", "modifyPlan": "修改方案"}, "home": {"hideListFirst": "列表内容已隐藏（共{total}条）", "hideListSecond": "点击“刷新”图标可展示列表详情", "search": {"placeholder": "搜索患者名字、患者ID、数据名称", "clear": "清空过滤器"}, "selectLabel": "状态", "audit": {"menu": {"await": "待审核任务", "all": "所有治疗任务"}, "emptyTable": "暂无待审核任务"}, "table": {"name": "姓名", "patientId": "患者ID", "sex": "性别", "age": "年龄", "type": "疾病", "noStart": "未开始治疗", "noAge": "未知", "updateAt": "最近一次治疗", "uploadAt": "上传时间", "folderName": "扫描数据名称", "project": "状态", "action": "操作", "cure": "治疗", "empty": "无患者，点击按钮添加", "emptyPlanButHavePatient1": "无治疗任务记录", "emptyPlanButHavePatient2": "可点击按钮新建患者后上传数据以创建治疗任务", "emptyPlanButHavePatient3": "无已完成的治疗任务", "createButton": "新建患者", "emptyPlan": "没有治疗任务，点击按钮添加", "createPlanButton": "新建任务", "deleteConfirm": "您确定要删除该患者吗", "detailToolTitle": "查看详情", "deleteToolTitle": "删除患者", "editToolTitle": "编辑患者", "uploadToolTitle": "上传数据", "updateRecordToolTitle": "修改历史", "noDelete": "治疗方案处理中，不可删除此患者", "actionTool": {"detailPlan": "查看方案详情", "report": "查看报告", "upload": "上传数据", "detailPatient": "查看患者详情", "goBack": "恢复", "edit": "编辑", "goBackTwiceConfirm": "确认要恢复吗？", "view": "查看方案", "audit": "审核", "resultOf": "查看原因"}, "createType": {"title": "创建方式", "1": "本地上传", "2": "修改方案", "3": "重新处理", "4": "再次治疗", "5": "核磁共振获取"}, "lastTreatmentAt": "上次治疗时间", "createAt": "创建时间", "createAtUser": "创建者", "realtimeCureTime": "最近一次治疗", "cureUser": "治疗师", "mri": "MRI", "cureProjectNumber": "治疗方案数量", "hadCureNumber": "实施治疗次数", "deleteTime": "删除时间", "deleteUser": "删除者", "audit": {"orgName": "机构名称", "patientName": "患者姓名", "patientCode": "患者ID", "disease": "病症", "status": "状态", "statusUpdateAt": "状态变更时间", "auditTime": "方案完成时间", "auditResult": "审核结果", "auditUser": "审核人", "recentUpdateAt": "最近修改时间", "recentPlanCreateAt": "最近任务创建时间"}}, "planTooltip": {"15": "治疗任务排队中，请稍后查看", "20": "治疗任务处理中，请稍后查看", "90": "处理失败，暂无结果可查看"}}, "edit": {"goBack": "返回", "goBackCancel": "留在当前页面", "goBackOk": "确认退出", "editTitle": "修改患者信息", "addTitle": "添加患者信息", "firstName": "姓", "firstNamePlaceholder": "请输入姓", "lastName": "名", "lastNamePlaceholder": "请输入名", "patientId": "患者ID", "patientIdPlaceholder": "请输入患者病历号", "hadPatientInDB": "该患者已存在", "hadPatientInDBButNotPermission": "该患者已存在,请至患者列表页查看", "sex": "性别", "sexPlaceholder": "请选择性别", "birth": "出生日期", "birthPlaceholder": "请选择日期或者输入如下格式的日期（2000-01-01）后回车", "telephone": "手机号", "telephonePlaceholder": "请输入手机号", "remark": "备注", "remarkPlaceholder": "请输入备注", "cancel": "取消", "uploadButton": "上传数据", "havePatientDb": "使用已有患者信息", "recoverPatient": "恢复已删除患者", "goBackTitle": "警告", "goBackContent": "若退出当前页面，不会保存已经填写的信息", "nameError": "仅允许输入中文、字母、数字、 -、_", "nameErrorMin": "最少输入一位", "nameErrorMax": "至多输入10位", "nameErrorRequired": "不能为空", "telephoneError": "请输入符合实际的手机号", "commentErrorMax": "至多输入300位", "patientCodeMin": "至少输入1位", "patientCodeMax": "至多输入40位", "patientCodeMin6": "至少输入6位", "patientCodeMax20": "至多输入20位", "tooltipText": "查询到已存在相同患者的ID，若使用已有患者信息，将更新已有患者信息", "patientCodeMust": "患者ID不能为空", "org": "机构"}, "report": {"goBackNavigate": "报告详情", "title": "报告", "download": "下载报告", "doctor": {"account": "操作者账户", "username": "操作者姓名", "createdAt": "生成时间", "org": "机构"}, "basic": {"title": "患者基本信息", "name": "姓名", "patientId": "患者ID", "sex": "性别", "age": "年龄", "type": "疾病", "position": "患侧", "telephone": "手机号", "remark": "病程", "motionScope": "运动阈值（%）"}, "diseaseFilter": {"title": "病状筛查信息", "disease": "疾病", "screening": "筛查信息", "info": "说明", "treatmentCount": "计划治疗次数", "remainTreatmentCount": "已经治疗次数"}, "cure": {"title": "治疗详情"}, "spot": {"newTitle": "治疗方案", "MEP": "MEP", "title": "靶点", "name": "靶点名称:", "run": "脉冲刺激", "purse": "脉冲暂停", "over": "脉冲结束", "tmsFail": "TMS异常", "simpleVolSeed": "坐标(volRAS) :", "simpleSurfSeed": "坐标(surfRAS) :", "volSeed": "靶点坐标(volRAS) :", "surfSeed": "靶点坐标(surfRAS) :", "point": "顶角编号 :", "region": "功能分区", "loopDes": "环路描述", "strength": "强度(%):", "pulseTotal": "脉冲个数:", "treatmentTime": "时长(s):"}, "params": {"titleProject": "刺激方案", "title": "刺激类型", "frequency": "频率(Hz):", "exciteTime": "刺激时间(s):", "exciteNum": "刺激个数:", "spaceTime": "间隙时间(s):", "repetitionNum": "重复次数:", "cureTime": "治疗时间:"}, "project": {"title": "治疗时间", "index": "频次", "updatedAt": "时间"}, "conclusion": {"title": "结论"}, "treatmentCycle": "治疗周期", "cureTable": {"startCureTime": "刺激开始时间", "endCureTime": "刺激停止时间", "allTime": "实际刺激时长(s)"}, "saveReportSuccess": "保存报告成功！", "saveReportFailed": "保存报告失败！", "exportPointSuccess": "导出成功", "exportPointFailed": "导出失败，请重试。", "pointPlanTitle": "导出NG方案", "planTitle": "导出JSON方案", "simpleReportTitle": "报告", "pdfReportTitle": "图文报告", "extraInfoTitle": "补充信息", "exportPlanTitle": "导出或下发方案", "diseaseDescription": "病情描述", "planDetail": "方案详情", "surfaceLoadingTips": "脑图加载中，预计需要2~8秒，请耐心等待", "picLoadFailed": "图片加载失败", "exportSimpleReportFailed": "报告下载失败，请重新下载", "exportPDFReportFailed": "图文报告下载失败，请重新下载", "treatmentCycleDefaultContent": "每轮治疗中间时间间隔：50min\n每次__轮， 连续__次"}, "cureProject": {"invalidProject": "无效方案", "haveRestingNoStart": "该患者存在休息中的治疗方案，无法开始本次治疗", "planDataError": "该方案数据加载可能不全面,请斟酌使用", "overlaySurfaceFileNotFound": "该方案因为数据问题，无法查看脑图结构分区", "whereHeIsGo": "该患者注册方案在有效期, 请选择注册流程", "copySuccessMessage": "请至首页查看新方案", "whereHeIsGoTitle": "提示", "goBackNavigate": "返回", "illnessLabel": "疾病", "title": "请选择治疗靶点", "parchType": "分区标记", "cureSpotTitle": "治疗靶点", "treatmentForm": {"count": "治疗次数", "must": "必填项", "placeholder": "请填写治疗次数"}, "spot": {"type": "正刺激", "deleteConfirm": "您确定要删除吗", "actionIcon": {"delete": "删除靶点", "export": "导入刺激参数", "save": "保存刺激模板"}, "pointType": "靶点类型", "pointName": "靶点名称", "pointNamePlaceholder": "请输入靶点名称", "pointCoordinate": "靶点坐标(vol RAS)", "pointType1": "治疗靶点", "pointType2": "MEP靶点"}, "options": {"common": "靶点", "sport": "MEP靶点"}, "create": {"title": "添加靶点", "info": "请添加运动区靶点用以测试患者的运动阈值", "spotName": "靶点名称", "showPatch": "显示分区", "notInCort": "您选择的靶点不在皮层上", "notInMotionCort": "您选择的靶点不在运动区皮层上"}, "remark": {"num": "治疗次数", "numPlaceholder": "请填写共需要治疗次数", "isEpilepsy": "是否有过癫痫发作", "isHeadInjured": "是否有过头部受伤", "headerInjuredPosition": "请填写头部损伤位置", "courseOfDisease": "病程", "courseOfDiseasePlaceholder": "请填写病人病程", "yes": "是", "no": "否"}, "params": {"saveTemplate": "保存模板", "templateName": "模板名称", "exportTemplate": "导入模板", "pulse": "脉冲数", "time": "时长", "stopExcite": "停止刺激", "exciteOver": "刺激完成", "button": {"create": "添加刺激类型", "deleteConfirm": "确认删除该刺激类型吗", "deleteRefuse": "不可删除此刺激类型"}, "type": {"label": "刺激类型", "common": "重复刺激", "iTBS": "iTBS", "cTBS": "cTBS", "rTMS": "rTMS", "1": "重复刺激", "2": "iTBS", "3": "cTBS"}, "common": {"stimulateType": "刺激类型", "sumNum": "总脉冲数", "relativeStrength": "相对强度(%)", "strength": "相对强度(%)", "exciteTime": "治疗时间(s)", "frequency": "串脉冲频率(Hz)", "inPulseNum": "串内脉冲数", "pulseNum": "脉冲串数", "exciteInterval": "刺激间隔(s)", "pulseTotal": "总脉冲数", "treatmentTime": "治疗时间(s)", "strandPulseFrequency": "串脉冲频率(Hz)", "innerStrandPulseCount": "串内脉冲数", "strandPulseCount": "脉冲串数", "intermissionTime": "刺激间隔(s)"}, "iTBS": {"stimulateType": "刺激类型", "strength": "相对强度(%)", "plexusInnerFrequency": "丛内频率(Hz)", "plexusInterFrequency": "丛间频率(Hz)", "plexusInnerPulseCount": "丛内脉冲数(个)", "plexusCount": "刺激丛数(丛)", "strandPulseCount": "脉冲串数(串)", "intermissionTime": "刺激间隔(s)", "pulseTotal": "总脉冲数(个)", "treatmentTime": "刺激时长(s)"}, "cTBS": {"stimulateType": "刺激类型", "strength": "相对强度(%)", "plexusInnerFrequency": "丛内频率(Hz)", "plexusInterFrequency": "丛间频率(Hz)", "plexusInnerPulseCount": "丛内脉冲数(个)", "plexusCount": "刺激丛数(丛)", "strandPulseCount": "脉冲串数(串)", "intermissionTime": "刺激间隔(s)", "pulseTotal": "总脉冲数(个)", "treatmentTime": "刺激时长(s)"}, "rTMS": {"stimulateType": "刺激类型", "strength": "相对强度(%)", "strandPulseFrequency": "串脉冲频率(Hz)", "innerStrandPulseCount": "串内脉冲数(个)", "sumNum": "总脉冲数(个)", "strandPulseCount": "脉冲串数(串)", "intermissionTime": "刺激间隔(s)", "pulseTotal": "总脉冲数(个)", "treatmentTime": "刺激时长(s)"}, "noSpot": "请添加治疗靶点", "noSelect": "请选择治疗靶点", "noCount": "请添加治疗次数", "noParam": "请选择刺激参数", "rules": {"mustWrite": "{value}为必填项", "maxValue": "最大允许输入{value}", "minValue": "最小允许输入{value}", "valueLimit": "请输入{minValue}-{maxValue}之间的数值", "limit": "超限", "pulseTotalLimit": "总脉冲数超限，范围为{minValue}-{maxValue}", "treatmentTimeLimit": "刺激时长超限，范围为{minValue}-{maxValue}", "strengthWrite": "相对强度超限，当前相对强度值不能高于{value}", "limitWrite": "输出功率超限，请尝试降低{value}参数", "intermissionTimeWrite": "输出功率超限，请尝试增加刺激间隔参数"}}, "footer": {"saveAs": "方案另存为", "save": "保存方案", "termination": "终止治疗", "edit": "修改方案", "measureScope": "测运动阈值", "resetMeasureScope": "重测运动阈值", "startCure": "开始治疗", "reStartCure": "再次治疗", "report": "查看报告", "download": "导出方案", "cancel": "取消", "add": "添加靶点", "resting": "休息", "register": "重新注册", "start": "开始刺激", "over": "结束治疗", "log": "查看日志"}, "addTitle": "添加刺激参数", "editTitle": "修改刺激参数", "preview": {"typeLabel": "疾病", "headInjuredLabel": "头部损伤", "courseOfDiseaseLabel": "病程", "totalLabel": "总治疗数", "havePastLabel": "已治疗次数", "createByUser": "创建者", "createAt": "创建时间", "treatByUser": "治疗师", "treatAt": "治疗时间", "sportSpotScopeLabel": "运动阈值", "measureScopeWarning": "当前患者没有添加运动区靶点位置,点击”添加“以添加运动区靶点，若不添加运动区靶点，请在下方输入运动阈值", "measureScopeWarningStatus50": "请在下方输入运动阈值", "measureScopeModalInputButton": "已输入运动阈值", "terminationContent": "确定要终止此治疗方案吗", "hasEpilepsy": "有癫痫发作史", "noEpilepsy": "无癫痫发作史"}, "noSpot": "请添加治疗靶点", "noSelect": "请选择治疗靶点", "noCount": "请添加治疗次数", "noParam": "请选择刺激参数"}, "patient": {"new": "新建治疗方案", "empty": "当前患者无治疗方案，可点击按钮新建", "delete": "确定删除此患者?", "cantDeleteTip": "有处理中的治疗报方案不能删除", "refresh": "刷新", "measureMotionThreshold": "重测运动阈值", "status": "状态:", "deleted": "已删除", "deletedMsg": "已删除信息：", "modifyAt": "修改时间", "modifyPeople": "修改者", "modifyHistory": "修改历史", "patientCard": {"attendingPhysician": "主治医师: {value}", "creatUser": "创建方式: {value}", "pointNum": "靶点数量: {value}", "lastTreatment": "最后一次: {value}", "stopUser": "终止治疗: {value}", "processingTime": "已进行: {value}", "remainTreatmentCount": "治疗次数: {value}", "seeJobFiles": "查看扫描文件", "reRun": "重新运行", "createAt": "创建时间: ", "dataName": "数据名称: {value}", "note": "备注", "noDataDes": "无治疗方案,点击按钮上传", "reRunTip": "确认重新处理此治疗方案么？", "delete": "确定删除此治疗方案吗?", "createSource": {"1": "本地上传", "2": "磁共振获取", "3": "重新处理", "4": "再次治疗", "5": "修改方案"}, "status": {"-1": "全部", "15": "排队中", "20": "处理中", "30": "图像处理完成", "100": "处理失败", "101": "处理失败", "40": "待治疗", "50": "治疗中", "60": "休息中", "110": "治疗完成", "90": "治疗终止"}}, "uploadCard": {"noDataDes": "无扫描数据,点击按钮上传", "noDataBtnTitle": "上传数据"}, "reportCard": {"planCreateAt": "治疗创建时间: {value}", "reportCreateAt": "报告生成时间: {value}", "noData": "暂无治疗方案可生成报告", "noDataDes": "无治疗报告可供查看, 点击按钮生成", "noDataBtnTitle": "查看报告", "delete": "确定删除此治疗报告吗?"}, "form": {"patientCodeFieldName": "患者ID", "genderFieldName": "性别", "ageFieldName": "年龄", "mobileFieldName": "手机号", "lastTreatFieldName": "最后一次治疗", "commentFieldName": "备注", "treatRecordsFieldName": "运动阈值", "createFieldName": "创建信息", "MRIFieldName": "MRI数量", "planFieldName": "治疗方案", "taskFieldName": "任务数量", "treatmentsFieldName": "已实施治疗次数", "treatCountFieldName": "已实施治疗次数", "createdAtFieldName": "创建时间", "createdAtByUserFieldName": "创建人", "updatedAtFieldName": "最后修改", "allPlanCountFieldName": "所有方案", "treatPlanCountFieldName": "已治疗方案"}, "message": {"status15": "系统运算中,请等待状态为待治疗时再点击", "status100": "处理失败，请检查上传的文件"}}, "planList": {"disease": "疾病", "attendingPhysician": "主治医师", "createAt": "创建时间", "statu": "状态", "title": "选择治疗方案生成报告", "createReportTip": "仅有被用于治疗的方案可生成报告", "status": {"15": "处理中", "20": "处理中", "30": "图像处理完成", "100": "处理失败", "40": "待治疗", "50": "治疗中", "110": "治疗完成", "90": "治疗终止"}}, "coil": {"steps": {"one": "选择线圈", "two": "选择标记模块", "three": "注册标记模块", "four": "检验注册结果", "over": "跳过注册流程"}, "leave": {"title": "注册流程未完成，是否结束注册流程？", "ok": "结束注册", "cancel": "继续注册"}, "reMotionThresholdRecords": {"title": "当前运动阈值为 {value}, 是否需要重新测量？", "ok": "重新测量", "cancel": "开始治疗"}, "select": {"action": "操作", "delAlert": "你确定要删除这个线圈吗？", "name": "名称", "tracker": "追踪器", "lastValid": "最后一次校准", "lastUsed": "最后一次使用", "new": "新建线圈", "reset": "重新校准", "nowTms": "现有TMS线圈", "validTitle": "选择线圈后请使用定标枪点击线圈上已标定的位置", "validInfo1": "距离小于5毫米，则视为线圈与线圈定标球相对位置准确", "validInfo2": "若多次尝试无法附和相对位置要求，请尝试重新校准线圈", "confirmCoil": "确认标记点", "useCoil": "使用此线圈", "distance": "相对距离（mm）", "resetDistance": "重新确认标记点", "warning": "相对距离较大，请重新确认标记点以确保注册准确性"}}, "newCoil": {"newCoil": "新建线圈", "deleteCoil": "删除线圈", "resetCoil": "重新校准线圈", "coilType": "线圈型号", "coilTypePlaceholder": "线圈型号", "coilName": "线圈名称", "coilNamePlaceholder": "请填写线圈名称", "fromTip": "请将线圈追踪装置绑定到线圈上", "coilNamePattern": "1-10 位数字/字母/中文/_ /-", "coilRegistered": {"title": "请使用指针依次点击5个标记点", "coilRegistered1": "标记点 1", "coilRegistered2": "标记点 2", "coilRegistered3": "标记点 3", "coilRegistered4": "标记点 4", "coilRegistered5": "标记点 5", "registeredButton": "注册标记点", "reRegisteredButton": "重新注册标记点", "relativeDistance": "相对距离(mm)：", "errTip1": "请选择线圈型号", "errTip2": "请填写线圈名称", "errTip3": "请注册线圈", "coilLabel": "线圈"}, "stimulate": {"fakeStimulate": "伪刺激模式", "strength": "强度衰减（%）："}}, "mark": {"validate": {"title": "验证注册的准确性", "info": "将指针沿着患者的头部皮肤移动，并观测皮肤与指针间的相对距离", "detail1": "小于3毫米证明注册结果精准", "detail2": "在3-5毫米之间证明注册结果相对精准", "detail3": "大于5毫米证明注册结果有误差，为了保证治疗的精准，请重新注册", "less": "小于", "notFound": "无法找到定标枪位置"}, "edit": {"preName": "标记点", "name": "名称", "added": "已添加的标记点", "info1": "在图上至少选择4个标记点以用于匹配图像和实际患者的关系。", "info2": "建议选择鼻根、鼻尖、耳珠上的凹凸处。"}, "register": {"title": "校准物理实际空间和影像的三维空间", "title_sub": "全部注册完成后可选择其中一个点重新注册", "info": "请使用指针在患者头部依次点击如下标记点", "confirm": "确认标记点", "reset": "重新注册标记点", "reConfirm": "重新确认标记点"}}, "preview": {"description": "扫描名称", "date": "时间", "type": "类型", "number": "序列号", "tip": "请同时选择T1和Bold类型数据上传", "tipT1": "请选择T1数据上传"}, "scaler": {"title": "物体是否在相机可监测范围内", "warning": "已超出监测范围", "scaler": "定标枪", "coil": "线圈", "patient": "患者", "unbind": "解除绑定", "unbindAlert": "你确定解除与该定标笔的绑定吗？", "model": {"title": "请选择定标抢型号并绑定", "tip": "请按一下定标笔上的按键或者点击下方对应的按钮，进行绑定", "bind": "绑定"}, "viewerTitle": "相机视野展示及边界示意"}, "treatment": {"complete": "本次治疗完成", "distance": "靶点距离", "angle": "法线夹角", "translation": "平移距离", "coil": "手持视角", "subject": "观察视角", "translationLabel": "平移指示", "angleLabel": "旋转示意", "view": "视图模式"}, "threshold": {"label": "阈值", "complete": "已完成运动阈值测试", "addSpot": "添加运动区靶点", "addSpotInfo": "请在皮层表面视图，已高亮的运动区位置上点击选择靶点，或在输入框中输入坐标"}, "dicom": {"table": {"node": "节点ID", "orgName": "机构名称", "createAt": "注册时间", "ip": "节点IP", "onLineStatus": "在线状态", "statusNumber": {"0": "在线", "1": "离线"}, "lastBeatTime": "最后心跳时间", "beatAction": "心跳记录", "time": "时间", "status": "状态", "damageStartTime": "中断开始", "recoverTime": "中断结束", "timeConsuming": "中断时长", "action": {"allReport": "所有记录", "damageReport": "中断记录"}, "modal": {"allTitle": "所有心跳记录", "damageTitle": "中断心跳记录"}}}, "audit": {"comment": "备注说明", "auditPage": {"title": "审核结果", "time": "时间", "cureSpot": "治疗靶点", "motionSpot": "MEP靶点"}, "result": {"1": "待审核", "2": "无需审核", "3": "审核不过", "4": "审核通过"}, "diseaseFilter": {"screening": {"1": "有过癫痫发作", "2": "有过中风", "3": "有植入设备", "4": "有过头部损伤", "5": "正服用精神或神经活性药物"}}, "operate": {"report": "查看报告", "view": "查看方案", "audit": "审核", "resultOf": "查看原因"}, "planStatus": {"15": "排队中", "20": "处理中", "30": "待审核", "40": "待治疗", "50": "治疗中", "90": "治疗终止", "100": "处理失败", "101": "审核不过", "110": "治疗完成", "targetCount": "个靶点"}, "card": {"await": "待审核", "origin": "原始方案", "diseaseFilter": "症状筛查", "auditResult": "已审核", "auditFail": "审核不过", "cureResult": "治疗方案"}, "filter": {"disease": "疾病", "screening": "筛查", "markName": "方案备注名", "markNamePlaceholder": "请填写方案备注名", "comment": "说明", "commentPlaceholder": "请填写说明"}, "container": {"header": {"patientTooltip": "查看患者详情", "colorMap": "分区颜色图样式", "changeLayout": "切换视图布局"}}, "auditFooter": {"passTooltip": "确认通过吗？", "cancel": "取消", "pass": "通过", "refuse": "不通过", "refuseTooltip": "确认不通过吗？"}, "auditNotification": {"title": "已被审核", "at": "于", "result": "审核", "currentPlan": "该方案"}}, "stimulus": {"template": {"label": "模板名称", "namePlaceholder": "请填写模板名称", "delMsg": "确认删除此刺激模板吗？", "searchPlaceholder": "搜索刺激模板名称", "empty": "当前没有刺激模板, 点击按钮创建", "duplicateStimulusTemplate": "刺激模板名称已存在", "createTemplate": "新建模板", "delTemplate": "删除模板", "seeMsg": "预览刺激模板", "modal": {"createTitle": "新建刺激参数", "editTitle": "编辑刺激参数", "importTitle": "导入刺激参数"}, "form": {"templateName": "请填写刺激模板名称", "exciteIntervalLimit": "数值范围需在1-600之间"}}}, "errorCode": {"SV50002": "模板名称重复"}, "log": {"table": {"createdAt": "时间", "createdAtUser": "操作者", "eventType": "事件", "remark": "备注", "empty": "暂无日志"}, "event": {"CreatePlan": "任务创建", "CompleteDataProcessing": "图像处理完成", "StartTreament": "开始治疗", "StartStimulation": "开始刺激", "StopStimulation": "停止刺激", "StopTreament": "结束治疗", "TerminateTreament": "终止治疗"}}, "emg": {"leave": {"title": "运动阈值未保存，是否保存", "ok": "保存", "cancel": "取消", "content": "运动阈值不能为空", "emptyTitle": "测量运动阈值流程未完成，是否结束测量运动阈值？", "emptyOk": "继续测量", "emptyCancel": "结束测量"}, "motionThresholdRecord": {"content": "阈值MT(%)已更新"}}}, "pointCloud": {"createCorrect": "数据上传时间正序", "createReverse": "数据上传时间倒序", "doneCorrect": "方案完成时间正序", "doneReverse": "方案完成时间倒序", "statusCorrect": "状态变更时间正序", "statusReverse": "状态变更时间倒序", "waitApproval": "待审核", "doneApproval": "已完成", "all": "全部", "error": "异常", "schemeCreateAt": "数据上传时间", "schemeDoneAt": "处理完成时间", "schemeApprovalAt": "方案完成时间", "linkOrgCounts": "关联机构数", "create": "创建", "save": "保存", "reviewerManageOrg": "请选择审核员要管理的机构", "firstName": "姓", "lastName": "名", "joinReviewerManageOrg": "请选择参与管理此机构的审核员", "createUser": "创建用户", "editUser": "编辑用户", "accountInfo": "账号信息", "equipment": "第三方设备", "taskMonitor": "平台任务监控", "funcArea": "功能分区", "aparcAseg": "结构分区", "headRun": "头动曲线", "accessFirstAt": "首次请求时间", "accessLatestAt": "最近请求时间", "accessAt": "请求时间", "accessResult": "请求结果", "remark": "备注", "planProcessingCount": "处理中", "planComputeSuccessCount": "待审核", "planReviewedCount": "审核通过", "planReviewFailedCount": "处理异常", "planAbnormal": "异常", "planComputeFailedCount": "处理失败", "sequence": "序列", "failed": "失败", "success": "成功", "computed": "计算中", "scanName": "扫描名称", "exception": "异常信息", "scanException": "数据处理信息", "seqNumber": "序列号", "pCloud": "优 点 云"}, "template": {"TEMPLATEA": {"title": "抑郁症"}, "TEMPLATEB": {"title": "失语症"}, "TEMPLATEC": {"title": "运动障碍"}, "TEMPLATED": {"title": "自闭症"}, "TEMPLATEE": {"title": "常规"}, "TEMPLATEF": {"title": "帕金森"}, "TEMPLATEG": {"title": "脑瘫"}, "TEMPLATEH": {"title": "阿尔茨海默"}, "TEMPLATEI": {"title": "注意力缺陷多动障碍"}, "TEMPLATEJ": {"title": "运动障碍"}}, "semantic": {"correlation": "相关系数", "marker": "标记点"}}
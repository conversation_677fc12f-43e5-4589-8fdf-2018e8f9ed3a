{"app": {"mailToPre": "Please contact", "mailToLast": "for resolution", "restart": "<PERSON><PERSON>", "restartImmediately": "Restart Immediately", "close": "Power Off", "NeuralGalaxy": "Neural Galaxy", "NeuralGalaxyApp": "Neural Galaxy Client", "NeuralGalaxyFancy": "<b>N</b>eural<b>G</b>alaxy", "name": "Precise Functional Atlas", "version": "Version", "publishVersion": "Release Version", "uploadData": "Upload Data", "uploadTip": "You can choose to upload data in the form of a folder or a ZIP file, and you can group this uploaded data at the same time, which is very helpful for data analysis", "uploadSingleTip": "You can choose to upload data in the form of a folder or a ZIP file", "surgeUploadMsg": "If BOLD data exists in the uploaded data, please fill in the following information:", "uploadStartNotification": "Uploading status, If you leave this page, data is lost", "noData": "No Data", "noUploadData": "There is no data for this subject, click \"Upload Data\" button to upload", "refreshData": "Refresh Processing Data", "downloadImg": "Download Image", "dataAnalysis": "Data Analysis", "downloadData": "Download Data", "downloadTitle": "Data Download", "downloadLimit": "The file you want to download is too large, please use Neural Galaxy Desktop if you want to download.", "downloadTemplateData": "Download File Template", "downloadTemplateCont1": "Upload the completed Run information file", "downloadTemplateCont2": "Click on the area to select files", "selectDownloadCont": "Download Data", "uploadTitle": "Data Upload", "uploadLimit": "The file you want to upload is over 300MB, please use Neural Galaxy Desktop if you want to upload.", "clientDownloadTip": "Please use the Desktop App to download the data", "selectUploadCont": "Upload Data", "uploadFileMode": "Upload File Mode", "folder": "Folder", "local": "Local Folder", "compressed": "Compressed Folder", "uploadSingle": "Single Upload", "uploadMore": "Batch Upload", "nuclearMagnetic": "Data from MRI scanner push", "uploadAllRun": "Handle all runs in the scan data", "uploadCheckRun": "Process only selected runs from the scan", "uploadAllRunTask": "Uploaded scan data contain EVS directory and files", "uploadCheckRunTask": "Need to upload RunInfo file", "seeSubjectInfo": "View Subject Details ", "checkEmailApp": "Check the default email program Settings", "downloadJob": "To download the treatment results of the subject separately,", "click": "Click", "inprogress": "Transfer progress", "downloadAllData": "to download all data in a project or a subject", "viewSubjectDetail": " to go to the subject details page to download the subject itemized data", "downloadNoteTitle": "Download Prompt", "downloadNoteContent": "The data you selected is being downloaded", "uploadNoteTitle": "Upload Prompt", "uploadNoteContent": "The data you selected is being uploaded", "description": "NeuralImaging", "officialWebsite": "https://www.neuralgalaxy.com", "switchLanguage": "Switch Language to 中文", "switchApp": "Switch to {name}", "switchBoxServer": "Switch Server to Navigator Server", "switchCloudServer": "Switch Server to Cloud", "uploadStatusTooltip": "Upload Status", "downloadStatusTooltip": "Download Status", "shrinkage": "shrinkage", "homeTooltip": "Home", "uploadTooltip": "Upload Data", "uploadState": "Upload State", "downloadTooltip": "Download Data", "downloadState": "Download State", "analysisTooltip": "Data Anaylsis", "settingsTooltip": "Settings", "userTooltip": "User", "deleteSubjectTooltip": "Delete Subject", "cannotDeleteSubjectTooltip": "No Permission To Delete Subject", "editSubjectTooltip": "Edit Subject Information", "browserTip": "The current browser is not supported, please use Chrome or Firefox to use this product", "hour": "hour", "minute": "minute", "second": "second", "hours": "hours", "minutes": "minutes", "seconds": "seconds", "itemsPerPage": "/ page", "noConnent": "The network status is abnormal. Please check the network connection", "switchAppMsg": "Please wait until data transfer is complete to switch product!", "partitionTitle": "Group-level functional atlases", "downloadPdf": {"func": "Download anatomical locations of each functional partition (template at the current No. regions)", "bold": "Download anatomical locations of each BOLD partition (template at the current No. regions)"}, "viewPdf": {"func": "View anatomical locations of each functional partition (template at the current No. regions)", "bold": "View anatomical locations of each BOLD partition (template at the current No. regions)"}, "downloadPdfTitle": "Parcellation Template", "error": {"400": "Bad request", "404": "Hmm... What you are looking for isn't here", "403": "Oops... It seems that you are not authorized", "connection": "Connection error, please try again later", "serverError": "Internal server error, please try again later", "defendError": "In order to provide more rich, convenient and safe services, Neural Galaxy is maintaining the system.", "expired": "License Expired", "httpErrorCode": {"SV13004": "Username already exists", "PNT_RP10001": "无图文报告下载权限，请联系管理员开通", "PNT_RP10002": "该病症数据无法下载图文报告", "PNT_RP10003": "该病症数据无法下载图文报告", "PNT_P3409": "该方案已被申请新方案"}}, "booleanValue": {"true": "Yes", "false": "No"}, "buttons": {"userArgument": "User Argument", "close": "Close", "closeApp": "Shutdown System", "add": "Add", "back": "Back", "cancelUpload": "Cancel Upload", "upload": "Upload", "uploadFailed": "Upload Failed", "cancelDownload": "Cancel Download", "download": "Download", "downloadFailed": "Download Failed", "compress": "Compress", "viewProcessingResults": "View Processing Results", "edit": "Edit", "save": "Save", "saveAndExit": "Save Next Exit", "saveAndUpload": "Save Next Upload", "noSave": "Don't Save", "search": "Search", "eusure": "Confirm", "cancel": "Cancel", "deactivate": "Freeze", "active": "Active", "generatePwdToken": "Send Password Reset Token", "update": "Update", "restart": "<PERSON><PERSON>", "confirm": "Confirm", "filter": "Filter", "reset": "Reset", "manage": "Manage", "delete": "Delete", "agreement": "Agree", "recallOpera": "Cancel Changes", "filterOne": "From this scan", "filterMore": "From multiple scans", "previous": "Previous", "next": "Next", "tryAgain": "Try Again", "abort": "Abort", "triggerDesc": "Click sort by descend", "triggerAsc": "Click sort by ascend", "cancelSort": "Click to cancel sort", "perPage": "/ page", "items_per_page": "/ page", "prevPage": "Previous Page", "nextPage": "Next Page", "noFilters": "No filters", "selectionAll": "Select all data", "selectInvert": "Select current page", "use": "use"}, "desktop": {"paused": "Paused", "timeLeft": "Time Left", "downloadingFile": "Downloading", "uploadingFile": "Uploading", "downloadingErrorFile": "Failed", "uploadingErrorFile": "Failed", "downloadFileStopError": "Download operation is paused due to unstable network connectivity. Please check your network connectivity and then click \"Start\" button to resume download. (You have the opportunity to download failed files again once the current download operation finishes)", "uploadFileStopError": "Upload operation is paused due to unstable network connectivity. Please check your network connectivity and then click \"Start\" button to resume upload. (You have the opportunity to upload failed files again once the current upload operation finishes)", "showAll": "View All", "putAway": "View Less", "reUploadErrorFiles": "Reupload the wrong file", "reDownloadErrorFiles": "Re-download the wrong file", "viewProcessingResults": "View Processing Results", "downloadProjectData": "Download Project Data", "dataTransferStatus": "Data Transfer Status", "downloadAllSubjectData": "Download All Subject Data", "deleteAllSubjectData": "Delete All Subject Data", "downloadProcessingData": "Download Processing Data", "deleteProcessingData": "Delete Processing Data", "editSubjectInformation": "Edit Subject Information", "viewProcessingData": "View Processing Data", "downloadSelectedData": "Download Selected Files", "uploadDiskNotEnoughSpace": "To avoid upload failures, please ensure that the target drive has enough disk space and you have 'write access' to it", "downloadDiskNotEnoughSpace": "To avoid download failures, please ensure that the target drive has enough disk space and you have 'write access' to it", "transferDiskNotEnoughSpace": "The download task has been suspended due to insufficient storage space in the {path}, Please clean up the disk and confirm that there is enough space to continue downloading", "windowsTransferDiskNotEnoughSpace": "The download task has been suspended due to insufficient storage space in the {path} disk, Please clean up the disk and confirm that there is enough space to continue downloading", "uploadingTransferDiskNotEnoughSpace": "The upload task has been suspended due to insufficient storage space in the {path}, Please clean up the disk and confirm that there is enough space to continue uploading", "uploadingWindowsTransferDiskNotEnoughSpace": "The upload task has been suspended due to insufficient storage space in the {path} disk, Please clean up the disk and confirm that there is enough space to continue uploading", "showMoreFiles": "Show Advanced Files", "showDefaultFiles": "Show Default Files", "addSubject": "Add Subject", "chooseSubject": "Choose Subject", "uploadedFileName": "Uploaded File Name", "uploadTime": "Upload Time", "checkForUpdate": "Check For Update", "forceUpdateMsg": "Note: This update cannot be interrupted", "updateNow": "Update Now", "updateInfo": "New version is successfully downloaded.", "noUpdate": "There are currently no updates available", "updating": "Updating", "menuLearnMore": "Learn More", "aboutUs": "About Us", "menuContactUs": "Contact Us", "sureToDeleteSubject": " Are you sure  you want to delete  this subject?", "sureToDeleteJob": "Are you sure you want to delete this data?", "calculating": "Calculating", "downloadProgress": "Download Progress", "uploadProgress": "Upload Progress", "Transferring": "Transferring  ({ num })", "uploading": "Uploading ({ num })", "downloading": "Downloading ({ num })", "transferCompleted": "Transfer Completed ({ num })", "clearRecording": "Clear Recording", "transferCompletionIime": "Transfer completion time: ", "takeUpTime": "Take up time: ", "totalData": "Total data: ", "activation": "Start/Stop", "cancelTransfer": "Cancel Transfer", "clientMoreData": "Each data download in browser is limited to 1GB in total size, please use Neural Galaxy Desktop if you want to download more data than 1GB in size.", "clientDownloadInfo": "The total size of data you are trying to download is more than 1GB, please use Neural Galaxy Desktop to download data. ", "confirmInfo": "There is an unfinished transfer task. Do you want to start the transfer again?", "confirmTitle": "transfers", "downloadFilePath": "See Where The Files Are", "dataBeingProcessed": "The data is being processed, please check the processing result file later", "dataFaile": "Data processing failed, no processing result file was generated", "nodata": "No Data", "cantOpen": "Can't open file directory", "cantFindPath": "Can't find file directory", "updateReminderModal": {"newVersion": "New version discovered", "updateAnnouncement": "Release Note", "cancel": "Cancel", "updateNow": "Update Now", "exitApp": "Exit App", "noMorePrompts": "Do not remind again", "automaticRestart": "Automatic restart in", "updating": "Updating...", "updateError": "Failed to update, please try again.", "forceUpdateError": "Failed to update, please contact administrator.", "getUpdateInfoError": "Failed to obtain Information."}, "updatePreviewModal": {"newVersion": "New version discovered", "updateAnnouncement": "Release Note", "updateNow": "Update Now"}}}, "home": {"search": {"placeholder": "Please enter a project name or subject ID", "placeholderWithFile": "Please enter a file name or subject ID"}}, "layout": {"header": {"menuUpload": "Upload Data", "menuSubject": "Subject Database", "linkSettings": "Settings", "linkLogout": "Logout", "userPrompt": "User Name", "dropDownProject": "All", "downloadDesktop": "Download", "downloadWin": "Windows", "win32Bit": "Windows 32-bit", "win64Bit": "Windows 64-bit", "downloadLinux": "Linux", "downloadMac": "<PERSON>", "addOrg": "add Org", "downloadDesktopTip": "Download Desktop App", "downloadRule": "Minimum Hardware Requirements:", "downloadCpu": "Intel i5 CPU Dual Core or above", "downloadMemory": "8GB or more memory", "downloadDisk": "Minimum 300MB free space, 10GB free space recommended", "downloadVga": "Video card with minimum 64MB RAM and 3D support", "upload": "Upload", "download": "Download", "analysis": "Analysis", "accountSettings": "Account <PERSON><PERSON>", "myOrganization": "My Organization", "downloadDesktopApp": " Download Desktop App", "message": "Message", "connecctedCloud": "Connected to : Cloud Medical Image Data Processing Software", "connecctedBox": "Connected to : MRI Data Processing Software", "endPointWaringContent": "You have data transmission in the process right now. If you switch, the data transmission process will be terminated.", "endPointWaringTitletoCloud": "Are you sure to switch to Cloud Medical Image Data Processing Software?", "endPointWaringTitletoBox": "Are you sure to switch to MRI Data Processing Software?", "switchEndpointWarning": "Switch Endpoint Warning", "myAccount": "My Account"}, "notification": {"notification": "Notification", "downloadSuccessfully": "Automatic Download Successfully", "downloadFailed": "Automatic Download Failed", "dataId": "Data ID", "fileName": "File Name", "downloadIPAddress": "Download IP Address", "reDownload": "Re-Download", "boxDetail": "View Scan Data for Details", "cloudDetail": "The processing result of this scan data is in Cloud Medical Image Data Processing Software", "deleteTip": "Delete Current Notification"}, "settingNav": {"linkProfile": "Profile", "linkSystem": "System", "linkOrganizations": "Organizations", "linkProjects": "Projects", "linkUsers": "Users"}, "footer": {"version": "Version", "copyright": "Copyright &copy; 2020&nbsp;", "reserved": "&nbsp;All rights reserved."}}, "register": {"message": "Register your Neural Galaxy account:", "resetpwdmessage": "Reset your Neural Galaxy password", "form": {"firstNameFieldName": "First Name", "firstNamePlaceHolder": "Please enter first name", "lastNameFieldName": "Last Name", "lastNamePlaceHolder": "Please enter last name", "userNamePlaceHolder": "Please enter user name, The first digit must be a letter", "usernameRepeat": "Username already exists", "uniqueIdRepeat": "唯一标识已存在", "passwordFieldName": "Password", "passwordPlaceHolder": "Please enter password", "confirmPasswordFieldName": "Confirm Password", "confirmPasswordPlaceHolder": "Please confirm password", "buttonRegister": "Register", "buttonResetPwd": "Reset Password", "userName": "Username", "email": "E-Mail: ", "contactSupport": "contact support", "waringDesktopVersion": "The current version of the desktop is not compatible with Cloud Medical Image Data Processing Software. Please upgrade the desktop to {version} version", "waringBoxversion": "The current version of MRI Data Processing Software is incompatible with the Cloud Medical Image Data Processing Software version and cannot be switched."}, "updateUserNameModal": {"title": "Please enter user name", "des": "The current account does not have a user name. Please set a user name. You can use the user name to log in again when logging in. The password is the same as the login password in the mailbox."}, "error": {"firstNameRequired": "First name is required", "firstNameNotMatch": "First name is incorrect, 1-10 digits/letters/chinese/space/_ /-", "lastNameRequired": "Last name is required", "lastNameNotMatch": "Last name is incorrect, 1-10 digits/letters/chinese/space/_ /-", "passwordLength": "Password must be between 8 and 20 characters", "passwordRequired": "Password is required", "passwordNotMatch": "Passwords don't match", "userNameRequired": "Username is required", "userNameNotMatch": "Username is incorrect,3-20 characters, Numbers, English, underscore, dash are allowed.", "oldPwdTrue": "Please make sure the old password is entered correctly", "oldNotNew": "Please confirm that the old password is different from the new password"}, "invalidToken": "Access token is not found or expired. Please contact your administrator to (re)send you an invitation with access token", "loginInstruction": "If you already registered, ", "loginLink": "login.", "alreadyRegistered": "Already registered? Login", "success": "You have successfully registered with Neural Galaxy.", "resetPwdSuccess": "You have successfully reset your password", "loginLink2": "<PERSON><PERSON>"}, "reqPwdResetToken": {"message": "Please enter your email address you register with us below. An email will be sent to you with instructions to reset your password", "button": "Send", "loginLink": "<PERSON><PERSON>", "emailNotFound": "Email not found, please contact your administrator.", "success": "An email with instructions has been sent to you. Please follow the instructions to reset your password", "error": "Reset password link failed to send. Please try again later."}, "login": {"adminMessage": "Administrator <PERSON><PERSON> ", "message": "Sign in", "staySignedIn": "&nbsp;Stay signed in", "forgotPassword": "Forgot Password", "licenseError": " Your license does not allow for  {app} app endpoint. To enable this capability, { contact }  to update your license. ", "contact": "contact support", "desktop": "desktop", "client": "client", "form": {"emailFieldName": "Email", "emailPlaceHolder": "Please Enter Email Address or UserName", "emailOnlyPlaceHolder": "Please Enter Email Address ", "accountPlaceHolder": "Please enter atlas box admin account", "account": "Account", "passwordFieldName": "Password", "passwordPlaceHolder": "Please Enter Password", "buttonLogin": "Log in", "choseEndpoint": "Connect to Endpoint", "cloud": "Cloud Medical Image Data Processing Software", "box": "MRI Data Processing Software", "boxSystem": "MRI Image Data Processing System"}, "error": {"validEmail": "Email is not valid email", "emailRequired": "Email or UserName is required", "adminAccoutRequired": "Atlas box administrator account is required", "passwordRequired": "Password is required", "authFailed": "The account or password you’ve entered is incorrect. Please contact your administrator to reset your password if you forgot your password.", "sessionTimeout": "Session timed out, please login again", "offline": "Network connection error", "networkError": "Network Error", "timeout": "Network request timed out"}}, "kiosk": {"resetPassword": "Please go to the following url to reset password: ", "emailPrompt": "Please send <NAME_EMAIL>"}, "account": {"orgianzation": "Organization", "username": "Username", "name": "Name", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "password": "Password", "userConfig": "User Configuration", "originalPassword": "Please enter the original password", "newPassword": "Please enter the new password", "reconfirmNewPassword": "Reconfirm the new password", "pwdPlaceholder": "8-20 characters,must contain upper and lower case letters and Numbers", "projects": "Projects", "role": "Role", "status": "Status", "table": {"projectName": "Project Name", "myRole": "My Role", "projectAdmins": "Project Admins", "subjects": "Subjects", "scans": "Scans", "groups": "Groups", "plans": "Plans", "status": "Status", "sync": "Project Info Sync", "automatic": "Automatic Download"}}, "settings": {"org": {"title": "Organization", "btnNew": "New Organization", "orgDetail": "<PERSON><PERSON>", "orgFormPageTitle": "Organization Information", "orgMembers": "Org Members", "form": {"nameFieldName": "Name", "namePlaceHolder": "Organization name", "contactEmailFieldName": "Contact Email", "contactEmailPlaceHolder": "Organization contact email", "isActiveFieldName": "Status", "valueActive": "Active", "valueInactive": "Inactive", "nameRequired": "Organization name is required", "nameRepeat": "Duplicate Organization name ", "contactEmailRequired": "Organization contact email is required", "validContactEmail": "Invalid email"}, "orgCreated": "Organization Created", "orgUpdated": "Organization Updated", "list": {"searchPlaceholder": "Search organization", "noDataIndicator": "There is no organization, click\"New Organization\" to create"}, "raList": {"orgRaListTitle": "Advisor Association", "removeOrgRaList": "Remove Advisor Association", "searchPlaceholder": "Search Advisor"}}, "project": {"title": "Projects", "taskTitle": "Task Design", "btnNew": "New Project", "projectFormPageTitle": "Project Information", "newProjectFormPageTitle": "New Project Information", "projectMemberPageTitle": "Project Members", "emptyTaskDesign": "There is currently no task design, click the button to create", "emptyCondition": "There is currently no task condition, click the button to create", "choicePrjectForJob": "Select Project to Download Scan Data", "refreshTip": "Refresh Project", "editTip": "Edit Project Information", "uploadTip": "Upload Data", "downloadTip": "Download Data", "downloadDataImgTip": "Download image and data", "form": {"taskName": "Project Type", "orgIdFieldName": "Organization", "nameFieldName": "Name", "namePlaceHolder": "Project name", "contactEmailFieldName": "Contact Email", "contactEmailPlaceHolder": "Project contact email", "isActiveFieldName": "Status", "valueActive": "Active", "valueInactive": "Inactive", "nameRequired": "Project name is required", "contactEmailRequired": "Project contact email is required", "validContactEmail": "Invalid email", "orgIdRequired": "Organization is required", "orgIdPlaceHolder": "Select organization", "timeofRepetition": "Scan TR (s)", "skip": "No. Frame<PERSON> to Skip", "timeofRepetitionRequired": "Repeat time must be a number between 0.2 - 10 seconds", "skipRequired": "No. Frames to <PERSON><PERSON> must be a non-negative integer, maximum length of 10", "taskOptions": {"resting": "Resting", "task": "Task", "tip": "Please Choose Project Type", "restingAndTask": {"title": "Resting/Task", "subTitle": "What runs should be processed as Resting run:", "allRun": "All Runs", "restingState": "Runs Marked as Resting"}}}, "newTask": {"createTitle": "Please Create Tasks", "createButton": "Tasks", "name": "Name", "description": "Description", "title": "Task Design", "addTask": "Add Task Design", "condition": "Conditions", "selectConditions": "Please select the condition", "startTime": "Start Time (s)", "durationTime": "Duration (s)", "startTimePH": "e.g. 2.5", "durationTimePH": "e.g. 3.6", "tip": "Task conditions cannot be empty, please complete", "timeTip": "The start time must be greater than or equal to 0, and the duration must be greater than 0", "taskConditionsEmpty": "Task condition cannot be empty", "deleteTitle": "Click \"Confirm\" To Delete ", "nameEmpty": "Task name cannot be empty", "deleteSubtitle": " is being used in the following tasks. These tasks will be deleted too if you delete the condition.", "namePlaceholder": "Up To 20 Alphanumeric Characters, <PERSON> \"-\" or Underscore \"_\""}, "newConditions": {"nameEmpty": "Task condition name cannot be empty", "createTitle": "Please Create Conditions First", "title": "Condition", "condition": "Name", "addCondition": "Add Condition", "nameRepeat": "Duplicate Condition Name", "namePlaceholder": "Up To 10 Alphanumeric Characters", "description": "Description", "desPlaceholder": "Please Enter a Description..."}, "projectCreated": "Project Created", "projectTypeError": "Please select the project type", "projectUpdated": "Project Updated", "list": {"searchPlaceholder": "Search project", "noDataIndicator": "There is no project, click\"New Project\" to create"}}, "user": {"title": "Users", "btnNew": "Invite Users", "raUserOrgPageTitle": "Orgs Association", "userFormPageTitle": "User Information", "userProjectMemberPageTitle": "Projects Association", "inviteUsersTitle": "Invite Users", "inviteUserInput": {"inputPrompt": "Input a list of emails separated by comma(,) / space( ) / semicolon(;) / or pipe(|). Maximum: 50", "duplicateMessage": "The following emails are duplicate", "moreThanMaxMessage": "There are too many emails", "asyncValidationFailMessage": "The account shown in the list has been invited, please change another email", "invalidFormatMessage": "Invalid emails", "validateButton": "Add Users", "removeAllButton": "Remove All", "submitButton": "Invite", "maxItemTip": "Maximum: 50 emails"}, "userRole": {"projectAdminOrMemberFieldLabel": "Project Admin/Member", "ngRAFieldLabel": "Advisor", "pointNgRAFieldLabel": "Point Advisor", "ngRAjoinOrgTittle": "Please select organizations to which you want to add this advisor", "pointNgRAjoinOrgTittle": "Please select organizations to which you want to add this advisor", "orgFieldLabel": "Organization", "orgFieldPlaceholder": "Select organization", "projectFieldLabel": "Project", "ngAdminCannotBeNGRA": "System Admin cannot be a Advisor", "projectFieldPlaceholder": "Select project", "ngAdminFieldLabel": "System Admin", "orgAdminFieldLabel": "Organization Admin", "projectAdminFieldLabel": "Project Admin", "projectMemberFieldLabel": "Project Member", "selectedProjectTip": "Select project as project member, tick as project manager", "ngAdminCannotBeOrgAdmin": "System Admin cannot be an Organization Admin", "ngAdminCannotHaveOrg": "System Admin cannot be assigned to an organizations", "ngAdminCannotBeProjectAdmin": "System Admin cannot be a Project Admin", "ngAdminCannotHaveProject": "System Admin cannot be assigned to a project", "orgAdminMustHaveOrg": "Organization Admin must be assigned to an organization", "projectAdminMustHaveOrg": "Project Admin must be assigned to an organization", "projectAdminMustHaveProject": "Project Admin must be assigned to a project", "userMustHaveOrg": "Regular user must be assigned to an organization", "userMustHaveProject": "Regular user must be assigned to a project", "ngRACannotBeOrgAdmin": "Advisor cannot be an Organization Admin", "ngRACannotHaveOrg": "Advisor cannot be assigned to an organizations", "ngRACannotBeProjectAdmin": "Advisor cannot be a Project Admin", "ngRACannotHaveProject": "Advisor cannot be assigned to a project"}, "form": {"nameFieldName": "Name", "roleFieldName": "Role", "orgIdFieldName": "Organization", "projectIdFieldName": "Project", "firstNameFieldName": "First Name", "firstNamePlaceHolder": "First name", "lastNameFieldName": "Last Name", "actionPoint": "Action TMSPoint", "lastNamePlaceHolder": "Last name", "emailFieldName": "Email", "emailPlaceHolder": "Email", "isActiveFieldName": "Status", "valueActive": "Active", "valueInactive": "Inactive", "adminLabel": "(Administrator)", "NGAdminLabel": "System Administrator", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "emailRequired": "Email is required", "emailNotAvailable": "Email is not available", "emailAvailable": "Email is available", "cannotCheckEmailAvailability": "Cannot check email availability at the moment", "validEmail": "Invalid email", "orgName": "Organization Name"}, "userUpdated": "User Updated", "profileUpdated": "Profile Updated", "tokenSent": "Password reset token is sent", "list": {"searchPlaceholder": "Search user", "noDataIndicator": "There is no user, click\"Invite Users\" to invite users", "editUserLink": "Edit"}}, "projectMember": {"isMemberHeader": "Member", "projectMemberHeader": "Project Member", "isAdminHeader": "Administrator", "touchedHeader": "Edited", "adminHasToBeInProject": "Project adminstrator has to be in the project", "userProjectPrompt": "Manage user's projects. Make necessary changes to 'Member' and 'Admin' columns by clicking on the Yes/No value and check/unckeck the checkbox. Once you are satisfied with the results, click 'Save'", "projectMemberPrompt": "Manage project's members. Make necessary changes to 'Member' and 'Admin' columns by clicking on the Yes/No value and check/unckeck the checkbox. You can also add new members by click on 'Add' button. Once you are satisfied with the results, click 'Save'", "pointProjectMemberPrompt": "Manage project's members. Make necessary changes to 'Member' and 'Admin' columns by clicking on the Yes/No value and check/unckeck the checkbox. You can also add new members by click on 'Add' button. Once you are satisfied with the results, click 'Save'", "userHasNoProject": "User is not in any project", "projectHasNoUser": "Project does not have any user", "noUserIndicator": "No additional user to be added", "memberHeader": "Member", "adminHeader": "Administrator", "noProjectAdmin": "Please set at least one user as administrator", "notModifySelf": "Cannot edit myself"}, "templateManagement": {"modalTitle": "模板管理", "importBtnTitle": "导入", "confirmDeleteTitle": "确定删除模板？", "alreadyExists": "模板管理器中已存在“{name}”模板， 是否替换？", "deleteFailed": "删除模板失败", "checkFailed": "导入失败，模板文件格式错误。", "uploadFailed": "导入失败，模板文件格式错误。"}}, "scanGroup": {"list": {"name": "Group Name", "description": "Description", "action": "Action", "action2": "View Data Group Info"}, "hidePointList": "默认隐藏患者和任务列表", "clearFilter": "Clear Filters", "subject": "Subjects", "groupListTitle": "Data Groups", "newButton": "New", "newGroup": "Create Group", "selectJobs": "Select Data", "addJobs": "Add Data", "preTitle": "Group", "newGroupDes": "The current project does not group data, please click the button", "selectProjectJobs": "Which Scans You Want to Download Data From", "selectDataErrorMsg": "The selected data has no processing result file available for download.", "uploadAddGroup": "The data group is very helpful for the analysis of the data, please select / enter the group name this data group", "newGroupFrom": {"title": "Group name", "description": "Description", "modalHeader": "New Group", "editHeader": "Edit Group", "titlePlaceholder": "Some common data set names for reference: reference group / control group", "descriptionPlaceholder": "Please enter a description for the group", "nameEmpty": "Group name cannot be empty", "nameRepeat": "This name already exists, please re-enter", "nameError": "Only letters, numbers,-, _, @, space,Chinese are allowed"}, "noJobs": "This group contains no data. Click to add data.", "noCurrentGroup": "Select a group to see its data", "deleteGroup": {"warnningTitle": "Are you sure to delete this data group ?", "warnningDes": "Delete this group, the corresponding data will be undivided."}, "editCard": {"selectedGroup": "Selected Group", "tipEnter": "Enter the name of the new group and press 'Enter' to create", "tipSelected": "Please select a group below"}, "customGroup": "Newly created custom group", "scangroupJobs": {"allData": "All Data Groups", "selectData": "Selected Data Groups ({ num })", "allScanData": "All Scan Data", "selectScanData": "Selected Scan Data ({ num })", "searchPH": "Please enter a file name or subject ID", "table": {"fileName": "File Name", "subject": "Subject ID", "status": "Status", "anaysislyStatus": "Used in analysis", "createdAt": "Created At", "createdDate": "Created At", "finishedAt": "Finished At", "filterHourDate": "within { num } hour", "filterDaysDate": "within { num } days", "startTime": "From", "endTime": "To", "endLessStartError": "End time cannot be earlier than start time", "today": "Today", "group": "Group"}}}, "subject": {"btnNew": "New Subject", "subjectBe": "Subject already exists", "subjectBeFill": "Use existing information", "subjectCreated": "Subject Created", "subjectUpdated": "Subject Updated", "requery": "Try  again", "searchSubject": "Search Subjects", "subjectInfoTip": "Click to view all processing results under this subject", "processingResults": "Processing Results", "downloadFiles": "Download Files", "downloadAllFiles": "Download All Files", "uploadedFiles": "Uploaded Files", "uploadNotAllowed": "Upload Not Allowed", "subjectToTeTransfered": "Subjects pending transfer", "subjectBeingTransfered": "Subjects being transfered", "failedTransfered": "Files failed to transfer", "transferStatus": "Transfer Status", "beingTransfered": "Files pending transfer", "subjectCustId": "Subject: ", "mySubject": {"title": "My Subjects"}, "dates": {"createdAt": "Created At", "startedAt": "Start Time", "finishedAt": "Finish Time", "readyAt": "Uploaded At"}, "rotateToggle": {"on": "On", "off": "Off"}, "laterality": {"latLeft": "Left Advantage", "latBoth": "Bilateral", "latRight": "Right Advantage"}, "lateralityType": {"latLanguage": {"func": "Language Laterality Index ", "bold": "BOLD Asymmetry Index "}, "latMemory": "Memory Laterality Index (-1 ~ +1):", "latFormula": "(LH-RH)/(LH+RH) (-1 ~ +1)"}, "helpDocument": {"reference": "References", "volumeOperTips": "Shift-click to drag volume viewer image", "parcellation": "Parcellation", "languageLaterality": "Language Laterality", "SNR": "SNR", "motionCorrection": "Motion Correction", "ACC": "ACC", "PCC": "PCC", "Motor": "Motor", "vmPFC": "VMPFC", "anatomicalParcellation": "Anatomical Parcellation", "corticalThickness": "Cortical Thickness", "sulcalDepth": "<PERSON><PERSON>", "myelin": "<PERSON><PERSON>"}, "upload": {"helpPdf": "Guide to Neural Galaxy Data Upload", "downloadHelpPdf": "Download Guide to Neural Galaxy Data Upload", "viewHelpPdf": "Data Upload Guide", "refreshWarning": "Please do not refresh the page, otherwise you will lose the information you've already entered", "noDataIndicator": "there is no input for the subject yet", "uploadStatus": "Input Status", "logicalName": "Logical Name", "uploadRules": "The names of the upload directory, sub-directory and files can only contain ASCII characters.", "selectScanData": "Select Scan Data to Upload", "uploadRunInfo": "Upload Run information File", "uploadResult": "Run information file check result display", "selectUploadType": "Choose upload type", "support": "For a better upload experience, try", "desktopApp": "Desktop App", "preUploadWarning": "Please fill in the contents of the Run information file and upload the Scan Data", "downloadTemplate": "Download Run Information Template V{version}", "restingTip": "Run information files can specify how different Runs should be processed, and should be treated as resting or skipped", "taskMainTip": "The Run information file can specify how different Runs should be processed, which should be treated as resting state, task state, or even skipped", "taskTip": "In a task project, you should specify the conditions, start time, and duration of each condition for each run", "restingError": "The scan file is not marked in the current file to be processed in rest mode, please fill in the Run information again", "reupload": "Reupload the file", "runNumber": "Run Number", "runNumberError": "Run Number must consist of three digits", "evs": "EVS", "conditionError": "Please modify the conditions to match the conditions in the project", "condition": "Condition", "start": "Start", "duration": "Duration", "uploadedAllError": "Please fill in the local position of all task data", "uploadedAllInfo": "Click on the \"EVS\" field and select the local location of the file", "uploadedEmptyError": "The content of the Run information file is empty, please fill in and upload again", "confirm": "Confirm", "uploadZipText": "Drag & Drop a single compressed file into the area below, or click on the area below to select compressed file", "uploadZipSubText": "only Zip format is supported now", "uploadRunInfoTextOne": "Upload the completed Run information file", "uploadRunInfoTextTwo": "Drag & Drop the file to this area or click the area to select the file", "uploadGroupLabel": "Group upload data：", "uploadNoteLabel": "Add a note for the uploaded data：", "allowedUploadNumber": "Number of scans remaining for upload this month: {numberRemaining}", "delDicom": "Are you sure to delete these files?", "all": "All"}, "job": {"jobStatus": "Processing Status", "errorInfo": {"header": "Processing Job Exception Info", "colSeverity": "Severity", "colFile": "File Path", "colMsg": "Message"}, "errorInfoMsg": {"discover_nifti": "Cannot find file. Possible reasons include incorrect file structure, or data was not uploaded successfully", "check_nifti": "Incorrect", "anat_reorient": "Anatomical reorientation was not successful", "bold_reorient": "BOLD reorientation was not successful", "reorient_qc": "MRI quality control was not successful", "recon1": "Recon-all step 1 was not successful", "recon2_lh": "Recon-all step 2  left hemisphere was not successful", "recon2_rh": "Recon-all step 2 right hemisphere was not successful", "recon3": "Recon-all step 3 was not successful", "preprocess": "Preprocess was not successful", "preprocess_qc": "Preprocess quality control was not successful", "parcellate": "18 network parcellation was not successful", "functional_connectivity_step": {"func": "18 network functional connectivity was not successful", "bold": "18 network BOLD correlation was not successful"}, "parc92": "92 region parcellation was not successful", "functional_connectivity_parc92": {"func": "92 region functional connectivity was not successful", "bold": "92 region BOLD correlation was not successful"}, "parc152": "152 region parcellation was not successful", "functional_connectivity_parc152": {"func": "152 region functional connectivity was not successful", "bold": "152 region BOLD correlation was not successful"}, "parc213": "213 region parcellation was not successful", "functional_connectivity_parc213": {"func": "213 region functional connectivity was not successful", "bold": "213 region BOLD correlation was not successful"}, "tms_target": "TMS processing was not successful", "tms_target_xw_aphasia": "TMS processing was not successful", "project_T1_template": "T1 template projection was not successful", "project_residuals_2mm_fs_mni": "Residual projection was not successful", "volumetric_seed_atlas": "Volumetric functioinal connectivity was not successful", "snm_task": "Task fMRI processing was not successful"}, "syncJob": "Synchronization", "syncJobFaild": "The synchronization failed", "downloadJobPrompt": "Download processed results", "clientDownloadJobTip": "Unable to download the experimental data, please download the deskTop", "rerunJobPrompt": "Process Data Again", "note": "Note", "showJobInfoPrompt": "Show Warnings And Errors During Data Processing", "deleteJobPrompt": "Delete Data", "cannotDeleteJobPrompt": "No Permission To Delete Data", "cannotDelRunning": "Processing, cannot delete", "cannotDel": "Cannot Delete", "noAuthcannotDel": "You don't have access to delete", "modifyTaskPrompt": "Modify data task design", "noRerunJobPrompt": "Wait Until After This Processing Finished To Rerun Processing", "noModifyTaskPrompt": "Data task configuration is not needed", "noShowJobInfoPrompt": "No Warning Or Error In This Processing", "descriptOfTasks": "Task Design of Runs", "rerunJobConfirm": "Click \"Confirm\" to reproess for the data", "task": {"title": "Task Detail", "errorType": "Error", "resting": "resting", "task": "Task", "addTask": "Add Task Design", "condition": "Conditions", "startTime": "Start Time (s)", "durationTime": "Duration (s)"}}, "invalidFileReason": {"10": "mri_info fails to get info from the provided nifti file", "20": "OS system files", "30": "Discovered raw nifti files, skipping non-raw-nifti files"}, "list": {"searchPlaceholder": "Search subjects", "noDataIndicator": "There is no subject available, click \"New Subject\" to create", "viewSubjectResultLink": "Detail"}, "notes": {"newNotePlaceholder": "Please type notes", "textNote": "Text Note", "imgNote": "Image Note"}, "subjectFormPageTitle": "Fill In Subject Information", "subjectScanPageTitle": "MR Sessions", "subjectPageTitle": "Subject Data", "subjectJobPageTitle": "Result Files", "form": {"ageFieldName": "Birth Year", "genderFieldName": "Gender", "genderPlaceHolder": "Gender", "birthYearFieldName": "Birth Year", "birthYearPlaceHolder": "Birth Year", "invalidBirthYear": "Invalid Birth Year", "handednessFieldName": "Handedness", "handednessPlaceHolder": "Handedness", "handednessScoreFieldName": "Handedness Score", "handednessScorePlaceHolder": "Handedness Score", "invalidHandednessScore": "Invalid Handedness Score, (Between -100 and 100)", "validHandednessScorePrompt": "An integer between -100 and 100", "ethnicityFieldName": "Ethnicity", "ethnicityPlaceHolder": "Ethnicity", "occupationFieldName": "Occupation", "occupationPlaceHolder": "Occupation", "educationFieldName": "Education", "educationPlaceHolder": "Education", "maritalStatusFieldName": "Marital Status", "maritalStatusPlaceHolder": "Marital Status", "projectIdFieldName": "Project", "projectIdPlaceHolder": "Project", "newSubjectPageTitle": "Upload Data", "subjectCustIdFieldName": "Subject ID", "subjectCustIdPlaceHolder": "Subject ID", "subjectCustIdRequired": "Please indicate Subject ID", "subjectCustIdNotMatch": "Subject ID does't match what we have on record", "invalidSubjectCustId": "3-18 characters, can use letters, numbers, -, _, such as (a-4)", "specialCharactersNotMatch": "Input special characters are not allowed", "projectIdRequired": "Project is required", "notes": "Notes", "latestJobStatus": "Latest Job Status", "latestJobCreatedAt": "Latest Job Created At", "latestJobFinishedAt": "Latest Job Finished At"}, "newSubjectWizard": {"run": "Run", "addRunFile": "Add Run(s)", "selectTaskDesign": "Please select task design", "runTypeError": "At least one run is task run.", "addRunError": "Task design of run(s) cannot be empty.", "runMatchError": "Run match error, Please try again", "nextButton": "Next", "backButton": "Previous", "finalNextButton": "Start Job", "stepVerify": "Verify Subject", "stepInput": "Upload Data", "stepJobStatus": "Job Status", "verifyInstruction": "Please input subject ID for verification purpose", "inputInstruction": "Drag & Drop a single input file into the area below, or click on the area below to select input file", "jobStatusInstruction": "The page will refresh to show the job status. Or you can click 'Subject overview' to go to subject page", "linkSubject": "Subject overview", "filenameLabel": "File name", "jobStatusLabel": "Job Status", "buttonCheckStatus": "Check Job Status", "fileNameError": "Uploaded file name can only be English, numbers, underscores", "eusure": "Eusure Create"}, "joberror": {"errorTitle": "Modify Task Design", "errorTip": "The number of runs specified does not match number of runs found in the data"}, "runAdditionalOptions": {"excludedRun": "Error Run", "restingRun": "Resting Run"}, "fcJobStatus": {"30": "Failed", "40": "Success"}, "jobStatus": {"0": "Created", "5": "Submitted", "10": "Queued to start", "20": "Running", "30": "Failed", "40": "Completed", "50": "Unconfirmed", "400": "Sync Failed", "500": "Sync Pending", "1010": "Processing", "1020": "Processed", "1030": "Failed", "1040": "Ready"}, "errorSeverity": {"0": "Info", "10": "Warning", "20": "Error"}, "errorPrompt": "View processing failure reason", "errorCode": {"opreview_rejected": "患者信息审核未通过", "empty_file": "Empty folder", "bad_file": "Invalid data file", "user_recon_result_missing_file": "Missing necessary recon files in user reconall zip", "bad_orientation": "Invalid orientation file", "ignored_file": "Ignoring common hidden file", "skipframes_error": "Skipframes of BOLD run is more than total frames", "run_info_not_match_data": "Run numbers in RunInfo file or EVS directory do not match the run numbers in uploaded data", "not_raw_nifti": "Found raw NIFTI files, ignoring this data file", "job_failed": "Encountered processing errors", "query_failed": "Error fetching subject data", "not_checked_download_files": "No files to download were selected", "missing_file_dir": "Missing file directory", "inconsistent_trs": "Inconsistent TRs in runs", "bad_tr": "Invalid TR in NIFTI files. TR should be between 0.2s and 10.0s", "bad_tr_json": "Bad TR in Project configuration. TR should be between 0.2s and10.0s", "bad_tr_match": "TR not match", "inconsistent_dims": "Inconsistent dimensions in BOLD runs", "bold_frames": "The number of frames of BOLD run is less than 40", "bold_totaltime": "Total time of BOLD run is less than 120s", "internal_error": "Internal Error", "job_timeout": "Time out", "extra_anat": "Found more than one anat image", "no_bld_runs": "No BOLD runs", "no_task_runs": "No task runs in user config", "no_rest_runs": "No rest runs", "design_value_error": "Errors in task design value", "design_totaltime_error": "Task design time is longer than total time of the BOLD run", "no_good_task_runs": "Not found good task runs", "no_good_tr": "Not found good TR", "no_found_evs": "EVS directory does not exist", "not_supported_condition_encoding": "Cannot read EVS files, supported file formats are .txt or .csv with UTF-8, GB-2312 or ASCII encoding", "not_supported_condition_version": "Wrong EVS file version, please use the latest EVS template", "not_found_run_info_in_evs": "Empty EVS directory", "condition_no_match_with_project": "Conditions in EVS files are not defined in the project", "found_anonymous": "检测到敏感信息", "single_bold_size_exceeds_limit": "BOLD单个文件大小超出限制(NIFTI: 600MB, DICOM: 1500MB)，任务有可能会失败", "series_number_changed": "序列号被更改", "no_bold_file": "没有找到BOLD文件，请重新上传", "session_size_exceeds_limit": "BOLD总体文件大小超出限制(NIFTI: 800MB, DICOM: 2000MB)，任务有可能会失败", "abnormal_heatmap_value": "异常heatmap值", "using_default_target_candidate_region": "使用默认模板区域", "bad_hc_templ": "健康对照组数据缺失", "bad_age_gender": "患者年龄和性别信息缺失或错误", "wrong_bold_res": "BOLD分辨率超出限制，应大于1毫米小于5毫米", "multi_nii_in_one_dir": "同一文件夹下发现多个nifti文件", "unknown_file_format": "发现未知文件格式", "wrong_directory_hierachy": "用户未按规范命名文件夹", "fail_dicom_to_nifti": "DICOM转NIfTI失败", "no_raw_t1wi": "未发现有效的t1wi数据", "candidate_target_region_was_empty": "目标靶点区域为空", "missing_task_run": "未发现您定义的任务态功能像run，请重新设置或重新上传", "missing_rest_run": "未发现您定义的静息态功能像run，请重新设置或重新上传", "bld_pixdim_mismatch": "BOLD图像无效，不同run的pixdim差异超出限制0.1mm"}, "uploadStatus": {"0": "Pending upload", "10": "Uploaded", "20": "Invalid", "30": "Validated"}, "sideNav": {"linkOverview": "Overview", "linkJob": "Subject Data", "Parcellation": "Parcellation", "Laterality": {"func": "Laterality", "bold": "BOLD Asymmetry"}, "AnatFeatures": "Anatomical Features", "FuncFeatures": "Functional Features", "AMUTMSProject": "AHMU TMS", "XWHTMSProject": "Xuanwu TMS", "Intermediate": "Raw Data", "DataQC": "Data QC", "ConnectivityQC": "Connectivity QC", "TaskfMRI": "Task fMRI", "Task": "Task fMRI", "Functional": {"func": "Functional", "bold": "BOLD"}, "Anatomical": "Anatomical", "ScanInfo": "Scan Info", "AllFiles": "All Files", "QC": "QC", "AbnormDetect": "Abnormality Score"}, "menuTip": {"maxZoom": "Maximized View", "normalZoom": "Normalized View", "expandControl": "Expand Control Panel", "foldControl": "Fold Control Panel"}, "vizScanInfo": {"runNumber": "Run Number", "label": "Label", "frames": "Frames", "dimensions": "Dimensions", "time": "<PERSON>an Date", "runType": "Run Type", "unknown": "Unknown"}, "topTab": {"downLoadResult": "Download Abnormality Score Result", "volumetric": "Volume View", "surface": "Surface View", "surfaceNative": "Native Surface", "surfaceFS6": "FsAverage6 Surface", "surfaceFS4": "FsAverage4 Surface", "volumeNative": "Native Volume", "volume1mm": "MNI1mm Volume", "volume2mm": "MNI2mm Volume", "bold": "BOLD", "boldRaw": "Original Data", "boldRes": "Registration Result", "anat": "Anatomical", "corticalThickness": "Cortical Thickness", "sulcalDepth": "<PERSON><PERSON>", "myelin": "<PERSON><PERSON>", "rawInput": "Raw Input", "snr": {"func": "Signal-to-noise Ratio", "bold": "Signal-to-noise Ratio"}, "allMeanSnr": "Mean SNR of all runs : ", "meanSnr": "Mean SNR : ", "mc": "Motion Correction", "connMat": {"func": "Connectivity Matrix", "bold": "BOLD Correlation Matrix"}, "funcFeat": "Functional Features", "anatFeat": "Anatomical Features", "amuTmsProject": "AHMU TMS", "xwhTmsProject": "Xuanwu TMS", "taskfMRI": "Task fMRI", "statistics": "Statistics", "connectivity": "Connectivity", "intro": "Intro", "FsAverage4Surfac": "FsAverage4 Surface FC", "NativeSurface": "Native Surface FC", "FsAverage4SurfacTitle": "Group-level", "totalPeopleCount": "Total Sample Size", "age": "Age", "maleToFemale": "Sex Ratio", "male": "Male", "female": "Female", "NativeSurfaceTitle": "Native", "groupLevel": "Group-level", "native": "Native"}, "tableName": {"networkSize": "Network Size", "roiSize": "ROI Size", "roiCenterOfMass": "ROI Center of Mass", "meanCorticalThickness": "Network Mean Cortical Thickness", "meanSulcalDepth": "Network Mean Sulcal Depth", "icv": "Estimated Total Intracranial Volume", "amuTms": {"target": "TMS Target Coordinates", "patch": "TMS Patch Center Point", "distance": "Distance", "distanceText": "Center Point to Target:", "targetScreenshot": "TMS Target Screenshot"}, "xwhTms": {"target": "TMS Target Coordinates", "patch": "TMS Patch Center Point", "distance": "Distance", "distanceText": "Center Point to Target:", "targetScreenshot": "TMS Target Screenshot"}}, "tableHeader": {"corticalThickness": "Cort. Thick. (mm)", "sulcalDepth": "Sulc. Dep. (mm)", "size": "Size (mm2)"}, "scanInfo": {"TR": "TR", "TE": "TE", "res": "Voxel sizes", "resUnit": "mm", "flipAngle": "Flip angle", "trteUnit": "msec", "flipAngleUnit": "degrees"}, "result": {"artifactNotAvailable": "No processing results are available to view, please rerun the processing", "loading": "Loading", "loadingClickTip": "Results are loading, please operate after loading", "Network": "Network", "Region": "Region"}, "surfaceView": {"lateralLeft": "Lateral", "medialRight": "Medial", "lateral": "Lateral", "medial": "Medial", "superior": "Superior", "inferior": "Inferior", "anterior": "Anterior", "posterior": "Posterior"}, "connectivity": {"connectivity": "Connectivity", "analyses": "Analyses", "des": {"func": "Neural Galaxy supports point-and-click functional connectivity analysis based on any seed ROI of your choice", "bold": "Neural Galaxy supports point-and-click BOLD correlation analysis based on any seed ROI of your choice"}, "analysesDes": "Analyses for seed types below can be computed within 30 seconds. The result will be shown here inside this {connectivity} tab:", "offlineAnalysesDes": "Analyses for other types of seeds may take 10 minutes to compute. Please save these seeds as ROI and go to “{connectivity}” page to analyze functional connectivity:", "offlineAnalysesDesBold": "Analyses for other types of seeds may take 10 minutes to compute. Please save these seeds as ROI and go to “{connectivity}” page to analyze BOLD correlation:", "surface": {"anatomical": "A vertex from anatomical surface view", "functional": {"func": "A vertex from functional surface view", "bold": "A vertex from BOLD surface view"}, "region": {"func": "A functional region from surface view", "bold": "A BOLD region from surface view", "anat": "An anatomical parcel from surface view"}, "abnormal": "A vertex from functional surface view of abnormality index", "abnormalFs4": "A vertex from functional surface view of abnormality score"}, "volume": {"anatomical": "A voxel from anatomical volumetric view", "functional": {"func": "A voxel from functional volumetric view", "bold": "A voxel from BOLD volumetric view"}, "region": {"func": "A functional region from volumetric view", "bold": "A BOLD region from volumetric view", "anat": "An anatomical parcel from volumetric view"}}, "task": {"des": {"func": "From Task fMRI visualization, you can perform functional connectivity analysis by selecting a seed ROI in a point-and-click fashion.", "bold": "From Task fMRI visualization, you can perform BOLD correlation analysis by selecting a seed ROI in a point-and-click fashion."}, "analysesDes": "Connectivity analysis using any seed below will take about 10 minutes. Please selected the type of seed below, save seed as ROI and go to “{ connectivity }” page for connectivity analysis:", "analysesDesBold": "Correlation analysis using any seed below will take about 10 minutes. Please selected the type of seed below, save seed as ROI and go to “{ connectivity }” page for correlation analysis:", "option1": "A vertex from anatomical surface view", "option2": "A voxel from anatomical volumetric view", "option3": {"func": "A vertex from task fMRI native surface view", "bold": "A vertex from task fMRI native surface view"}, "option4": {"func": "A voxel from task fMRI native volumetric view", "bold": "A voxel from task fMRI native volumetric view"}, "option5": {"func": "A cluster region from task fMRI native surface view", "bold": "A cluster region from task fMRI native surface view"}, "option6": {"func": "A cluster region from task fMRI native volumetric view", "bold": "A cluster region from task fMRI native volumetric view"}, "option7": {"func": "A vertex from task fMRI FsAverage6 surface view", "bold": "A vertex from task fMRI FsAverage6 surface view"}, "option8": {"func": "A voxel from task fMRI MNI1mm volumetric view", "bold": "A voxel from task fMRI MNI1mm volumetric view"}, "option9": {"func": "A cluster region from task fMRI FsAverage6 surface view", "bold": "A cluster region from task fMRI FsAverage6 surface view"}, "option10": {"func": "A cluster region from task fMRI MNI1mm volumetric view", "bold": "A cluster region from task fMRI MNI1mm volumetric view"}}}, "statistics": {"tabs": {"metricsVolume": "Metrics (Volume)", "metricsVolumeLH": "Metrics (LH Volume)", "metricsVolumeRH": "Metrics (RH Volume)", "metrics": "Metrics (WB Surface)", "metricsLH": "Metrics (LH Surface)", "metricsRH": "Metrics (RH Surface)", "rOIsMapROIs": "Anatomical Details (WB Surface)", "rOIsMapROIsLH": "Anatomical Details (LH Surface)", "rOIsMapROIsRH": "Anatomical Details (RH Surface)", "rOIsMapROIsVolume": "Anatomical Details (Volume)", "rOIsMapROIsVolumeLH": "Anatomical Details (LH Volume)", "rOIsMapROIsVolumeRH": "Anatomical Details (RH Volume)", "aparcWB": "aparc(WB)", "aparcLH": "aparc(LH)", "aparcRH": "aparc(RH)", "a2009sWB": "a2009s(WB)", "a2009sLH": "a2009s(LH)", "a2009sRH": "a2009s(RH)", "aseg": "aseg"}, "table": {"taskClusterNo": "Cluster", "cerebralCortexArea": "Surf Area", "network_mean_cortical_thickness": "Mean Cortical Thickness", "network_mean_sulcal_depth": "Mean Sulcal Depth", "meanMyelinScore": "Mean Cortical Myelin", "minMyelinScore": "Min Cortical <PERSON>elin", "maxMyelinScore": "Max Cortical Myelin", "center": "Center", "anatomicalPatchName": "Anatomical Parcel", "percent": "Percent (%)", "std_myelin_score": "Std Cortical Myelin"}, "selectresolution": "Select No. Regions"}, "control": {"control": "Control", "seeds": "Recent Seeds", "viewControl": "View Control", "anatomicalTitle": "Anatomical - Surface View Control", "title": {"func": "Functional - Surface View Control", "bold": "BOLD - Surface View Control"}, "taskTitle": "Task - Surface View Control", "volumeTitle": {"func": "Functional - Volumetric View Control", "bold": "BOLD - Volumetric View Control"}, "startAutoPlay": "Start autoplay", "stopAutoPlay": "Stop autoplay", "anatVolumeTitle": "Anatomical - Volumetric View Control", "taskVolumeTitle": "Task - Volumetric View Control", "functionalConnectivityAnalysis": {"func": "Functional Connectivity Analysis", "bold": "BOLD Connectivity Analysis"}, "volumeView": "View", "colormap": "Color Map Style", "colormapDefault": "<PERSON><PERSON><PERSON>", "colormapSpectral": "Spectral", "colromapThermal": "Thermal", "nativeSpace": "Native Space", "1mmSpace": "MNI152 1mm Space", "native": "Native", "group": "Group", "1mm": "MNI1mm", "sliceShowAll": "Show All", "slicesNumber": "Number of slices", "slicesTitle": {"coronal": "Coronal Views", "axial": "Axial Views", "saggital": "Sagital Views"}, "surface": {"viewChange": {"autoRotate": "Auto Rotate", "grid": "Grid"}, "pointModal": {"visualization": "Visualization", "atlas": "Atlas", "putAway": "Collapse", "putUp": "Unfold", "title": "Pointer Mode", "titleRegion": "Functional connectivity between designated ROIs", "shortSurfRASXYZCoordinates": "<PERSON>,<PERSON>,<PERSON>(surfRAS):", "shortVolRASXYZCoordinates": "<PERSON>,<PERSON>,<PERSON>(volRAS):", "surfRASXYZCoordinates": "<PERSON>,Y,Z Coordinates(surfRAS):", "volRASXYZCoordinates": "<PERSON>,Y,Z Coordinates(volRAS):", "VertexNumber": "Vertex Index:", "corticalThickness": "Cort. T<PERSON>:", "zvalue": "Z-Value:", "clusterID": "Cluster", "clusterIDLabel": "Cluster:", "cluster": "Cluster", "sulcalDepth": "<PERSON><PERSON> Depth:", "parcelLabel": "Anatomical Parcel:", "connectParcelLabel": "<PERSON><PERSON> (aparc+aseg):", "connectFineParcelLabel": "<PERSON><PERSON> (aparc.a2009s+aseg):", "connectParcelLabelTip": "Anatomical parcellation(aparc+aseg)", "connectFineParcelLabelTip": "Anatomical parcellation(aparc.a2009s+aseg)", "parcelLabelTip": "", "abnormPatchLabelTip": "Functional Networks(18)", "abnormPatchLabel": "Func Networks(18):", "networkLabel": {"func": "Func Network:", "bold": "BOLD Network:"}, "networkLabelTip": {"func": "Functional Network", "bold": ""}, "regionLabel": {"func": "Func Region:", "bold": "BOLD Region:"}, "regionLabelTip": {"func": "Functional Region", "bold": ""}, "parcelAnatDetailLabel": "<PERSON><PERSON>:", "parcelAnatDetailLabelTip": "Anatomical Details", "taskAnatDetailLabel": "<PERSON><PERSON>:", "taskAnatDetailLabelTip": "Anatomical Details", "patchAnatDetailLabel": "<PERSON><PERSON>:", "patchAnatDetailLabelTip": "Anatomical Details", "networkAnatDetailLabel": "<PERSON><PERSON>:", "networkAnatDetailLabelTip": "Anatomical Details for Selected Network", "regionAnatDetailLabel": "<PERSON><PERSON>:", "regionAnatDetailLabelTip": "Anatomical Details for Selected Region", "myelinScore": "<PERSON><PERSON><PERSON>:", "anatomicalPatchName": "Anatomical Details:", "functionalConnectivityfor": {"func": "Functional Connectivity for Selected:", "bold": "BOLD Correlation for:"}, "vertex": "Vertex", "region": "Region", "network": "Network", "regionId": "Region ID", "navigate": "Navigate", "anatomicalDetailsforCluster": "Anatomical Details for", "anatomicalDetailsforNetwork": "Details for Network", "anatomicalDetailsforRegion": "Details for Region", "anatomicalPatchNameNetWork": "Anatomical Details for Selected Network:", "anatomicalPatchNameRegion": "Anatomical Details for Selected Region:", "selectVertexandRegion": "Select Vertex and Region", "selectVoxelAndRegion": "Select Voxel and Region", "regionText": "Click and hold the mouse to rotate the visualization; Click \"{ tip }\" to make selections.", "nativeT1": "T1 Coordinates(volRAS):", "mni1mm": "MNI1mm Coordinates(volRAS)", "mni1mmShort": "Coordinates(volRAS):", "voxel": "Voxel Coordinates", "voxelNumberLabel": "Voxel Coordinates(T1 Space):", "voxelNumber": "Voxel Coordinates(T1 Space)", "voxelNumberShort": "<PERSON><PERSON><PERSON> (T1 Space):", "voxelNumber1mm": "Voxel Coordinates (MNI1mm Space)", "voxelNumber1mmShort": "Voxel Coordinates (MNI1mm Space):", "patchId": "Patch ID", "patchName": "<PERSON> Name", "showSlices": "Show Slices", "selectSnapshot": "View Snapshot", "coronal": "Coronal", "axial": "Axial", "saggital": "Sagital", "anatomicalatlas": "Anatomical Atlas", "proportion": "Proportion", "clusterId": "Cluster ID", "clusterIdLabel": "Cluster:", "showThresholdRange": "Show Signals in Threshold Range", "thresholdRange": "Threshold Range", "selectConditions": "Select Contrast", "selectSpace": "Template Space", "taskSaveSelectedSeed": "Save Selected as Seed ROI", "regionHighlightSelected": "Highlight Selected Region", "networkHighlightSelected": "Highlight Selected Network", "parcelHighlightSelected": "Highlight Selected <PERSON><PERSON><PERSON>", "colorBar": "Color Bar", "snapshot": "4-Way", "4waysnapshot": "4-Way Snapshot", "8waysnapshot": "8-Way Snapshot", "nativeFs4": "Native FS4", "groupFs4": "Group FS4", "emptyVertet": "Please Select Vertex and Region", "patchCorticalThickness": "Cort. Thkns:", "patchCorticalThicknessTip": "Cortical Thickness(mm)", "patchSulcalDepth": "<PERSON><PERSON> Depth:", "patchSulcalDepthTip": "Sulcal Depth(mm)", "patchCorticalMyelin": "Cortical Myelin:", "patchAvgCorticalThickness": "Patch Mean:", "patchAvgCorticalThicknessTip": "Patch Mean Cortical Thickness(mm)", "patchAvgSulcalDepth": "Patch Mean:", "patchAvgSulcalDepthTip": "Patch Mean Sulcal Depth(mm)", "patchAvgCorticalMyelin": "Patch Mean:", "patchAvgCorticalMyelinTip": "<PERSON> Mean Cortical Myelin", "patchAnatDetails": "Anatomical Details:", "parcelAvgCorticalThickness": "<PERSON><PERSON><PERSON> Mean:", "parcelAvgCorticalThicknessTip": "Pa<PERSON>el Mean Cortical Thickness(mm)", "parcelAvgSulcalDepth": "<PERSON><PERSON><PERSON> Mean:", "parcelAvgSulcalDepthTip": "<PERSON><PERSON><PERSON> Mean Sulcal Depth(mm)", "parcelAvgCorticalMyelin": "<PERSON><PERSON><PERSON> Mean:", "parcelAvgCorticalMyelinTip": "<PERSON><PERSON><PERSON> Mean Cortical Myelin", "parcelAnatDetails": "Anatomical Details:", "networkAvgCorticalThickness": "Network Mean:", "networkAvgCorticalThicknessTip": "Network Mean Cortical Thickness(mm)", "networkAvgSulcalDepth": "Network Mean:", "networkAvgSulcalDepthTip": "Network Mean Sulcal Depth(mm)", "networkAvgCorticalMyelin": "Network Mean:", "networkAvgCorticalMyelinTip": "Network Mean Cortical Myelin", "networkAnatDetails": "Anatomical Details for Selected Network:", "regionAvgCorticalThickness": "Region Mean:", "regionAvgCorticalThicknessTip": "Region Mean Cortical Thickness(mm)", "regionAvgSulcalDepth": "Region Mean:", "regionAvgSulcalDepthTip": "Region Mean Sulcal Depth(mm)", "regionAvgCorticalMyelin": "Region Mean:", "regionAvgCorticalMyelinTip": "Region Mean Cortical Myelin", "regionAnatDetails": "Anatomical Details for Selected Region:", "tryAgainTip": "Please try again in 5 seconds", "volumeParcel": "Parcel Volume:", "volumeParcelTip": "Parcel Volume (mm³)", "parcelPercentICV": "Percent of ICV:", "parcelPercentICVTip": "Parcel Percentage of ICV", "saveVoxel": "<PERSON> <PERSON><PERSON><PERSON> as Seed", "saveVertex": "Save Vertex as Seed", "saveRegion": "Save Region as Seed", "surfaceInputVertexError": "The vertex you entered doesn't exist.", "line": {"Line": "Line", "Polyline": "Polyline", "length": "Length", "Coordinate": "Coordinate:", "Rectangle": "Polygon", "area": "Area", "startCoordinate": "Start Coordinate:", "Select": "Select", "Measure": "Measure"}, "anatomiacalParcel": "Anatomiacal Parcel", "connectivityVal": "FC Value", "Region": "Region", "Parcel": "<PERSON><PERSON><PERSON>", "AbnormalityScore": "Abnormality Score:", "nativeFcValue": "Native FC Value", "normalPeopleFcValue": "Normal People FC Value"}, "seeds": {"seed": "Seeds:", "viewConnectivity": "View Connectivity", "saveSeedROI": "Save Seed", "saveSeedROIForSel": "Save as Seed for Selected:", "scanID": "Data ID", "vertexNumber": "Vertex Index", "selectedVertexNumber": "Selected Vertex Index", "addSeed": "Add As Seed ROI", "addSeedSuccess": "Seed ROI saved successfully", "newSeedDes": "Seed ROI will be saved in a standard space in order to be applicable to different subjects. As a result, coordinates and vertex numbers will be transformed from native space to FS4 space.", "newVolSeedDes": "Seed ROI will be saved in a standard space in order to be applicable to different subjects. As a result, both coordinates and voxel coordinates will be transformed from native space to MNI2mm Space.", "newVolSeedDes1mm": "Seed ROI will be saved in a standard space in order to be applicable to different subjects. As a result, As a result, the parcellation patch of the current cluster ID will be transformed from MNI152 1mm space to MNI2mm Space.", "newVolSeedDesCluster": "Seed ROI will be saved in a standard space in order to be applicable to different subjects. As a result, the parcellation patch of the current cluster ID will be transformed from native space to MNI2mm Space.", "newVolSeedDesRegion": "Seed ROI will be saved in a standard space in order to be applicable to different subjects. As a result, the parcellation patch of the current region ID will be transformed from native space to MNI2mm Space.", "radius": "<PERSON><PERSON>", "namePlaceholder": "Please fill in the name of seed ROI", "name": "name", "description": "Description", "nameEmpty": "Name is required", "nameError": "Allowed characters are Numbers, English, Chinese, underscore, dash, comma, period, colon and space", "radiusPlaceholder": "0-100", "radiusEmpty": "Radius cannot be empty", "radiusError": "Only 0-100 numbers are allowed", "desPlaceholder": "please fill in the description of seed ROI", "resolution": "No. Regions", "resolutionNo": "ROI No.", "ROI": "ROI", "space": {"VertexFS4": "FS4 Surface", "VertexFS6": "FS6 Surface", "VertexNative": "Native Surface", "VoxelMNI1mm": "MNI1mm Volume", "VoxelMNI2mm": "MNI2mm Volume", "VoxelNative": "Native Volume", "ROI": "Region", "Anat": "Region", "TaskCluster": "Task fMRI Cluster"}, "type": {"Vertex": "Vertex", "Voxel": "Voxel", "Region": "Region", "anatRegion": "Anatomical Region", "funcRegion": "Functional Region"}, "func": {"func": "Func", "bold": "BOLD"}}, "countDown": {"countDownDes": "Please do not leave the current page, processing result will show up momentarily.", "countDownFailedDes": {"func": "Functional Connectivity processing failed.", "bold": "BOLD Connectivity processing failed."}, "taskCountDownDes": "Please do not leave the current page, processing result will show up momentarily.", "taskCountDownFailedDes": "Calculating failed"}, "task": {"surfacefs6": "Free Surfer FsAverage6 Surface Space", "surfaceNative": "Free Surfer Native Space", "volumTemplate": "MNI152  1mm Space"}}}, "viz": {"anatStatistics": {"from": "From", "to": "To", "anatomical_atlas": "Anatomical Atlas", "anat_patch_name": "Anatomical Parcel", "surf_area": "Surface Area", "surf_area_percent": "Surface Area Percent", "grayvol": "Gray Volume", "grayvol_percent": "Gray Volume Percent", "surf_mass_center": "Surface Mass Center", "mean_cortical_thickness": "Mean Cortical Thickness", "max_cortical_thickness": "Max Cortical Thickness", "min_cortical_thickness": "Min Cortical Thickness", "std_cortical_thickness": "Std Cortical Thickness", "mean_sulcal_depth": "Mean Sulcal Depth", "max_sulcal_depth": "<PERSON>", "min_sulcal_depth": "<PERSON>", "std_sulcal_depth": "Std Sulcal De<PERSON>h", "mean_myelin_score": "Mean Cortical Myelin", "min_myelin_score": "Min Cortical <PERSON>elin", "max_myelin_score": "Max Cortical Myelin", "std_myelin_score": "Std Cortical Myelin", "volume": "Volume", "volume_percent": "Volume Percent", "tipMessage": "End value cannot be less than start value", "No": "Network", "patchName": "Anatomical Parcel", "percentage": "Percent", "groupFirstValue": "Average Value (mm)", "groupFirstName": "Group Name", "groupSecondValue": "Average Value (mm)", "groupSecondName": "Group Name", "p": "Uncorrected P Value", "method": "Hypotheses-test Method", "atlas": "Atlas", "parcelName": "Parcel <PERSON>", "metric": "Metric", "mask": "Mask", "BonferroniCorrectedPValue": "<PERSON><PERSON><PERSON><PERSON> corrected P Value", "FdrCorrectedPValue": "FDR corrected P Value"}, "taskStatistics": {"clusterVolume": "Cluster Volume (mm³)", "maxZValue": "<PERSON>", "maxZValueVertexIndex": "Max ZValue Native Vertex", "meanZValue": "<PERSON>", "peakZValueNativeCoor": "Peak ZValue Native Coordinates", "NVoxels": "No. <PERSON><PERSON><PERSON>", "noVertice": "No. Vertice", "SpaceTypeMNI1mm": "MNI 1mm", "SpaceTypeNative": "Native"}, "viewer": {"download": "Download", "vertex": "Vertex", "clickInstuct": "Shift-click to get vertex infomation", "view": "View", "surface": "Surface", "volume": "Volume", "overlay": "No. Regions", "fileContent": "Files", "Acc": "ACC", "Pcc": "PCC", "Motor": "Motor", "VMPFC": "VMPFC", "comboCondition": "Combo Conditions", "Threshold": "<PERSON><PERSON><PERSON><PERSON>", "BeginThreshold": "Min", "EndThreshold": "Max", "BeginPValue": "Min", "EndPValue": "Max", "HidePos": "Hide Positive", "HideNeg": "Hide Negative", "MsgBeginThreshold": "Min Threshold Value Needed!", "MsgEndThreshold": "Max Threshold Value Needed!", "MsgBeginPValue": "Min Threshold Value Needed!", "MsgEndPValue": "Max Threshold Value Needed!", "MsgIllegal": "Illegal Value"}, "run": "Run", "contrast": "Contrast", "T1": "Native T1", "FS2mm": "FS2mm space", "MNI2mm": "MNI2mm space"}, "surfaceViewerValue": {"value": "Value", "SurfParc": "Network ID", "SurfParc92": "Network ID", "SurfParc152": "Network ID", "SurfParc213": "Network ID", "CorticalThickness": "Cort. <PERSON>.", "SulcalDepth": "Sulc. Dep."}, "arti": {"WBPialSurf": "Pial Surface", "LHPialSurf": "LH Pial Surface", "RHPialSurf": "RH Pial Surface", "WBWhiteMatterSurf": "White Matter Surface", "LHWhiteMatterSurf": "LH White Matter", "RHWhiteMatterSurf": "RH White Matter", "LHInflatedSurf": "LH Inflated Surface", "RHInflatedSurf": "RH Inflated Surface", "SurfParc": "Functional Parcellation", "SurfParc18": "18", "SurfParc92": "92", "SurfParc152": "152", "SurfParc213": "213", "SurfParcHighRes": "Functional Parcellation High Res", "CorticalThickness": "Cortical Thickness", "SulcalDepth": "<PERSON><PERSON>", "Volume": "Volume", "VolParc": "Functional Parcellation", "RawAnat": "Raw Anatomical Data", "RawBold": "Raw BOLD Data", "ImgLateralityLanguage": "Language Laterality", "ImgLateralityMemory": "Memory Laterality", "ImgAnatInput": "Anatomical Run", "ImgAnatT2Input": "Anatomical_t2w Run", "ImgBoldInput": "Bold Run", "ImgBoldInputSubmission": "Run", "ImgAllBold": "Total Frames", "ImgAllBoldCurrent": "Frames", "ImgMcDisp": "Motion Correction Displacement", "ImgMcRotation": "Motion Correction Rotations", "ImgMcTrans": "Motion Correction Translation", "ImgSnr": "BOLD Data Signal-to-noise Ratio", "ImgMeanSnr": "BOLD Data Mean Signal-to-noise Ratio Per Slice", "JsonMeanSnr": "Mean Signal-to-noise Ratio", "ImgLhAccSurf": "Left Hemi ACC Seed Activation Surface", "ImgPTWBMotorVol": "Right Motor Seed", "ImgNTWBMotorVol": "Left Motor Seed", "ImgRhAccSurf": "Right Hemi ACC Seed Activation Surface", "ImgLhPccSurf": "Left Hemi PCC Seed Activation Surface", "ImgRhPccSurf": "Right Hemi PCC Seed Activation Surface", "ImgLhMotorSurf": "Left Hemi Motor Seed Activation Surface", "ImgRhMotorSurf": "Right Hemi Motor Seed Activation Surface", "JsonAnatFeatures": "", "JsonFuncConnectivityMatrix": "", "JsonFuncFeatures": "", "JsonAMUTMSProject": "TMS Project", "DownloadAMUTMSProject": "TMS Download", "JsonScanInfo": "", "Laterality": "", "ImgAMUTMSProject": "", "SurfAnatAparcAseg": "aparc+aseg", "SurfAnatAparcA2009sAseg": "aparc.a2009s+aseg", "FreeSurferFs4": "FreeSurfer FsAverage4 Surface Space", "FreeSurfer2mm": "FreeSurfer Non-linear 2mm", "JsonGroupResult": "Metrics group result", "JsonGroupCompare": "Metrics group compare"}, "gender": {"1": "Male", "2": "Female", "3": "其他"}, "handedness": {"1": "Right", "-1": "Left", "0": "Ambidextrous"}, "maritalStatus": {"1": "Single", "2": "In Relationship", "3": "Married", "4": "Divorced", "5": "Widowed"}, "processingType": {"0": "UploadAdhoc", "1": "Anatomical Scan", "2": "BOLD Scan", "3": "Parcellation", "5": "Laterality"}, "scans": {"scanType": "Scan Type", "noDataIndicator": "there is no MR session for the subject yet"}, "scanType": {"0": "Anatomical", "1": "BOLD"}, "sliceImage": {"total": "Total number of slices:", "start": "Start from slice", "finish": "finish at slice", "and": "and show", "end": "slices.", "confirm": "Confirm", "eachRow": "Each Row Show", "totalLabel": "Total:"}}, "batchUpload": {"projects": {"headerTitle": "Select Project to Upload Scan Data", "onceUpload": "Single upload", "uploadMore": "Bulk upload"}, "subjects": {"headerTitle": "Select Subject to Upload Scan Data"}, "scanData": {"headerTitle": "{ projectname } - Upload Batch Envelope File", "desOne": "A batch envelope file provides necessary information on how to do a batch upload. It should be a TEXT file (typically saved in .csv format). if you are going to creare it manually, we suggest you {format}, use Microsoft Excel to edit it, and save it in .CSV format for uploading below", "desformat": "Download Envelope File Template", "downloadTemplate": "Download Run Information Template V{version}", "uploadTip": "Upload the completed envelope file click on the area to select files"}, "validate": {"onlyOneTask": "At least one of the EVS in the runInfo message is the task", "fileName": "File names can only be .csv and .txt", "pathExist": "Path does not exist", "nofilesPath": "No files in the path", "subjectIDEmpty": "Subject ID cannot be empty", "genderDifferenError": "Please keep the same gender for the same subject Id", "birthYearDifferentError": "Please keep the same birth year for the same subject Id", "birthYearError": "Year of Birth 1900-Present", "genderError": "Only allowed: 输入男/女, male/female", "maritalStatusError": "Only allowed: 单身/在关系中/已婚/离异/丧偶/single/married/divorced/inrelationship/windowed", "handednessError": "Only allowed: 双手/左手/右手/ambidextrous/left/right", "handednessScoreError": "Only allowed: -100 ～ 100 Integer", "mRIScanDateError": "Only allowed: 1990.1.1 - Present", "ageAtScanError": "Year of scanned data-year of birth (plus or minus 1)", "runInfoEmpty": "Task state Runinfo cannot be empty", "runinfoCondition": "The task condition in the task state cannot be empty", "conditionNameError": "The task conditions do not match the task conditions in the project settings", "envelopeFileVersion": "Please check if the EnvelopeFile version is correct and the file format is correct", "runinfoVersion": "Please check {path} version is correct, format is correct", "conditionVersion": "Please check {path} version is correct, format is correct", "checkVersionError": "Please check the following errors in the file", "readingFileFailed": "Reading {path} Failed", "evsNotFound": "EVS not found in line {num}", "fileNotFound": ".txt .csv was not found in the {path}", "evsEmpty": "{path} Evs folder not found", "evsSameRunNumber": "EVS must be the same as <PERSON><PERSON><PERSON>ber in line {num}", "onlyOneResting": "At least one of the EVS in the runInfo message is the resting state", "lineEmpty": "{num} line Condition is empty", "isEmpty": "Is Empty", "fileEmpty": "Path cannot be empty", "samePath": "Paths cannot be the same", "fileCondingError": "Reading failed, the file is in an unsupported encoding format", "runNumberError": "Run number must consist of three digits in line {num}", "uploadButtonTip": "After the upload is successful, please refresh the project details page to view the latest upload data"}, "showTable": {"note": "Note", "title": "Batch upload envelope file check", "allUploadData": "Scan data needs to be uploaded: { num }", "noErrorUploadData": "Check the data for proper scanning: { num }", "errorUploadData": "Check for correct scan data: { num } (Please correct the error message then upload again.)", "mRIScanData": "MRI scan file path", "ageAtScan": "Age at scan", "errorContent": "错误内容: ", "mRIScanDate": "MRI scan date", "clearFilters": "Clear Filter", "runInfo": "Run configuration information file", "assessmentData": "Evaluation scale data file path", "reUpload": "Reupload", "index": "No.", "startUpload": "Start Upload", "leavePageTitle": "Are you sure you want to abandon the batch upload?", "leavePageContent": "If the batch upload process is interrupted, all content that has been selected / filled will be erased.", "runInfoTable": {"title": "Run Configuration Information", "runNumber": "RunNumber", "condition": "Conditional File"}, "conditionTable": {"condition": "Condition", "startTime": "Start Time (s)", "durationTime": "Duration (s)"}}, "modal": {"chooseUploadType": "Select Upload Type", "chooseDownloadType": "Select Download Type"}}, "singleUpload": {"runInfoErrorTitle": "EVS Document check results display", "evsFileErrorTip": "EVS file is wrong, please upload again", "filePathEmpty": "Please select a file", "runinfoCondition": "Condition cannot be empty", "runinfoConditionCantEmpty": "Runinfo should not be empty", "restingDownloadTemplateDescript": "The Run information file can specify how different Runs should be processed, and should be processed as a resting state or skipped", "taskDownloadTemplateDescriptOne": "The run information file can specify how different runs should be processed, which should be treated as resting state, task state, or even skipped", "taskDownloadTemplateDescriptTwo": "In a task-based project, you should specify the conditions, start time, and duration of each condition for each run", "noTasks": "No tasks in project settings", "radioButton": "Fill in task configuration manually"}, "createAnalysis": {"scanCompDes": "Individual Level Functional Connectivity", "groupConnectomeDes": "Group Level Functional Connectivity", "createBtns": {"analysis": " Anatomical Metrics Analysis", "groupComp": "Group Level Anatomical Metrics Analysis", "scanComp": "Functional Connectivity", "IndividualConnectome": "Functional Connectome Connectivity", "groupConnectome": "Group Connectome", "task": "Task", "SeedToBrain": "Seed to Brain", "Coonectome": "Connectome"}, "analysisType": {"IndividualConnectome": "Individual Level Connectome", "IndividualSeedToBrain": "Individual Level Seed To Brain", "groupConnectome": "Group Level Connectome", "ConnectomeSeedToBrain": "Group Level Seed To Brain"}, "homeTitle": "Select Analysis Type", "stepOneTitle": "New Group Level Anatomical Metrics Analysis", "stepOneTitleConnectome": "New Group Level Connectome Analysis", "selJobsWarning": "Please select Scan Data.", "createGroupTip": "Only processing completed scan data will be used in analysis.", "createGroupMsg1": "The selected project do not have data groups, you can", "createGroupMsg2": "create new data group", "createGroupMsg3": "or previous to select a new project .", "groupChangeWaring": "The scan in data group {tips} in this analysis has been changed.", "groupsLimit": "Please select no more than 10 data groups."}, "vizAnalysis": {"filter": {"selectHemisphere": "Select Hemisphere", "valueOperation": "Value Operation", "downTableTip": "Download Data", "The1stGroup": "Group1", "The2ndGroup": "Group2", "SwitchTo": "Switch to"}, "table": {"groupName": "Group Name", "groupValue": "Value (mm)", "metric": "Metric", "mask": "Mask", "atlas": "Atlas", "parcelName": "Parcel <PERSON>", "pValue": "P Value", "viewChart": "View Chart", "viewMyelin": "View Myelin Info", "viewMyelinTitle": "<PERSON><PERSON>", "total": "Total {total} items", "uncorrectedPValue": "Uncorrected P Value", "fdrCorrectedPValue": "FDR corrected P Value", "bonferroniCorrectedPValue": "<PERSON><PERSON><PERSON><PERSON> corrected P Value"}, "method": {"title": "Hypotheses-test Method", "ttest_ind": "Independent Two-Sample T-Test", "ranksums": "<PERSON><PERSON> Rank-Sum Test"}, "tabs": {"customize": "Customize Analysis", "statistical": "Statistical Analysis", "sliderTitle": "P Value", "seeMore": "See More", "foldUp": "Fold Up"}, "brain": {"operation": "Select Hemisphere", "wb": "Whole Brain", "lhrh": "Whole Brain", "lh": "Left Brain", "rh": "Right Brain", "wbHemi": "Both Hemisphere", "lhrhHemi": "Both Hemisphere", "lhHemi": "Left Hemisphere", "rhHemi": "Right Hemisphere"}, "group": {"controlTitle": "Compare Group", "separateTitle": "Separate Display"}, "metric": {"label": "Metric", "corticalThickness": "Cortical Thickness", "sulcalDepth": "<PERSON><PERSON>", "corticalMyelin": "Cortical Myelin", "surfaceArea": "Surface Area", "grayMatterVolume": "Gray Matter Volume", "parcelVolume": "Parcel Volume", "corticalThicknessUnit": "Cortical Thickness(mm)", "sulcalDepthUnit": "Sulcal Depth(mm)", "corticalMyelinUnit": "Cortical Myelin", "surfaceAreaUnit": "Normolized Surf Area", "grayMatterVolumeUnit": "Normolized Gray Matter Volume", "parcelVolumeUnit": "Normolized Brain Structure Volume"}, "mask": {"label": "Mask", "anat": "Anatomical Parcel", "func": "Functional Region", "brain": "Brain"}, "atlas": {"aparc": "aparc", "a2009s": "aparc.a2009s", "aseg": "aseg", "18": "18", "92": "92", "152": "152", "213": "213", "label": "Atlas", "allAtlas": "All Atlas", "selectedParcels": "Selected Parcels { checkedCount }"}, "percent": {"title": "Add to display", "add": "Add"}, "alert": {"switchMaskTitle": "Are you sure switch the mask type?", "switchMaskContent": "If you switch the mask type, the content you selected in the current mask will be lost.", "switchMetricTitle": "Are you sure switch the metric type?", "switchMetricContent": "If you switch the metric type, the content you selected in the current metric will be lost.", "inputErrMsg": "Please enter a valid number"}, "chart": {"scansLabel": "Count", "downPNGTip": "Download PNG"}, "math": {"avg": "Average", "avgNoEnd": "Average", "p25": "25th Percentile", "p75": "75th Percentile", "max": "Max", "min": "Min", "pattern": "{percent}th Percentile", "end": "th Percentile", "median": "Median"}, "viz": {"uncorrected": "Uncorrected P Value", "FDR": "FDR corrected P Value", "Bonferroni": "<PERSON><PERSON><PERSON><PERSON> corrected P Value", "returnWb": "Return Whole Brain", "multipleComparisonsCorrection": "Multiple Comparisons Correction"}}, "dataAnalysis": {"home": {"title": "Analytics", "newAnalysis": "New Analysis", "existingAnalyses": "Existing Analyses", "seedROIs": "Seed ROIs", "scans": "Scans"}, "common": {"analysisName": "Analysis Name", "project": "Project", "projectPlaceHolder": "Select a project", "projectName": "Project Name", "projectReq": "Please select project!", "des": "Description", "desPlaceholder": "Input analysis description", "create": "Created By", "space": "Space", "type": "Type", "status": "Status", "start": "Start Time", "finish": "Finish Time", "action": "Action", "viewResultTip": "View Analysis Result", "groupsDataTip": "Show Data info of groups", "errorTip": "Show warning and error info", "delWarning": "Please select <PERSON><PERSON><PERSON><PERSON>", "selectGroup": "Select Group", "analysisType": "Analysis Type"}, "actions": {"save": "Save", "delete": "Delete", "new": "New", "next": "Next", "previous": "Previous", "back": "Back", "cancel": "Cancel", "clear": "Clear Filters", "confirm": "Confirm", "runAnalysis": "Run Analysis", "confirmDel": "Confirm Deletion"}, "title": {"connectivity": {"func": "New Individual Level Seed To Brain Analysis", "bold": "New BOLD Connectivity Analysis"}, "IndividualConnectome": "New Individual Level Connectome Analysis", "GroupConnectome": "New Group Level Seed To Brain Analysis", "exsiting": "Existing Analyses", "exception": "Exception Info", "delAnalysis": "Delete Analysis", "seed": {"func": "Functional Connectivity Seed ROIs", "bold": "BOLD Connectivity Seed ROIs"}, "newSeed": "New Seed", "editSeed": "Edit Seed Info", "newAnalysis": "New Analysis"}, "vizTab": {"seed": "Seed"}, "exception": {"scanId": "Data ID", "seedName": "Seed Name", "severity": "Severity", "path": "File Path", "msg": "Message"}, "delete": {"sure": "Are you sure to delete these seeds?", "need": "The seed you can delete:", "donot": "You don’t have premission to delete the following seeds, so they will not be deleted:", "sureAnalysis": "Are you sure to delete these analyses?", "needAnalysis": "The analyses you can delete:", "donotAnalysis": "You don’t have premission to delete the following analyses, so they will not be deleted:"}, "connectivity": {"name": "Name", "namePlaceHolder": "Input analysis name", "analysisName": "Analysis Name", "nameReq": "Please input name!", "nameLen": "Input characters exceed the limit. Only allowed 20 characters.", "nameError": "Allowed characters are Numbers, English, Chinese, underscore, dash, comma, period, colon and space", "selectData": "Select Data", "selectGroup": "Select Group", "selectROIs": "Select Seed ROIs", "org": "Organization", "empty": "The exisiting Analysis is empty, click the button to create.", "goBackDes": "Go back to {analysisName} analysis detail page ?", "viewData": "View Data ID ", "viewSeed": "View Seed ", "dataUsedInAnalysis": "Data used in this analysis", "deleteMsg": "The data does not exist, has been deleted on {time}", "nameRepeat": "Duplicate Analysis Name"}, "seed": {"name": "Seed Name", "nameReq": "Please input Seed Name!", "location": "Seed Location", "new": "New Seed", "type": "Seed Type", "typeReq": "Please select Seed Type!", "radius": "Radius(mm)", "radiusReq": "Please input 0-100 range Radius(mm)!", "vertex": "Vertex Index", "vertexReq": "Please input Vertex Index!", "vertexFormat": "Vertex Index format error!", "mni": "MNI Coordinates", "mniReq": "Please input MNI Coordinates!", "mniFormat": "MNI2mm Coordinates format error!", "addMore": "Add More Seed", "empty": "The seed ROI is empty, click the button to create new seed", "desPlaceholder": "This is the description of this analysis", "space": "Space", "seedType": "Seed Type", "fs4Location": "FS4 Location", "fs6Location": "FS6 Location", "createAt": "Create Time", "createBy": "Created By", "mni2": "MNI2mm", "mni1": "MNI1mm", "des": "Description", "seedInfo": "Seed Information", "atlas": "Atlas", "atlasReq": "Select Atlas", "parcelName": "<PERSON><PERSON><PERSON>", "parcelNameReq": "Select Parcel", "Network": "Network", "resolution": "No. Regions", "NativeVertex": "Native Vertex", "FS4Vertex": "FS4 Vertex", "NativeVoxel": "Native Voxel", "MNI2mmVoxel": "MNI2mm Voxel", "FS6Vertex": "FS6 Vertex", "MNI1mmVoxel": "MNI1mm Voxel", "FunctionalRegion": "Functional Region", "AnatomicalParcel": "Anatomical Parcel", "ViewSeedInfo": "View Seed Info", "nativeSurface": "FsAverage4 Surface", "mni2mm": "MNI2mm Volume ", "groupDifferent": "Difference", "scanGroupResult": "Scan Group Result", "pleaseInputDesc": "Please input description"}, "scanVisualizationInfo": {"des": "Description: ", "createdBy": "Created By: ", "startTime": "Start Time: ", "finishTime": "Finish Time: ", "status": "Status: "}}, "httpError": {"networkError": "The server responded incorrectly, please try again later", "SV11003": "Subject ID already exists, please choose a new ID"}, "install": {"linux": {"1": "Supported Linux Operating Systems:", "2": "Linux Installation Instructions", "3": "Install `wget`", "4": "(skip this step if `wget` is already installed)", "5": "How to check if wget is installed:", "6": "Run the following command in Terminal:", "7": "Go to installation directory.", "7explain": "(If the installation directory is desktop, a desktop shortcut will be created after installation, typical path to desktop is $HOME/Desktop)", "8": "[installation directory]", "9": "Run Neural Galaxy Desktop App", "10": "There are two ways to start Neural Galaxy desktop app:", "11": "Click Neural Galaxy Icon on the desktop (only if the installation directory is $HOME/Desktop)", "12": "Uninstall Neural Galaxy App", "13": "Please follow the following 3 steps to install Neural Galaxy desktop app.", "14": "Install Neural Galaxy desktop app. "}}, "control": {"zoom": "Zoom", "parcBorder": "Display Functional Network Boundary", "setWH": "调节对比度"}, "organization": {"organizationTitle": "Organization Information", "organizationName": "Organization Name", "robot": "NG Robot", "licenseAndUsage": "License and Usage", "workSpace": "WorkSpace", "licenseTemplates": "License Templates", "name": "Name", "pointName": "User Name", "uniqueId": "Unique ID", "productOfNum": "Nunber of Product", "role": "Role", "createDate": "Created Date", "status": "Status", "advisorsStatus": "Advisors Status", "pointAdvisorsStatus": "Advisors Status", "numberOfadmin": "Number of Admins", "numberOfUsers": "Number of Users", "numberOfProjests": "Number of Projects", "numberOfSubjects": "Number of Subjects", "numberOfScans": "Number of Scans", "numberOfDataGroups": "Number of Data Groups", "numberOfPlans": "Number of Data Plans", "numberOfProduct": "Number of Data Products", "lastLogin": "Last Login", "valueActive": "Active", "valueInactive": "Inactive", "projects": "Projects", "projectRole": "Project Role", "projectStatus": "Project Status", "newProject": "New Project", "user": "Users", "lastLoginIn": "Last Logged In", "action": "Action", "email": "Email", "endpoint": "MRI Data Processing Software Endpoint", "pointEndpoint": "Hardware List", "dicom": "DICOM Node Manage", "projectInfo": "There are a total of { total } projects in the Org, you participated in { show }of them.", "deleteMsg": "The organization of {name} has been deleted successfully.", "point": {"userCount": "用户数", "patientCount": "患者数", "uploadIntakeCountTotal": "数据处理总次数", "uploadIntakeCountMonth": "当月数据次数", "treamentCountTotal": "治疗总次数", "treamentCountMonth": "当月治疗次数", "robotCount": "机器人设备数"}, "robotList": {"id": "机器人ID", "endpointId": "机器人编号", "type": "机器人型号", "registTime": "注册时间", "connectCloudTime": "最近于云端通讯时间"}, "deviceList": {"id": "设备ID", "endpointId": "设备编号", "type": "设备型号", "registTime": "注册时间", "connectCloudTime": "最近于云端通讯时间"}, "table": {"orgMembers": "Org Members", "projectAdmins": "Project Admins", "ngAdmins": "NG Admins", "noProject": "No Project", "advisors": "Advisors", "ngAdvisors": "NG Advisors", "pointAdvisors": "AfterSalesaintenancePersonnel", "pointNgAdvisors": "AfterSalesaintenancePersonnel", "delete": "Delete", "projectName": "Project Name", "projectRole": "Project Role", "inviteUser": "Invite User", "reViewer": "<PERSON><PERSON><PERSON><PERSON>"}, "form": {"nameFieldName": "Name", "namePlaceHolder": "Organization name", "contactEmailFieldName": "Contact Email", "contactEmailPlaceHolder": "Organization contact email", "isActiveFieldName": "Status", "orgType": "机构类型", "enableExportPDF": "允许生成图文报告", "clinicalInstitutions": "临床机构", "scientificResearchInstitution": "科研机构", "isReview": "审核治疗方案xxxxxx", "valueActive": "Active", "valueInactive": "Inactive", "nameRequired": "Organization name is required", "uniqueIdRequired": "Unique ID is required", "contactEmailRequired": "Organization contact email is required", "nameFactor": "Input characters exceed the limit. Only allowed 20 characters", "nameError": "The input content isn't in accordance with the specification, only allowed to enter Numbers, English, Chinese, _, -", "validContactEmail": "Invalid email", "createDate": "Created Date", "alreadyMsg": "The name already exists, please re-enter.", "uniqueIdAlreadyMsg": "The uniqueId already exists, please re-enter.", "uniqueIdError": "UniqueId is incorrect,3-20 characters, Numbers, English, underscore, dash are allowed."}, "viewInfo": "View Info"}, "product": {"productTitle": "Product {value}", "product": "Product"}, "advisors": {"modalTitle": "Delete Advisors", "editTitle": "Mange Advisors", "pointEditTitle": "Mange After-sales maintenance personnel", "pointReView": "Mange Reviewer", "ensure": "Confirm", "delete": "Delete", "cancel": "Cancel", "name": "Name", "email": "Email", "status": "Status", "lastLogin": "Last Logged In", "projectName": "Project Name", "projectStatus": "Project Status"}, "inviteUsers": {"title": "Invite Users", "organization": "Organization", "project": "Project", "userEmails": "User Emails", "inviteRole": "Invite <PERSON>", "ngReviewer": "Reviewer", "user": "User", "inputPrompt": "Input a list of emails separated by comma(,) / space( ) / semicolon(;) / or pipe(|). Maximum: 50", "organizationAdmin": "Organization Admin", "editInfo": "Edit Info", "therapist": "Therapist", "afterSalesMaintenance": "After-sales maintenance", "resetPwd": "Reset Password", "updatePwd": "Update Password", "resetPwdMsg": "Are you sure to reset the password?", "projectAdmin": "Project Admin", "projectMember": "Project Member", "advisors": "Advisors", "ngAdmin": "NG Admin", "back": "Back", "invite": "Invite", "moreThanMaxMessage": "There are too many emails", "onEmails": "There are too no true emails", "userMustHaveProject": "Regular user must be assigned to a project", "errorEmailMessage": "There is an error in the mailbox information, please modify it.", "asyncValidationFailMessage": "The mailbox has been invited and cannot be invited again: { emails } ", "inviteLicenseMsg": "Number of users allowed to invited: {numberRemaining} remaining ({total} Total of pre year)", "modal": {"title": "Account and Password", "projectName": "Project Name", "role": "Role", "clipboard": "The password is copied to the clipboard", "userName": "User Name", "password": "Password", "copyPassword": "Copy UserName And  Passwrod"}}, "projectInfo": {"title": "Project Information", "organization": "Organization", "createDate": "Created Date", "createBy": "Created By", "type": "Project Type", "status": "Project Status", "scanTR": "Scanning TR", "scanTRTip": "Set the number base on the actual scanning parameter", "skip": "No. Frame<PERSON> to Skip", "skipTip": "Affected by the instability of the magnetic field at the beginning of the scan, the image data of the first few frames may be noisy. You can choose to remove the data of the first few frames", "sync": "Project Info Sync", "syncStrategy": {"off": "OFF", "on": "ON", "boxToCloud": "From MRI Data Processing Software to Cloud Medical Image Data Processing Software", "onlyProject": "Only project info (No scan data sync)", "twoWay": "Between MRI Data Processing Software and Cloud Medical Image Data Processing Software"}, "subjectsMenu": "Subjects", "scansMenu": "Scans", "dataGroupsMenu": "Data Groups", "settingMenu": "Project Setting", "userMenu": "Users", "name": "Name", "nameRequired": "Project name is required", "nameRule": "20 characters, Numbers, English, Chinese, underscore, dash and space are allowed.", "timeofRepetitionRequired": "Repeat time must be a number between 0.2 - 10 seconds", "skipRequired": "No. Frames to <PERSON><PERSON> must be a non-negative integer, maximum length of 10", "resting": "Resting", "task": "Task", "isActive": "Status", "valueActive": "Active", "notNeed": "NO", "need": "YES", "valueInactive": "Inactive", "subjects": {"total": "Total Number of Subjects: { total }", "subjectId": "Subject ID", "birthDay": "Birth Year", "sanNumber": "Number of Scans", "handedness": "Handedness"}, "scans": {"total": "Total Number of Scans: {total} ", "scanId": "Data ID", "fileName": "File Name", "dataGroups": "Data Groups", "createDataGroups": "Creating data groups", "clickButtonCreateDataGroup": "No data group exists under the current project，click on the button to create", "runInfo": "Task Detail", "runInfoTips": "View Task Detail", "subject": "Subject ID", "status": "Status", "createAt": "Create At", "finishedAt": "Finished At", "returnDataGroup": "Return to data group information page", "returnAnalysis": "Return to data analysis page", "runInfoModal": {"titile": "about {id} Run Info", "runNumber": "Run Number", "evs": "EVS"}}, "dataGroups": {"total": "Total Number of Data Groups : {total} ", "dataGroups": "Data Group", "action": "Action", "numberOfScans": " Number of Scans"}, "users": {"total": "Total Number of Users: {total} ", "name": "Name", "role": "Role", "email": "Email", "lastLoingIn": "Last Logged In", "status": "Status"}, "setting": {"conditions": "Conditions", "newCondition": "New Condition", "taskDesign": "Task Design", "newTaskDesign": "New Task Design", "startTime": "Start Time (s)", "durationTime": "Duration (s)", "uploadedEVS": "Uploaded EVS", "sameTaskNameMsg": "The task name already exists, please try a different name.", "conditionPage": {"total": "Total Number of Conditions: {total}", "conditionName": "Condition Name", "description": "Description", "opera": "Action", "deleteTitle": "Are you sure to delete this Condition ?", "deleteContent": "The condition {name} task design will be deleted at the same time.", "empty": "The conditions is empty, click the button to create new conditions."}, "taskDesignPage": {"total": "Total Number of Task Design: {total}", "conditions": "Conditions", "startTime": "Start Time (s)", "durationTime": "Duration (s)", "deleteContent": "Click \"Confirm\" To Delete: {name}", "empty": "To enter task design information, you can either enter task designs here, or upload EVS files directly. Click the button below to create new task designs:"}}}, "ngContianer": {"overviewStatistics": "Overview Statistics", "numOgOrganization": "Number Of Organizations", "numOfUsers": "Number Of Users", "numOfproject": "Number Of Projects", "numOfScans": "Number Of Scans", "numOfSubject": "Number Of Subjects", "numOfDataGrounps": "Number Of Data Groups", "newOrganizaiton": "New Organization", "table": {"title": "Organizations", "name": "Name", "createDate": "Created Date", "status": "Status", "license": "License", "activateProduct": " Activate Product", "usagelable": "Usage", "licenseTemplateTable": {"new": "New License Template", "licenseTemplate": "License Template", "description": "Description", "action": "Action", "deleteTip": "Are you sure you want to delete the license template?"}, "usage": {"info": {"name": "Name of license granted:", "licenseGrantedTo": "License granted to", "effective": "License effective on", "expires": "License expires on", "purchased": "Purchased License", "expiresOn": "Expires On", "effectiveOn": "Effective On", "userBtn": "Current License", "purchasedBtn": "License to be Activated", "expiredBtn": "License to be Expired", "create": "New License"}, "table": {"licenseItem": "License Item", "usageStatistics": "Usage Statistics", "licenseLimits": "License Limits"}, "form": {"overageRate": "Overage Rate", "note": "Note", "item": "<PERSON><PERSON>", "pricing": "Pricing(RMB)"}}}, "modal": {"deleteTitle": "Are you sure to delete this organization ?", "content": "If the organization is deleted, the projects, subjects, data, etc. under the organization will be deleted at the same time.", "conditions": "Please enter the organization name you want to delete", "placeholder": "Enter the Organization Name "}}, "special": {"info": {"title": " {name} Information", "numOfOrgMembers": "Number of Org Members", "numOfNGAdmin": "Number of NG Admins", "numOfAdvisors": "Number of NG Advisors", "pointNumOfAdvisors": "Number of After-sales Personnel", "crateDate": "Created Date", "lastLogin": "Last Login"}}, "license": {"empty": "The license is empty, click the button to create new license.", "createBtn": "Create License", "explain": "If you have some questions or want to update your license,", "support": "please contact us", "allow": "Allow", "forbidden": "Not available", "navigator": "MRI Data Processing Software", "boxSystem": "MRI Image Data Processing System", "cloud": "Cloud", "surgical": "Surgical", "readOnly": "License is expired, this functionality is disabled", "template": {"modal": {"title": "License Template", "name": "Name of License Template", "nameMsg": "Please input Name of License Template", "description": "Description", "web": "Web", "desktop": "Desktop", "navigator": "Navigator", "navigatorIsTrue": "MRI Data Processing Software", "endpointsAllowed": "Endpoints allowed", "scanPerProcessig": "Per scan processed", "stroagePerScanPerYesr": "Per scan stored per year", "appCn": "User account per year", "license": "License Item", "client": "Desktop Client", "value": "Value", "batchUpload": "Batch Upload", "batchDownload": "Batch Download", "batchDownloadAndbatchUpload": "Batch Upload And Download", "newWorkFileServer": "File Server", "maxAccounts": "Number of user accounts allowed", "scansToUploadPerYear": "Number of scans allowed to process per year", "scansToUploadPerDay": "Number of scans allowed to process per day", "scansToStorePerYear": "Number of scans allowed to store per year", "scansToUploadPerMonthUser": "Number of scans to process per month per user", "overageRate": "Overage Rate", "nolimit": "No Limit", "item": "<PERSON><PERSON>", "pricing": "Pricing", "ensure": "Confirm", "cancel": "Cancel", "switch": "Switch", "endpoint": "MRI Data Processing Software Endpoint", "storage": "Storage", "automatic": "Automatic Download"}}, "licenseTimeError": {"1": "Effect date of the license cannot be later than expiration date", "2": "License effective period cannot overlap with effective period of another license for the same product"}, "modal": {"updateLicenseTitle": "Edit License", "createLicenseTitle": "Create License", "note": "Note", "moreProdunct": "Add More Products", "morePermit": "Add more License and Usage", "fromTemplate": "Start From a License Template", "nameOfLicense": "Name Of License", "grantedTo": "Granted To", "effectiveOn": "Effective On", "expiresOn": "Expires On", "deleteTitle": "Delete this product license"}}, "batch": {"upload": {"modal": {"title": "Upload Data", "explain": "Your license does not have access to batch upload functionality. To enable this feature, please { contact } to update your license.", "contact": "contact us"}}, "download": {"modal": {"title": "Download Data", "explain": "Your license does not have access to batch download functionality. To enable this feature, please { contact } to update your license.", "contact": "contact us"}}, "limit": {"modal": {"title": "Upload Limit", "reRunTitle": "Processing restrictions", "explain": "The number of uploaded data exceeds the limit, you can continue to upload, need to pay an additional fee, are you sure to continue to upload?", "reRunExplain": "The number of scanned data that can be processed exceeds the license limit. Additional fees will be incurred. Are you sure to continue processing?", "noTime": "The number of uploaded data exceeds your license limit. You can not continue to upload.", "reRunNoTime": "The number of scanned data that can be processed exceeds the license limit. You cannot continue processing."}}}, "connectome": {"zValue": "Z Value", "rValue": "R Value"}, "newProject": {"conditions": {"title": "Conditions", "explain": "Conditions are required for a task project. Task configurations can be provided using Task Designs UI here or using EVS files during the upload process (Task conditions in \"EVS\" files must be consistent with those in the project).", "buttonBefore": " Please click the button below to create conditions:"}, "task": {"title": "Task Design (Optional)", "haveTitle": "Task Design", "explain": " If task configurations are relatively simple and many scans use the same task configurations, you can enter them here using task designs. However, if task configurations are complex and different scans have different task configurations, please use \"EVS\" files instead.", "firstExplain": "If task configurations are relatively simple and many scans use the same task configurations, you can enter them here using task designs. However, if task configurations are complex and different scans have different task configurations, please use \"EVS\" files instead.", "buttonBefore": "click below to enter a new task design", "errorInfo": "Please check the task conditions"}}, "inviteUser": {"modal": {"title": "Invite User", "explain": "The number of invited users exceeds your license limit. You can continue to invite (additional fees may apply). Are you sure to continue?", "limit": "The number of invite users exceeds your license limit. Please {contact } to update your license. ", "contact": "contact us", "number": "Number of users allowed to invited: {value}"}}, "storageAndDownload": {"helpPdf": "Guide to Neural Galaxy Data Storage and Download", "title": "Storage and Download", "fileStorage": "My File Storage", "totalSpace": "Organization Total Space", "used": "Account Used", "available": "Organization Available", "help": "Storage and Download Guide", "description": "Neural Galaxy provides a storage space for you to store your files, and download processing results. You can mount this storage space to your local computer as part of your file system. After mounting, you can do file copy and paste as if this storage is part of your local disk. ", "downloadDirectory": "Directory for automatic result download", "downloadNotify": "Notify me after automatic download is completed", "newDirectory": "New directory", "delete": "Delete", "licenseMsg": "Unable to view \"Storage and Download\" content due to license restrictions,Please {contact}", "netMsg": "\"Storage and Download\" is not available when you access Cloud Medical Image Data Processing Software. If your organization has MRI Data Processing Software access,please either visit web application on the MRI Data Processing Software or sBrain Atlas Serverwitch endpoint of our desktop app to Brain Atlas Server", "contact": "contact us", "hiddenLabel": "Show hidden files", "deleteModal": {"title": "Are you sure to delete these files?", "content": "Please enter \"Delete Files\" in the input box.", "limitMsg": "Please select files.", "deleteTitle": "Unable to delete this directory.", "deleteContent": "This directory is set to automatic result download and cannot be deleted.", "okText": "Cancel"}, "chooseDirectoryModal": {"title": "Choose Directory"}, "newModal": {"title": "New Folder", "name": "Name", "nameRequired": "Directory name cannot be empty and the length of the name cannot exceed 20 characters", "errorInfo": "Failed to create file. Check to see if the same file name exists.", "nameRule": "Use chart, number,- and _ to create directories. Use / to create subdirectories"}, "directory": {"name": "Name", "size": "Size", "time": "Time"}}, "endpoints": {"client": "Client", "desktop": "Desktop", "navigator": "Navigator", "box": "Navigator Server", "pointBox": "Brain function analysis software", "pacs": "MRI scanner node", "inActive": "inActive", "active": "active", "banned": "banned", "status": {"pending": "Pending", "submitted": "Submitted", "canceled": "Canceled", "inProgress": "InProgress", "failed": "Failed", "finished": "Finished"}, "table": {"uniqueId": "Unique ID", "type": "Type", "ip": "Ip Address", "version": "Version", "note": "Note", "status": "Status", "action": "Action"}, "edit": {"title": "Edit Endpoint", "note": "Note"}, "update": {"title": "Select Version", "select": "Select Version"}, "warn": {"title": "Update Information", "name": "Name", "version": "Version", "status": "Status", "description": "Description", "time": "Time", "reason": "Reason"}}, "common": {"scansT1": "Scans (T1)", "login": {"message": "You will log into {value} product. You can switch products after login.", "research": "Cloud Medical Image Data Processing Software", "surgical": "PreSurge", "dataServer": "Cloud Medical Image Data Processing Software", "box": "MRI Image Data Processing Software", "boxSystem": "MRI Image Data Processing System", "version": "Version: {version}", "point": "Point", "point100": "脉  冲  磁  刺  激  仪  管  控  软  件", "pointTherapy": "优 点 疗 法 云 软 件", "dataServerAboutUs": "Cloud Medical Image Data Processing Software", "boxAboutUs": "MRI Data Processing Software", "boxSystemAboutUs": "MRI Image Data Processing System", "surgicalAboutUs": "PreSurge", "httpErrorCode": {"endpointTypeInvalid ": "Invalid endpoint type", "surgeryLicenseInvalid": "You don't have access to {value} product. Please choose another product.", "surgeryUserInvalid": "You don't have access to this product. Please contact your administrator", "onfoundLicense": "You don't have access to this product", "notFoundRAOrg": "Account not belong to any organization", "authBoxLimited": "You don't have access for this product", "licenseDenied": "The current account can not coonect to {value}.", "endpointTypeInvalidOfRA": "Advisor can't sign in PreSurge", "accountInvalid": "Account not activated", "orgInvalid": "Organization not activated", "PNT_US0008": "user ip rule config can not be null", "PNT_US0009": "The user IP address can not be used to log in", "PNT_US0010": "user ip rule config erro"}}, "header": {"seeg": "PreSurge", "connectedSeeg": "Link to PreSurge", "research": "Cloud Medical Image Data Processing Software", "connecteSresearch": "Link to Cloud Medical Image Data Processing Software"}}, "surgery": {"home": {"home": "Home", "patientList": "PatientList", "homeTooltip": "Home", "subjects": "Subjects", "subjectsTooltip": "Subjects", "surgery": "Plans", "surgeryTooltip": "Plans", "seegEmpty": "No project, click \"New Project\" to create", "surgeEmpty": "No PreSurge  , click \"New Plan\" to create"}, "createNewPlan": "No surgical plan, click the button to create"}, "seeg": {"showMsg": "Please enter subject ID", "moreShow": "Load more", "plan": {"distance": {"entry": "Entry", "target": "Target", "vesselDisLabel": "Vessel dist.(mm)", "targetDisLabel": "Target dist.(mm)", "navigate": "Navigate"}, "download": {"title": "Export Result", "exporting": "Exporting", "exportComplete": "Export Complete", "failureNoExport": "Unable to export if planning fails", "tip": "Planning results are being exported, please download results after the export is finished", "downloadFiles": "Download File", "tipDes": "We are preparing to download the file. You can go to the homepage, subject details page, surgical planning overview page, download page later, click to download this file", "subjectCustId": "Subject ID", "planType": "Plan Type", "createAt": "Created At", "status": "Status", "action": "Action", "resectionExportTitle": "Select the key functional area to export", "resection": "Resection", "sEEG": "Automatic path planning", "baseTip": "Export planning result in T1 space", "downTip": "Download the exported planning result", "rerun": "Result export failed", "rerunTip": "Export planning result again", "empty": "You can export planning result after planning is finished"}, "resection": {"riskValue": "Risk Value", "riskColor": "Risk Value", "anat": "Anatomical Region", "func": "Functional Network", "showFunc": "Show Functional Network", "coords": "Coordinate", "majorFun": "Major Brain Functions", "majorFunShow": "Selected Major Brain Functions", "literature": "References", "languageLateralizationIndex": "Language Laterality Index", "langHemiLR": "Language dominant hemisphere - Bilateral", "langHemiRight": "Language dominant hemisphere - Right", "langHemiLeft": "Language dominant hemisphere - Left", "languageDominanceHemisphereR": "Language Dominance Hemisphere-Right", "languageDominanceHemisphereLR": "Language Dominance Hemisphere-Bilateral", "languageDominanceHemisphereL": "Language Dominance Hemisphere-Left", "riskTitle": "Risk value for selected voxel", "allBrainTitle": "Selected voxel", "riskTitleTip": "Risk value of resection/ablation for selected voxel", "languageDominanceDes": "The range of language laterality index is from -1 to +1, greater than 0.1 means left-lateralized, and less than -0.1 means right-lateralized. Language laterality index is calculated from resting state fMRI image. This index can be impacted by several factors, including head movement during the scan, length of fMRI scan", "allBrainFunctionAtlas": "All Brain Functions"}, "menu": {"plan": "Plan", "target": "Target", "trajectory": "Trajectory", "download": "Download", "export": "Output", "selectTargets": "Select Targets", "editTrajectory": "Edit Trajectory", "seeResult": "See Result"}, "target": {"targetNum": "No. of trajectory: {value}", "addedTarget": "Added Target", "electrode": "Max Electrode length(mm)", "anat": "Anatomical Region", "anatShort": "Anat Region", "nativeT1": "Target Coordinates", "riskLabel": "Risk Value(Greater than 0.7-higher risk | 0-1)", "note": "Note", "addNote": "Add Note", "point": "Target Point", "risk": "Target Point Risk Value", "addTarget": "Add Target", "addTargetSuccess": "Add target successfully", "targetConflict": "This target is too close to other targets. Any two targets should be at least 10mm apart", "targetRoi": "No target can be added in this area", "targetRoiTooltip": "Targets cannot be added to high risk areas, such as vessel, or invalid areas, such as ventricle, cerebellum and areas outside brain mask.", "riskWarning": "This target has higher risk, please add with caution", "riskMaskWarning": "There is no risk value in this area, please fill in carefully", "dragInfo": "Drag and drop to adjust sorting", "dragInfoTip": "After automated planning, the target with fewer number of candidate trajectories will be listed first. When two targets have the same amount of candidate trajectoties, they are sorted by the target input order.", "plan": "Plan", "delTitle": "Are you sure to delete this target?", "dragTitle": "Please confirm the sorting order of the targets", "dragSubTitle": "Drag target to reorder", "warning": "Warning", "successTitle": "Plan Is Created Successfully", "successInfo": "It will take some time to generate plan results, please check back later for results", "successToHome": "Subject Detail Page", "place": "Click/Enter Coordinates on Volume View", "notePlace": "Click/Enter on Volume View", "empty": "Please add sEEG targets under \"Targets\" tab. Click \"Start Planning\" button, plan results will be available within about 20 minutes", "leavePageTitle": "Do you want to save targets before exiting?", "nan": "Na", "notEmpty": "Can not be empty"}, "selectedFile": {"fileSelected": "Scan data included in this plan", "skull": "Skull", "vessel": "<PERSON><PERSON><PERSON>", "scalp": "<PERSON><PERSON><PERSON>", "cortex": "<PERSON>rtex", "anatomical": "Anatomical", "riskMap": "Risk Map", "fusionWarn": "The scan data is being registered with T1 scan, please check back later", "createPlan": "Create SEEG Plan", "createResaction": "Create Resaction", "opacity": "Opacity", "rectangle": "Show Trajectory only", "goBack": "goBack", "lackT1": "Lack T1", "lackBold": "Lack Bold", "lackVessel": "<PERSON><PERSON> Vessel", "lackSkull": "Lack Skull", "selectFiles": "Select Files", "info": {"title": "Selected File", "status": "Status", "dataId": "Data ID", "type": "Type", "scanName": "Scan Name"}}, "selectedBrainStatus": {"title": "Brain Status", "normal": "Normal", "damage": "Damage", "leftBrain": "Left Brain", "rightBrain": "Right Brain", "frontal": "Frontal", "central": "Central", "parietal": "Parietal", "temporal": "Temporal", "occipital": "Occipital", "notification": "It will result in inaccurate planning results if you select more than 5 distorted regions, select both sides of occipital, or selecting both sides of central. Please reserve at least one region for planning purpose."}, "trajectory": {"nativeT1": "Target Coordinates", "riskLabel": "Target Risk Value", "nativeT1Anat": "Target Anat", "all": "All", "left": "Left", "right": "Right", "leavePageTitle": "Leaving current page?", "leavePageContent": "Your changes won't be saved", "entryPoint": "Entry Point", "trajectoryRisk": "Trajectory Risk", "trajectoryRiskShort": "<PERSON><PERSON>", "angle": "Drilling Angle", "angleShort": "Drilling Angle", "length": "Trajectory Length(mm)", "lengthShort": "Traj Length", "overlappedFunctionalNetworks": "Intersect Functional Networks", "overlappedFunctionalNetworksShort": "Func Networks", "cantFunctional": "Please view intersect functional networks while transparency of cortex layer is not 100%", "throughTemporalLobe": "Through Temporal Lobe", "throughTemporalLobeShort": "Temp Lobe", "hideTrajectory": "Hide Trajectory", "showTrajectory": "Show Trajectory", "yes": "Yes", "no": "No", "editTarget": "Edit Target", "cancelEdit": "Cancel Edit", "savePlanAs": "Save Plan As", "cannotLocked": "No track selected, cannot be locked", "modificationcompleted": "Modification Completed", "lockInOrder": "Please lock in order", "modifyPlan": "Edit Plan", "lookTip": "Please lock trajectories from top to bottom", "entryPointInformation": "Entry Point Information", "temporalMuscleThickness": "Temporal Muscle Thickness:(mm)", "temporalMuscleThicknessShort": "Temp Mu<PERSON>cle <PERSON>", "skullThickness": "Skull Thick:(mm)", "skullThicknessShort": "Skull Thick", "noMatch": "No Match Entry Point", "lookDeleteTip": "Cannot be deleted while locked", "finshTrajectorysTip": "The trajectory of the target has been modified, Should be locked before saving", "deleteTrajectoryTip": "Confirm to delete this Trajectory?", "trajectorysTip": "To ensure effective sEEG plan, the targets are sorted by the number of candidate trajectories ascending. Please lock trajectories from top to bottom. The conflicting trajectories of remaining targets will be removed", "noTrajectoryMessage1": "The target is too close to the blood vessel ", "noTrajectoryMessage2": "The target is too close to the ventricle ", "noTrajectoryMessage3": "The target is too close to the cerebellum ", "noTrajectoryMessage4": "The candidate trajectories length of the target is larger than the length of the electrode ", "noTrajectoryMessage5": "The target is located near the hippocampus and amygdala, and the safe entry point could not be found in the temporal lobe ", "noTrajectoryMessage6": "The entry angles of the candidate trajectories are too large (> 30 degrees) ", "noTrajectoryMessage7": "Candidate trajectories at a safe distance from the vessel could not be found ", "noTrajectoryMessage8": "Candidate trajectories at a safe distance from the ventricle could not be found ", "noTrajectoryMessage9": "Candidate trajectories at a safe distance from the cerebellum could not be found ", "noTrajectoryMessage10": "Conflict with existing trajectory", "noTrajectoryMessage11": "The thickness of trajectories in temporal lobe over mean thickness", "noTrajectoryMessage12": "Trajectories perpendicular to ear", "anat": "Entry Anat"}}, "subject": {"subjectId": "Subjct ID: {value}", "createAt": "Created At: {value}", "scanData": "Scan Data: {value}", "loadMore": "Load More...", "cantDelete": "Cannot Delete", "cantDeleteOfJob": "Cannot delete this subject because there is scan data being processed.", "cantDeleteOfJobs": "Cannot delete this subject because there is scan data being processed.", "cantRerun": "<PERSON><PERSON>", "deleteUpload": "Are you sure you want to delete this upload", "subjectData": "Subject Data", "newSeegPlan": "New Plan", "all": "All", "upload": "<PERSON>an <PERSON>", "data": "Data", "seegPlan": "Plans", "display": "Display", "computing": "Planning, please check back later", "failedOpenContainer": "Failed, no result found", "delPlanTitle": "Are you sure you want to delete this plan?", "delTitleTwoWay": "If you delete this data, it will be delete from both Cloud Medical Image Data Processing Software and Galaxy Navigator Server", "delSubjectTitleTwoWay": "If you delete this subject, it will be delete from both Cloud Medical Image Data Processing Software and Galaxy Navigator Server", "notDataBts": "Please upload scan data and then create new plan", "notSeegBts": "Click button to create new plan", "readySeegBts": "Data is still being prepared, please create plan once the data preparation is complete", "seeg": "Automatic path planning", "safetySeeg": "Manual path planning", "resection": "Resection", "notification": {"title": "message", "samePlanStatus110": "Plan is already created and completed", "samePlanStatus20": "Plan is already created and is being processed", "samePlanStatus100": "Plan is already created and failed", "successInfo20": "This resection/Ablation surgical plan is already created and is being processed, please check back later", "successInfo100": "This resection/Ablation surgical plan is already created and failed", "successInfo0": "It will take some time to generate plan results"}}, "selectType": {"registered": "The scan data is being registered with T1 scan, please check back later", "T1": "T1", "Bold": "Bold", "Vessel": "<PERSON><PERSON><PERSON>", "Skull": "Skull", "lackVesselType": "CTA, MRA, MRV", "lackSkullType": "CT, CTA", "lack": "There is no {type} scan, please upload {value} scan and then create new plan", "seegInfo": "SEEG Surgery Planning requires MRI T1 data and CT (CTA or MRA) data. MRI BOLD data isn't required, but is highly recommended for check functional network information of the trajectories", "resactionInfo": "Resection/ablation surgical plan requires MRI T1 data and MRI BOLD data. CT Data isn't required but is recommended.", "seeg": "Automatic path planning", "safe": "Manual path planning", "resaction": "Resection/Ablation", "title": "Select Plan Type", "selectScan": "Select Scan Data", "doPlanning": "Plan"}, "seeg": {"newSeeg": "New Plan", "title": "Plans", "surgeryPlanningType": "Plan Type: {value}", "fileType": "File Type: {value}", "resection": "Resection/Ablation", "seeg": "Automatic path planning", "seegTableTitle": "Automatic path planning", "saseeg": "Manual path planning", "safety": "Manual path planning", "canDeleteTip1": "Download is being prepared, cannot delete plan", "planningcanDeleteTip2": "Planning, cannot delete plan", "canDownloadTip": "Download is being prepared, please try again later", "canreDownloadTip": "Download is being prepared, please try again later"}, "upload": {"datId": "Data ID: {value}", "fileName": "File Name: {value}", "uploadedAt": "Created At: {value}", "fileType": "File Type: {value}", "data": "Data", "plan": "Plan", "stauts": "Status: {value}", "modalTitle": "Select the purpose of viewing data", "modalFuseTitle": "Show scan data registration status", "fuseEmpty": "Fuse Empty", "uploadFailure": "The current data has no processing results to view", "uploadProgerss": "Scan data is being registered. Please create plan after scan data is registered", "uploadModalLabel": "Select Scan", "tableFileName": "File Name", "dataId": "Data ID", "createdAt": "Created At", "scanType": "Scan Type", "jobStatus": "Fusion Status"}, "viewTool": "View Detail", "tool": {"10": "Planning, please export results after planning is finished", "15": "Planning, please export results after planning is finished", "20": "Planning, please export results after planning is finished", "100": "Cannot export because there is no valid trajectory", "110": "Export planning result in T1 space", "120": "Planning results are being exported, please download results after the export is finished", "130": "Download the exported results", "140": "Planning results expord failed, cannot be downloaded"}, "deleteTool": {"10": "Planning, can delete plan", "15": "Planning, cannot delete plan", "20": "Planning, cannot delete plan", "100": "Delete Plan", "110": "Delete Plan", "120": "Planning results are being exported, cannot delete plan", "130": "Delete Plan", "140": "Delete Plan"}, "rerunTool": {"10": "Planning, please try to rerun later", "15": "Planning, please try to rerun later", "20": "Planning, please try to rerun later", "100": "Rerun Plan", "110": "Rerun Plan", "120": "Planning results are being exported, please try to rerun later", "130": "Rerun Plan", "140": "Rerun Plan"}, "status": {"10": "Planning", "15": "Planning", "20": "Planning", "100": "Plan Failed", "110": "Plan Completed", "120": "Exporting", "130": "Export Completed", "140": "Export Failed", "400": "Sync Failed", "500": "Sync Pending"}, "resection18etWorkName": {"0": "<PERSON><PERSON>", "1": "Lateral Visual Network", "2": "Medial Visual Network", "3": "Limb Sensorimotor Network", "4": "Facial Sensorimotor Network", "5": "Dorsal Attention Network A", "6": "Dorsal Attention Network B", "7": "Ventral Attention Network A", "8": "Ventral Attention Network B", "9": "Temporal Limbic Network", "10": "Frontal Limbic Network", "11": "Medial Parietal Network", "12": "Executive Control Network A", "13": "Executive Control Network B", "14": "Language Network B", "15": "Episodic Memory Network", "16": "Default Mode Network", "17": "Language Network A", "18": "Upper-limb Sensorimotor Network"}, "seeg18etWorkName": {"0": "<PERSON><PERSON>", "1": "Lateral Visual Network", "2": "Medial Visual Network", "3": "Lower-limb Sensorimotor Network", "4": "Facial Sensorimotor Network", "5": "Dorsal Attention Network A", "6": "Dorsal Attention Network B", "7": "Ventral Attention Network A", "8": "Ventral Attention Network B", "9": "Temporal Limbic Network", "10": "Frontal Limbic Network", "11": "Medial Parietal Network", "12": "Executive Control Network A", "13": "Language Network A", "14": "Language Network B", "15": "Episodic Memory Network", "16": "Default Mode Network", "17": "Executive Control Network B", "18": "Upper-limb Sensorimotor Network"}, "job18etWorkName": {"0": "<PERSON><PERSON>", "1": "Lateral Visual Network", "2": "Medial Visual Network", "3": "Lower-limb Sensorimotor Network", "4": "Facial Sensorimotor Network", "5": "Dorsal Attention Network A", "6": "Dorsal Attention Network B", "7": "Ventral Attention Network A", "8": "Ventral Attention Network B", "9": "Temporal Limbic Network A", "10": "Frontal Limbic Network", "11": "Medial Parietal Network", "12": "Executive Control Network A", "13": "Executive Control Network B", "14": "Language Network B", "15": "Episodic Memory Network", "16": "Default Mode Network", "17": "Language Network A", "18": "Upper-limb Sensorimotor Network"}, "riskMapName": {"0": "<PERSON><PERSON>", "1": "Language", "2": "Limb Motor", "3": "Limb Motor", "4": "Facial Motor", "5": "Visual"}, "surgePlanTable": {"subjectId": "Subject ID", "planType": "Plan Type", "createdAt": "Created At", "scanType": "Scan Type", "status": "Status", "action": "Action", "msg": {"download": "File preparation, please download later", "delete": "Are you sure you want to delete this plan?", "rerun": "Please prepare the file again later", "prompt": "Prompt", "downloadContent": "Plan results are being exported, please check back later to download the results"}}, "subjectsTable": {"scanData": "No. Scan Data", "planData": "No. Plans", "latePlanStatus": "Latest Plan Status", "uploadTip": "Upload Scan Data", "msg": {"delete": "Are you sure you want to delete this subject?", "prompt": "Prompt", "dataBeingProcessed": "There were data being processed under the subject."}}}, "safety": {"appendTrajectory": "Added Target：", "exportData": "Export Result", "controlTitle": "Trajectory info:", "titlePlaceHold": "Please enter Trajectory name", "next": "Next", "done": "Done", "cancel": "Cancel", "riskValue": "Risk Value", "suefaceClickWarn": "Drop down or click to select target region", "delAlert": "Are you sure to delete this trajectory?", "invalidRegion": "Invalid region, please select again", "riskLabel": "If the risk value is greater than 0.7, it is high risk, 0 risk is the lowest, 1 risk is the highest, and the risk value will be calculated according to the added trajectory", "card": {"title": "Trajectory Name", "targetRegion": "Target Region", "target": "Target", "entryRegion": "Entry Region", "entry": "Entry Point", "targetPlaceholder": "Drop down or click to select target region", "entryPlaceholder": "Drop down or click to select cortical entry point", "coordsPlaceholder": "Click/Enter Coordinates on Volume View"}, "cortexWarn": "Please select entry point in cortical"}, "routeError": {"goHome": "Go Home", "goLogin": "<PERSON>", "404": "Sorry, the page you visited does not exist."}, "patchName": {"Unknown": "Unknown", "G_and_S_frontomargin": "G_and_S_frontomargin", "G_and_S_occipital_inf": "G_and_S_occipital_inf", "G_and_S_paracentral": "G_and_S_paracentral", "G_and_S_subcentral": "G_and_S_subcentral", "G_and_S_transv_frontopol": "G_and_S_transv_frontopol", "G_and_S_cingul-Ant": "G_and_S_cingul-Ant", "G_and_S_cingul-Mid-Ant": "G_and_S_cingul-Mid-Ant", "G_and_S_cingul-Mid-Post": "G_and_S_cingul-Mid-Post", "G_cingul-Post-dorsal": "G_cingul-Post-dorsal", "G_cingul-Post-ventral": "G_cingul-Post-ventral", "G_cuneus": "G_cuneus", "G_front_inf-Opercular": "G_front_inf-Opercular", "G_front_inf-Orbital": "G_front_inf-Orbital", "G_front_inf-Triangul": "G_front_inf-Triangul", "G_front_middle": "G_front_middle", "G_front_sup": "G_front_sup", "G_Ins_lg_and_S_cent_ins": "G_Ins_lg_and_S_cent_ins", "G_insular_short": "G_insular_short", "G_occipital_middle": "G_occipital_middle", "G_occipital_sup": "G_occipital_sup", "G_oc-temp_lat-fusifor": "G_oc-temp_lat-fusifor", "G_oc-temp_med-Lingual": "G_oc-temp_med-Lingual", "G_oc-temp_med-Parahip": "G_oc-temp_med-Parahip", "G_orbital": "G_orbital", "G_pariet_inf-Angular": "G_pariet_inf-Angular", "G_pariet_inf-Supramar": "G_pariet_inf-<PERSON><PERSON><PERSON>", "G_parietal_sup": "G_parietal_sup", "G_postcentral": "G_postcentral", "G_precentral": "G_precentral", "G_precuneus": "G_precuneus", "G_rectus": "G_rectus", "G_subcallosal": "G_subcallosal", "G_temp_sup-G_T_transv": "G_temp_sup-G_T_transv", "G_temp_sup-Lateral": "G_temp_sup-Lateral", "G_temp_sup-Plan_polar": "G_temp_sup-Plan_polar", "G_temp_sup-Plan_tempo": "G_temp_sup-Plan_tempo", "G_temporal_inf": "G_temporal_inf", "G_temporal_middle": "G_temporal_middle", "Lat_Fis-ant-Horizont": "Lat_<PERSON>s-<PERSON>-<PERSON><PERSON>", "Lat_Fis-ant-Vertical": "Lat_Fis-ant-Vertical", "Lat_Fis-post": "Lat_Fis-post", "Medial_wall": "Medial_wall", "Pole_occipital": "Pole_occipital", "Pole_temporal": "Pole_temporal", "S_calcarine": "S_calcarine", "S_central": "S_central", "S_cingul-Marginalis": "S_cingul-<PERSON><PERSON><PERSON>", "S_circular_insula_ant": "S_circular_insula_ant", "S_circular_insula_inf": "S_circular_insula_inf", "S_circular_insula_sup": "S_circular_insula_sup", "S_collat_transv_ant": "S_collat_transv_ant", "S_collat_transv_post": "S_collat_transv_post", "S_front_inf": "S_front_inf", "S_front_middle": "S_front_middle", "S_front_sup": "S_front_sup", "S_interm_prim-Jensen": "S_interm_prim-<PERSON>", "S_intrapariet_and_P_trans": "S_intrapariet_and_P_trans", "S_oc_middle_and_Lunatus": "S_oc_middle_and_Lunatus", "S_oc_sup_and_transversal": "S_oc_sup_and_transversal", "S_occipital_ant": "S_occipital_ant", "S_oc-temp_lat": "S_oc-temp_lat", "S_oc-temp_med_and_Lingual": "S_oc-temp_med_and_Lingual", "S_orbital_lateral": "S_orbital_lateral", "S_orbital_med-olfact": "S_orbital_med-olfact", "S_orbital-H_Shaped": "S_orbital-<PERSON>_<PERSON>haped", "S_parieto_occipital": "S_parieto_occipital", "S_pericallosal": "S_pericallosal", "S_postcentral": "S_postcentral", "S_precentral-inf-part": "S_precentral-inf-part", "S_precentral-sup-part": "S_precentral-sup-part", "S_suborbital": "S_suborbital", "S_subparietal": "S_subparietal", "S_temporal_inf": "S_temporal_inf", "S_temporal_sup": "S_temporal_sup", "S_temporal_transverse": "S_temporal_transverse", "unknown": "unknown", "bankssts": "bankssts", "caudalanteriorcingulate": "caudalanteriorcingulate", "caudalmiddlefrontal": "caudalmiddlefrontal", "corpuscallosum": "corpuscallosum", "cuneus": "cuneus", "entorhinal": "entorhinal", "fusiform": "fusiform", "inferiorparietal": "inferiorparietal", "inferiortemporal": "inferiortemporal", "isthmuscingulate": "isthmuscingulate", "lateraloccipital": "lateraloccipital", "lateralorbitofrontal": "lateralorbitofrontal", "lingual": "lingual", "medialorbitofrontal": "medialorbitofrontal", "middletemporal": "middletemporal", "parahippocampal": "parahippocampal", "paracentral": "paracentral", "parsopercularis": "parsoper<PERSON>is", "parsorbitalis": "parsorbitalis", "parstriangularis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pericalcarine": "pericalcarine", "postcentral": "postcentral", "posteriorcingulate": "posteriorcingulate", "precentral": "precentral", "precuneus": "precuneus", "rostralanteriorcingulate": "rostralanteriorcingulate", "rostralmiddlefrontal": "rostralmiddlefrontal", "superiorfrontal": "superiorfrontal", "superiorparietal": "superiorparietal", "superiortemporal": "superiortemporal", "supramarginal": "supramarginal", "frontalpole": "frontalpole", "temporalpole": "temporalpole", "transversetemporal": "transversetemporal", "insula": "insula", "Left-Lateral-Ventricle": "Left-Lateral-Ventricle", "Left-Inf-Lat-Vent": "Left-Inf-Lat-Vent", "Left-Cerebellum-White-Matter": "Left-Cerebellum-White-Matter", "Left-Cerebral-White-Matter": "Left-Cerebellum-White-Matter", "Left-Cerebellum-Cortex": "Left-Cerebellum-Cortex", "Left-Thalamus-Proper": "Left-Thalamus-Proper", "Left-Caudate": "Left-Caudate", "Left-Putamen": "Left-Putamen", "Left-Pallidum": "Left-<PERSON><PERSON><PERSON><PERSON>", "3rd-Ventricle": "3rd-<PERSON><PERSON><PERSON><PERSON>", "4th-Ventricle": "4th-<PERSON><PERSON><PERSON><PERSON>", "Brain-Stem": "Brain-Stem", "Left-Hippocampus": "Left-Hippocampus", "Left-Amygdala": "Left-<PERSON><PERSON><PERSON><PERSON>", "CSF": "CSF", "Left-Accumbens-area": "Left-Accumbens-area", "Left-VentralDC": "Left-VentralDC", "Left-vessel": "Left-vessel", "Left-choroid-plexus": "Left-choroid-plexus", "Right-Lateral-Ventricle": "Right-Lateral-Ventricle", "Right-Inf-Lat-Vent": "Right-Inf-Lat-Vent", "Right-Cerebellum-White-Matter": "Right-Ce<PERSON>bellum-White-Matter", "Right-Cerebral-White-Matter": "Right-Ce<PERSON>bellum-White-Matter", "Right-Cerebellum-Cortex": "Right-Cerebellum-Cortex", "Right-Thalamus-Proper": "Right-<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "Right-Caudate": "Right-<PERSON><PERSON><PERSON>", "Right-Putamen": "Right-Putamen", "Right-Pallidum": "Right-<PERSON><PERSON><PERSON><PERSON>", "Right-Hippocampus": "Right-Hippocampus", "Right-Amygdala": "Right-<PERSON><PERSON><PERSON><PERSON>", "Right-Accumbens-area": "Right-Accumbens-area", "Right-VentralDC": "Right-VentralDC", "Right-vessel": "Right-vessel", "Right-choroid-plexus": "Right-choroid-plexus", "5th-Ventricle": "5th-<PERSON><PERSON><PERSON><PERSON>", "WM-hypointensities": "WM-hypointensities", "Left-WM-hypointensities": "Left-WM-hypointensities", "Right-WM-hypointensities": "Right-WM-hypointensities", "non-WM-hypointensities": "non-WM-hypointensities", "Left-non-WM-hypointensities": "Left-non-WM-hypointensities", "Right-non-WM-hypointensities": "Right-non-WM-hypointensities", "Optic-Chiasm": "Optic-Chiasm", "CC_Posterior": "CC_Posterior", "CC_Mid_Posterior": "CC_Mid_Posterior", "CC_Central": "CC_Central", "CC_Mid_Anterior": "CC_Mid_Anterior", "CC_Anterior": "CC_Anterior"}, "aboutUs": {"productionName": "Product Name", "boxAboutUs": "MRI Image Data Processing Software", "dataServerAboutUs": "Cloud Medical Image Data Processing Software", "boxSystemAboutUs": "MRI Image Data Processing System", "productModel": "Product Model", "manufacturer": "Manufacturer", "productionAddress": "Address", "telephone": "Telephone", "email": "E-mail", "version": "Version", "website": "Website", "downloadLog": "Export Log", "modelNumberMRI": "pBFS-Analyzer", "modelNumberCloud": "PNT-100SW", "modelNumberSystem": "PTC", "manufacturerContent": "Neural Galaxy(HuNan) Technology Co., Ltd.", "productionAddressContent": "Room 1201-1208, 12 Floor, No.1 Department, Hai Ping Yuan Park, No. 229 at Gu Yuan Road, Changsha Hi-Tech Industrial Development Zone, Changsha, Hunan Province, PRC", "contactUs": "Contact Us"}, "ipLimit": {"title": "Access restriction", "range": "Access restriction", "description": "Description", "action": "Actions", "add": "New access restriction", "addTitle": "New access restriction", "editTitle": "Edit access restriction", "ipType": "IP Type", "ip": "IP address", "from": "From", "to": "To", "delTitle": "Are you sure delete this access restriction?", "type": {"assign": "Assign IP address", "match": "Fuzzy designation of IP address", "range": "IP address range"}, "login": {"title": "Need specify IP access restriction", "reject": "Reject & Exit", "setting": "Setting"}, "reject": "IP access restricted, please contact your IT admin", "alert": {"ip": "Please enter the correct IP address", "range": "Wrong IP address range"}, "info": {"title": "Instruction", "1": " Input by fixed IP address- Need to input specific IP address for access. Example *************", "2": " Input by fuzzy IP address.  From the end to the first(the 4th IP section to the 1st IP section), \nyou can input * orderly, for example \"123.126.82.*\" means free access for ************ to **************, \n\"123.126.*.*\" implies permission for ***********-***************, \n\"*.*.*.*\" stands for all IP allowed.", "3": "Input by specific IP address range\n*************-*************"}}, "point": {"exit": "安全退出硬件", "header": {"home": "患者列表", "treatment": "治疗方案", "person": "患者", "newPerson": "新建治疗", "stimulateTemplate": "刺激模板", "processBar": "传输进度"}, "jumpToMotion": "导航追踪运动区靶点", "thresholdLabel": "请在下方输入运动阈值，或者通过导航追踪运动区靶点的方式测量运动阈值", "jumpToMotionPlaceholder": "在视图上高亮展示该患者的运动脑区，并确定运动区靶点，通过导航追踪靶点位置并观察波幅以得到运动阈值", "downloadLog": {"fileName": "文件名称", "outputPath": "导出路径", "success": "已完成", "error": "出错了", "errorInfo": "错误信息：{err}", "info": "请拔出U盘，将日志文件发送给技术支持"}, "upload": {"headTitle": "选择数据上传方式", "scan": "扫描数据", "reselect": "重选文件", "selected": "选中数据", "success": "数据上传成功", "successInfo": "处理结果将在24小时内展示", "home": "首页", "patient": "患者详情页", "emptyTitle": "警告", "emptyContent": "数据规格不符，请选择存在T1类型数据的文件目录", "screeningLabel": "筛查", "screening1": "有过癫痫发作", "screening2": "有过中风", "screening3": "有设备植入", "screening4": "有头部损伤", "screening5": "正服用精神或神经活性药物", "diseaseInfo": "说明", "diseaseInfoPlaceholder": "请填写说明内容", "remark": "方案备注名", "remarkPlaceholder": "请填写方案备注名", "loading": "正在加载文件，可能需要几分钟，请耐心等待...", "overSize": "您选择的数据已超过上传限制，请减少选择文件数量（DICOM文件1.7G/NIFTI文件800M）"}, "disease": {"label": "疾病", "other": "其他", "alert": "请至少选择一项疾病", "Aphasia": "失语症", "Depression": "抑郁症", "Insomnia": "失眠", "Hemiplegia": "偏瘫", "Parkinson": "帕金森", "1": "失语症", "2": "抑郁症", "3": "请输入疾病类型"}, "createSource": {"localUpload": "本地上传", "pacsGain": "磁共振获取", "reprocess": "重新处理", "retreatment": "再次治疗", "modifyPlan": "修改方案"}, "home": {"hideListFirst": "列表内容已隐藏（共{total}条）", "hideListSecond": "点击“刷新”图标可展示列表详情", "search": {"placeholder": "搜索患者名字、患者ID、数据名称", "clear": "清空过滤器"}, "selectLabel": "状态", "audit": {"menu": {"await": "待审核任务", "all": "所有任务"}, "emptyTable": "暂无待审核任务"}, "table": {"name": "姓名", "patientId": "患者ID", "sex": "性别", "age": "年龄", "type": "疾病", "noStart": "未开始治疗", "noAge": "未知", "updateAt": "最近一次治疗", "uploadAt": "上传时间", "folderName": "扫描数据名称", "project": "状态", "action": "操作", "cure": "治疗", "empty": "无患者，点击按钮添加", "emptyPlanButHavePatient1": "无治疗任务记录", "emptyPlanButHavePatient2": "可点击按钮新建患者后上传数据以创建治疗任务", "emptyPlanButHavePatient3": "无已完成的治疗任务", "createButton": "新建患者", "emptyPlan": "没有治疗任务，点击按钮添加", "createPlanButton": "新建任务", "deleteConfirm": "您确定要删除该患者吗", "detailToolTitle": "查看详情", "deleteToolTitle": "删除患者", "editToolTitle": "编辑患者", "uploadToolTitle": "上传数据", "updateRecordToolTitle": "修改历史", "noDelete": "治疗方案处理中，不可删除此患者", "actionTool": {"detailPlan": "查看方案详情", "report": "查看报告", "upload": "上传数据", "detailPatient": "查看患者详情", "goBack": "恢复", "edit": "编辑", "goBackTwiceConfirm": "确认要恢复吗？", "view": "查看方案", "audit": "审核", "resultOf": "查看原因"}, "createType": {"title": "创建方式", "1": "本地上传", "2": "修改方案", "3": "重新处理", "4": "再次治疗", "5": "核磁共振获取"}, "lastTreatmentAt": "上次治疗时间", "createAt": "创建时间", "createAtUser": "创建者", "realtimeCureTime": "最近一次治疗", "cureUser": "治疗师", "mri": "MRI", "cureProjectNumber": "治疗方案数量", "hadCureNumber": "实施治疗次数", "deleteTime": "删除时间", "deleteUser": "删除者", "audit": {"orgName": "机构名称", "patientName": "患者姓名", "patientCode": "患者ID", "disease": "病症", "status": "状态", "statusUpdateAt": "状态变更时间", "auditTime": "方案完成时间", "auditResult": "审核结果", "auditUser": "审核人", "recentUpdateAt": "最近修改时间", "recentPlanCreateAt": "最近任务创建时间"}}, "planTooltip": {"15": "治疗任务排队中，请稍后查看", "20": "治疗任务处理中，请稍后查看", "90": "处理失败，暂无结果可查看"}}, "edit": {"goBack": "返回", "goBackCancel": "留在当前页面", "goBackOk": "确认退出", "editTitle": "修改患者信息", "addTitle": "添加患者信息", "firstName": "姓", "firstNamePlaceholder": "请输入姓", "lastName": "名", "lastNamePlaceholder": "请输入名", "patientId": "患者ID", "patientIdPlaceholder": "请输入患者病历号", "hadPatientInDB": "该患者已存在", "hadPatientInDBButNotPermission": "该患者已存在,请至患者列表页查看", "sex": "性别", "sexPlaceholder": "请选择性别", "birth": "出生日期", "birthPlaceholder": "请选择日期或者输入如下格式的日期（2000-01-01）后回车", "telephone": "手机号", "telephonePlaceholder": "请输入手机号", "remark": "备注", "remarkPlaceholder": "请输入备注", "cancel": "取消", "uploadButton": "上传数据", "havePatientDb": "使用已有患者信息", "recoverPatient": "恢复已删除患者", "goBackTitle": "警告", "goBackContent": "若退出当前页面，不会保存已经填写的信息", "nameError": "仅允许输入中文、字母、数字、 -、_", "nameErrorMin": "最少输入一位", "nameErrorMax": "至多输入10位", "nameErrorRequired": "不能为空", "telephoneError": "请输入符合实际的手机号", "commentErrorMax": "至多输入300位", "patientCodeMin": "至少输入1位", "patientCodeMax": "至多输入40位", "patientCodeMin6": "至少输入6位", "patientCodeMax20": "至多输入20位", "tooltipText": "查询到已存在相同患者的ID，若使用已有患者信息，将更新已有患者信息", "patientCodeMust": "患者ID不能为空", "org": "机构"}, "report": {"goBackNavigate": "报告详情", "title": "TMS治疗报告", "download": "下载报告", "doctor": {"account": "账户", "username": "操作者", "createdAt": "生成时间", "org": "机构"}, "basic": {"title": "基本信息", "name": "姓名", "patientId": "病历号", "sex": "性别", "age": "年龄", "type": "疾病", "position": "患侧", "telephone": "手机号", "remark": "病程", "motionScope": "运动阈值"}, "diseaseFilter": {"title": "病状筛查信息", "disease": "疾病", "screening": "筛查信息", "info": "说明", "treatmentCount": "计划治疗次数", "remainTreatmentCount": "已经治疗次数"}, "cure": {"title": "治疗详情"}, "spot": {"newTitle": "治疗方案", "MEP": "MEP", "title": "靶点", "name": "靶点名称:", "run": "脉冲刺激", "purse": "脉冲暂停", "over": "脉冲结束", "tmsFail": "TMS异常", "simpleVolSeed": "坐标(vol RAS) :", "simpleSurfSeed": "坐标(surf RAS) :", "volSeed": "靶点坐标(vol RAS) :", "surfSeed": "靶点坐标(surf RAS) :", "point": "顶角编号 :", "region": "功能分区", "loopDes": "环路描述", "strength": "强度(%):", "pulseTotal": "脉冲个数:", "treatmentTime": "时长(s):"}, "params": {"titleProject": "刺激方案", "title": "刺激参数", "frequency": "频率(Hz):", "exciteTime": "刺激时间(s):", "exciteNum": "刺激个数:", "spaceTime": "间隙时间(s):", "repetitionNum": "重复次数:", "cureTime": "治疗时间:"}, "project": {"title": "治疗时间", "index": "频次", "updatedAt": "时间"}, "conclusion": {"title": "结论"}, "treatmentCycle": "治疗周期", "cureTable": {"startCureTime": "刺激开始时间", "endCureTime": "刺激停止时间", "allTime": "实际刺激时长(s)"}, "saveReportSuccess": "保存报告成功！", "saveReportFailed": "保存报告失败！", "exportPointSuccess": "导出成功", "exportPointFailed": "导出失败，请重试。", "pointPlanTitle": "导出NG方案", "planTitle": "导出JSON方案", "simpleReportTitle": "报告", "pdfReportTitle": "图文报告", "extraInfoTitle": "补充信息", "exportPlanTitle": "导出或下发方案", "diseaseDescription": "病情描述", "planDetail": "方案详情", "surfaceLoadingTips": "脑图加载中，预计需要2~8秒，请耐心等待", "picLoadFailed": "图片加载失败", "exportSimpleReportFailed": "报告下载失败，请重新下载", "exportPDFReportFailed": "图文报告下载失败，请重新下载", "treatmentCycleDefaultContent": "每轮治疗中间时间间隔：50min\n每次__轮， 连续__次"}, "cureProject": {"invalidProject": "无效方案", "haveRestingNoStart": "该患者存在休息中的治疗方案，无法开始本次治疗", "planDataError": "该方案数据加载可能不全面,请斟酌使用", "overlaySurfaceFileNotFound": "该方案因为数据问题，无法查看脑图结构分区", "whereHeIsGo": "该患者注册方案在有效期, 请选择注册流程", "copySuccessMessage": "请至首页查看新方案", "whereHeIsGoTitle": "提示", "goBackNavigate": "返回", "illnessLabel": "疾病", "title": "请选择治疗靶点", "parchType": "分区标记", "cureSpotTitle": "治疗靶点", "treatmentForm": {"count": "治疗次数", "must": "必填项", "placeholder": "请填写治疗次数"}, "spot": {"type": "正刺激", "deleteConfirm": "您确定要删除吗", "actionIcon": {"delete": "删除靶点", "export": "导入刺激参数", "save": "保存刺激模板"}, "pointType": "靶点类型", "pointName": "靶点名称", "pointNamePlaceholder": "请输入靶点名称", "pointCoordinate": "靶点坐标(vol RAS)", "pointType1": "治疗靶点", "pointType2": "MEP靶点"}, "options": {"common": "靶点", "sport": "MEP靶点"}, "create": {"title": "添加靶点", "info": "请添加运动区靶点用以测试患者的运动阈值", "spotName": "靶点名称", "showPatch": "显示分区", "notInCort": "您选择的靶点不在皮层上", "notInMotionCort": "您选择的靶点不在运动区皮层上"}, "remark": {"num": "治疗次数", "numPlaceholder": "请填写共需要治疗次数", "isEpilepsy": "是否有过癫痫发作", "isHeadInjured": "是否有过头部受伤", "headerInjuredPosition": "请填写头像损伤位置", "courseOfDisease": "病程", "courseOfDiseasePlaceholder": "请填写病人病程", "yes": "是", "no": "否"}, "params": {"saveTemplate": "保存模板", "templateName": "模板名称", "exportTemplate": "导入模板", "pulse": "脉冲数", "time": "时长", "stopExcite": "停止刺激", "exciteOver": "刺激完成", "button": {"create": "添加刺激类型", "deleteConfirm": "确认删除该刺激类型吗", "deleteRefuse": "不可删除此刺激类型"}, "type": {"label": "刺激类型", "common": "重复刺激", "iTBS": "iTBS", "cTBS": "cTBS", "rTMS": "rTMS", "1": "重复刺激", "2": "iTBS", "3": "cTBS"}, "common": {"stimulateType": "刺激类型", "sumNum": "总脉冲数", "relativeStrength": "相对强度(%)", "strength": "相对强度(%)", "exciteTime": "治疗时间(s)", "frequency": "串脉冲频率(Hz)", "inPulseNum": "串内脉冲数", "pulseNum": "脉冲串数", "exciteInterval": "刺激间隔(s)", "pulseTotal": "总脉冲数", "treatmentTime": "治疗时间(s)", "strandPulseFrequency": "串脉冲频率(Hz)", "innerStrandPulseCount": "串内脉冲数", "strandPulseCount": "脉冲串数", "intermissionTime": "刺激间隔(s)"}, "iTBS": {"stimulateType": "刺激类型", "strength": "相对强度(%)", "plexusInnerFrequency": "丛内频率(Hz)", "plexusInterFrequency": "丛间频率(Hz)", "plexusInnerPulseCount": "丛内脉冲数(个)", "plexusCount": "刺激丛数(丛)", "strandPulseCount": "脉冲串数(串)", "intermissionTime": "刺激间隔(s)", "pulseTotal": "总脉冲数(个)", "treatmentTime": "刺激时长(s)"}, "cTBS": {"stimulateType": "刺激类型", "strength": "相对强度(%)", "plexusInnerFrequency": "丛内频率(Hz)", "plexusInterFrequency": "丛间频率(Hz)", "plexusInnerPulseCount": "丛内脉冲数(个)", "plexusCount": "刺激丛数(丛)", "strandPulseCount": "脉冲串数(串)", "intermissionTime": "刺激间隔(s)", "pulseTotal": "总脉冲数(个)", "treatmentTime": "刺激时长(s)"}, "rTMS": {"stimulateType": "刺激类型", "strength": "相对强度(%)", "strandPulseFrequency": "串脉冲频率(Hz)", "innerStrandPulseCount": "串内脉冲数(个)", "sumNum": "总脉冲数(个)", "strandPulseCount": "脉冲串数(串)", "intermissionTime": "刺激间隔(s)", "pulseTotal": "总脉冲数(个)", "treatmentTime": "刺激时长(s)"}, "noSpot": "请添加治疗靶点", "noSelect": "请选择治疗靶点", "noCount": "请添加治疗次数", "noParam": "请选择刺激参数", "rules": {"mustWrite": "{value}为必填项", "maxValue": "最大允许输入{value}", "minValue": "最小允许输入{value}", "valueLimit": "请输入输入{minValue}-{maxValue}之间的数值", "limit": "超限", "pulseTotalLimit": "总脉冲数超限，范围为{minValue}-{maxValue}", "treatmentTimeLimit": "刺激时长超限，范围为{minValue}-{maxValue}", "strengthWrite": "相对强度超限，当前相对强度值不能高于{value}", "limitWrite": "输出功率超限，请尝试降低{value}参数", "intermissionTimeWrite": "输出功率超限，请尝试增加刺激间隔参数"}}, "footer": {"saveAs": "方案另存为", "save": "保存", "termination": "终止治疗", "edit": "编辑方案", "measureScope": "测运动阈值", "resetMeasureScope": "重测运动阈值", "startCure": "开始治疗", "reStartCure": "再次治疗", "report": "生成治疗报告", "download": "导出方案", "cancel": "取消", "add": "添加靶点", "resting": "休息", "register": "重新注册", "start": "开始刺激", "over": "结束治疗", "log": "查看日志"}, "noSpot": "请添加治疗靶点", "noSelect": "请选择治疗靶点", "noCount": "请添加治疗次数", "noParam": "请选择刺激参数", "addTitle": "添加刺激参数", "editTitle": "修改刺激参数", "preview": {"typeLabel": "疾病", "headInjuredLabel": "头部损伤", "courseOfDiseaseLabel": "病程", "totalLabel": "总治疗数", "havePastLabel": "已治疗次数", "createByUser": "创建者", "createAt": "创建时间", "treatByUser": "治疗师", "treatAt": "治疗时间", "sportSpotScopeLabel": "运动阈值", "measureScopeWarning": "当前患者没有添加运动区靶点位置,点击”添加“以添加运动区靶点，若不添加运动区靶点，请在下方输入运动阈值", "measureScopeWarningStatus50": "请在下方输入运动阈值", "measureScopeModalInputButton": "已输入运动阈值", "terminationContent": "确定要终止此治疗方案吗", "hasEpilepsy": "有癫痫发作史", "noEpilepsy": "无癫痫发作史"}}, "patient": {"new": "新建治疗方案", "empty": "当前患者无治疗方案，可点击按钮新建", "delete": "确定删除此患者?", "refresh": "Refresh", "cantDeleteTip": "有处理中的治疗报方案不能删除", "measureMotionThreshold": "重测运动阈值", "status": "状态:", "deleted": "已删除", "deletedMsg": "已删除信息：", "modifyAt": "修改时间", "modifyHistory": "修改历史", "modifyPeople": "修改者", "patientCard": {"attendingPhysician": "主治医师: {value}", "creatUser": "创建方式: {value}", "lastTreatment": "最后一次: {value}", "pointNum": "靶点数量: {value}", "stopUser": "终止治疗: {value}", "processingTime": "已进行: {value}", "createAt": "创建时间: ", "remainTreatmentCount": "治疗次数: {value}", "seeJobFiles": "查看扫描文件", "reRun": "重新运行", "noDataDes": "无治疗方案,点击按钮上传", "reRunTip": "确认重新处理此治疗方案么？", "delete": "确定删除此治疗方案吗?", "dataName": "数据名称: {value}", "note": "备注", "createSource": {"1": "本地上传", "2": "磁共振获取", "3": "重新处理", "4": "再次治疗", "5": "修改方案"}, "status": {"-1": "全部", "15": "排队中", "20": "处理中", "30": "图像处理完成", "100": "处理失败", "101": "处理失败", "40": "待治疗", "50": "治疗中", "60": "休息中", "110": "治疗完成", "90": "治疗终止"}}, "uploadCard": {"noDataDes": "无扫描数据,点击按钮上传", "noDataBtnTitle": "上传数据"}, "reportCard": {"planCreateAt": "治疗创建时间: {value}", "reportCreateAt": "报告生成时间: {value}", "noData": "暂无治疗方案可生成报告", "noDataDes": "无治疗报告可供查看, 点击按钮生成", "noDataBtnTitle": "查看报告", "delete": "确定删除此治疗报告吗?"}, "form": {"patientCodeFieldName": "患者ID", "genderFieldName": "性别", "ageFieldName": "年龄", "mobileFieldName": "手机号", "lastTreatFieldName": "最后一次治疗", "commentFieldName": "备注", "treatRecordsFieldName": "运动阈值", "createFieldName": "创建信息", "MRIFieldName": "MRI数量", "planFieldName": "治疗方案", "taskFieldName": "任务数量", "treatmentsFieldName": "已实施治疗次数", "treatCountFieldName": "已实施治疗次数", "createdAtFieldName": "创建时间", "createdAtByUserFieldName": "创建者", "updatedAtFieldName": "最后修改", "allPlanCountFieldName": "所有方案", "treatPlanCountFieldName": "已治疗方案"}, "message": {"status15": "系统运算中,请等待状态为待治疗时再点击", "status100": "处理失败，请检查上传的文件"}}, "planList": {"disease": "疾病", "attendingPhysician": "主治医师", "createAt": "创建时间", "statu": "状态", "title": "选择治疗方案生成报告", "createReportTip": "仅有被用于治疗的方案可生成报告", "status": {"15": "处理中", "20": "处理中", "30": "图像处理完成", "100": "处理失败", "40": "待治疗", "50": "治疗中", "110": "治疗完成", "90": "治疗终止"}}, "coil": {"steps": {"one": "选择线圈", "two": "选择标记模块", "three": "注册标记模块", "four": "检验注册结果", "over": "跳过注册流程"}, "leave": {"title": "注册流程未完成，是否结束注册流程？", "ok": "结束注册", "cancel": "继续注册"}, "reMotionThresholdRecords": {"title": "当前运动阈值为 {value}, 是否需要重新测量？", "ok": "重新测量", "cancel": "开始治疗"}, "select": {"action": "操作", "delAlert": "你确定要删除这个线圈吗？", "name": "名称", "tracker": "追踪器", "lastValid": "最后一次校准", "lastUsed": "最后一次使用", "new": "新建线圈", "reset": "重新校准", "nowTms": "现有TMS线圈", "validTitle": "选择线圈后请使用定标枪点击线圈上已标定的位置", "validInfo1": "距离小于5毫米，则视为线圈与线圈定标球相对位置准确", "validInfo2": "若多次尝试无法附和相对位置要求，请尝试重新校准线圈", "confirmCoil": "确认标记点", "useCoil": "使用此线圈", "distance": "相对距离（mm）", "resetDistance": "重新确认标记点", "warning": "相对距离较大，请重新确认标记点以确保注册准确性"}}, "newCoil": {"newCoil": "新建线圈", "deleteCoil": "删除线圈", "resetCoil": "重新校准线圈", "coilType": "线圈型号", "coilTypePlaceholder": "线圈型号", "coilName": "线圈名称", "coilNamePlaceholder": "请填写线圈名称", "fromTip": "请将线圈追踪装置绑定到线圈上", "coilNamePattern": "1-10 位数字/字母/中文/_ /-", "coilRegistered": {"title": "请使用指针依次点击5个标记点", "coilRegistered1": "标记点 1", "coilRegistered2": "标记点 2", "coilRegistered3": "标记点 3", "coilRegistered4": "标记点 4", "coilRegistered5": "标记点 5", "registeredButton": "注册标记点", "reRegisteredButton": "重新注册标记点", "relativeDistance": "相对距离(mm)：", "errTip1": "请选择线圈型号", "errTip2": "请填写线圈名称", "errTip3": "请注册线圈", "coilLabel": "线圈"}, "stimulate": {"fakeStimulate": "伪刺激模式", "strength": "强度衰减（%）："}}, "mark": {"validate": {"title": "验证注册的准确性", "info": "将指针沿着患者的头部皮肤移动，并观测皮肤与指针间的相对距离", "detail1": "小于3毫米证明注册结果精准", "detail2": "在3-5毫米之间证明注册结果相对精准", "detail3": "大于5毫米证明注册结果有误差，为了保证治疗的精准，请重新注册", "less": "小于", "notFound": "无法找到定标枪位置"}, "edit": {"preName": "标记点", "name": "名称", "added": "已添加的标记点", "info1": "在图上至少选择4个标记点以用于匹配图像和实际患者的关系。", "info2": "建议选择鼻根、鼻尖、耳珠上的凹凸处。"}, "register": {"title": "校准物理实际空间和影像的三维空间", "title_sub": "全部注册完成后可选择其中一个点重新注册", "info": "请使用指针在患者头部依次点击如下标记点", "confirm": "确认标记点", "reset": "重新注册标记点", "reConfirm": "重新确认标记点"}}, "preview": {"description": "扫描名称", "date": "时间", "type": "类型", "number": "序列号", "tip": "请同时选择T1和Bold类型数据上传", "tipT1": "请选择T1数据上传"}, "scaler": {"title": "物体是否在相机可监测范围内", "warning": "已超出监测范围", "scaler": "定标枪", "coil": "线圈", "patient": "患者", "unbind": "解除绑定", "unbindAlert": "你确定解除与该定标笔的绑定吗？", "model": {"title": "请选择定标抢型号并绑定", "tip": "请按一下定标笔上的按键或者点击下方对应的按钮，进行绑定", "bind": "绑定"}, "viewerTitle": "相机视野展示及边界示意"}, "treatment": {"complete": "本次治疗完成", "distance": "靶点距离", "angle": "法线夹角", "translation": "平移距离", "coil": "手持视角", "subject": "观察视角", "translationLabel": "平移指示", "angleLabel": "旋转示意", "view": "视图模式"}, "threshold": {"label": "阈值", "complete": "已完成运动阈值测试", "addSpot": "添加运动区靶点", "addSpotInfo": "请在皮层表面视图，已高亮的运动区位置上点击选择靶点，或在输入框中输入坐标"}, "dicom": {"table": {"node": "节点ID", "orgName": "机构名称", "createAt": "注册时间", "ip": "节点IP", "onLineStatus": "在线状态", "statusNumber": {"0": "在线", "1": "离线"}, "lastBeatTime": "最后心跳时间", "beatAction": "心跳记录", "time": "时间", "status": "状态", "damageStartTime": "中断开始", "recoverTime": "中断结束", "timeConsuming": "中断时长", "action": {"allReport": "所有记录", "damageReport": "中断记录"}, "modal": {"allTitle": "所有心跳记录", "damageTitle": "中断心跳记录"}}}, "audit": {"comment": "备注说明", "auditPage": {"title": "审核结果", "time": "时间", "cureSpot": "治疗靶点", "motionSpot": "MEP靶点"}, "result": {"1": "待审核", "2": "无需审核", "3": "审核不过", "4": "审核通过"}, "diseaseFilter": {"screening": {"1": "有过癫痫发作", "2": "有过中风", "3": "有植入设备", "4": "有过头部损伤", "5": "正服用精神或神经活性药物"}}, "operate": {"report": "查看报告", "view": "查看方案", "audit": "审核", "resultOf": "查看原因"}, "planStatus": {"15": "排队中", "20": "处理中", "30": "待审核", "40": "待治疗", "50": "治疗中", "90": "治疗终止", "100": "处理失败", "101": "审核不过", "110": "治疗完成", "targetCount": "个靶点"}, "card": {"await": "待审核", "origin": "原始方案", "diseaseFilter": "症状筛查", "auditResult": "已审核", "auditFail": "审核不过", "cureResult": "治疗方案"}, "filter": {"disease": "疾病", "screening": "筛查", "markName": "方案备注名", "markNamePlaceholder": "请填写方案备注名", "comment": "说明", "commentPlaceholder": "请填写说明"}, "container": {"header": {"patientTooltip": "查看患者详情", "colorMap": "分区颜色图样式", "changeLayout": "切换视图布局"}}, "auditFooter": {"passTooltip": "确认通过吗？", "cancel": "取消", "pass": "通过", "refuse": "不通过", "refuseTooltip": "确认不通过吗？"}, "auditNotification": {"title": "已被审核", "at": "于", "result": "审核", "currentPlan": "该方案"}}, "stimulus": {"template": {"label": "模板名称", "namePlaceholder": "请填写模板名称", "delMsg": "确认删除此刺激模板吗？", "searchPlaceholder": "搜索刺激模板名称", "empty": "当前没有刺激模板, 点击按钮创建", "duplicateStimulusTemplate": "刺激模板名称已存在", "createTemplate": "新建模板", "delTemplate": "删除模板", "seeMsg": "预览刺激模板", "modal": {"createTitle": "新建刺激模板", "editTitle": "编辑刺激模板", "importTitle": "导入刺激模板"}, "form": {"templateName": "请填写刺激模板名称", "exciteIntervalLimit": "数值范围需在1-600之间"}}}, "errorCode": {"SV50002": "duplicate Stimulus Template"}, "log": {"table": {"createdAt": "时间", "createdAtUser": "操作者", "eventType": "事件", "remark": "备注", "empty": "暂无日志"}, "event": {"CreatePlan": "任务创建", "CompleteDataProcessing": "图像处理完成", "StartTreament": "开始治疗", "StartStimulation": "开始刺激", "StopStimulation": "停止刺激", "StopTreament": "结束治疗", "TerminateTreament": "终止治疗"}}, "emg": {"leave": {"title": "运动阈值未保存，是否保存", "ok": "保存", "cancel": "取消", "content": "运动阈值不能为空", "emptyTitle": "测量运动阈值流程未完成，是否结束测量运动阈值？", "emptyOk": "继续测量", "emptyCancel": "结束测量"}, "motionThresholdRecord": {"content": "阈值MT(%)已更新"}}}, "pointCloud": {"createCorrect": "数据上传时间正序", "createReverse": "数据上传时间倒序", "doneCorrect": "方案完成时间正序", "doneReverse": "方案完成时间倒序", "statusCorrect": "状态变更时间正序", "statusReverse": "状态变更时间倒序", "waitApproval": "待审核", "doneApproval": "已完成", "all": "全部", "error": "异常", "schemeCreateAt": "数据上传时间", "schemeDoneAt": "处理完成时间", "schemeApprovalAt": "方案完成时间", "linkOrgCounts": "关联机构数", "create": "创建", "save": "保存", "reviewerManageOrg": "请选择审核员要管理的机构", "firstName": "姓", "lastName": "名", "joinReviewerManageOrg": "请选择参与管理此机构的审核员", "createUser": "创建用户", "editUser": "编辑用户", "accountInfo": "账号信息", "equipment": "第三方设备", "taskMonitor": "平台任务监控", "funcArea": "功能分区", "aparcAseg": "结构分区", "headRun": "头动曲线", "accessFirstAt": "首次请求时间", "accessLatestAt": "最近请求时间", "accessAt": "请求时间", "accessResult": "请求结果", "remark": "备注", "planProcessingCount": "处理中", "planComputeSuccessCount": "待审核", "planReviewedCount": "审核通过", "planReviewFailedCount": "处理异常", "planAbnormal": "异常", "planComputeFailedCount": "处理失败", "sequence": "序列", "failed": "失败", "success": "成功", "computed": "计算中", "scanName": "扫描名称", "exception": "异常信息", "scanException": "数据处理信息", "seqNumber": "序列号", "pCloud": "POINT Cloud"}, "template": {"TEMPLATEA": {"title": "抑郁症"}, "TEMPLATEB": {"title": "失语症"}, "TEMPLATEC": {"title": "运动障碍"}, "TEMPLATED": {"title": "自闭症"}, "TEMPLATEE": {"title": "常规"}, "TEMPLATEF": {"title": "帕金森"}, "TEMPLATEG": {"title": "脑瘫"}, "TEMPLATEH": {"title": "阿尔茨海默"}, "TEMPLATEI": {"title": "注意力缺陷多动障碍"}, "TEMPLATEJ": {"title": "运动障碍"}}, "semantic": {"correlation": "相关系数", "marker": "标记点"}}
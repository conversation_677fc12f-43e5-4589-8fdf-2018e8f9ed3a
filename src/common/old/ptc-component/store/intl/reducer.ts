import { Reducer } from 'redux';

import { defaultLocale } from '@common/old/ptc-component/lib/intl/locale';

// import { defaultLocale } from '../../lib/intl/defaults';
// import { getMessages } from '../../lib/intl/utils';
import { IntlActionTypes, IntlState, IntlActions } from './types';

// Type-safe initialState!
export const initialState: IntlState = {
  locale: defaultLocale,
  messages: {},
  // messages: getMessages(defaultLocale),
};

// Thanks to Redux 4's much simpler typings, we can take away a lot of typings on the reducer side,
// everything will remain type-safe.
const reducer: Reducer<IntlState, IntlActions> = (
  state: IntlState = initialState,
  action: IntlActions,
) => {
  switch (action.type) {
    case IntlActionTypes.UPDATE_LOCALE: {
      return { ...state, locale: action.payload.locale, messages: action.payload.messages };
    }
    default: {
      return state;
    }
  }
};

// Instead of using default export, we use named exports. That way we can group these exports
// inside the `index.js` folder.
export { reducer as intlReducer };
export { initialState as intlInitialState };

import { ActionCreator } from 'redux';

import {
  UpdateLocaleAction,
  IntlActionTypes,
} from './types';
import { getMessages } from '../../lib/intl/utils';
import { Locale } from '../../lib/intl/locale';

export const updateIntlLocale =  (
  locale: Locale
) => {
  return async function (dispatch) {
    console.log(locale);
    const res = await getMessages(locale);
    dispatch({
      type: IntlActionTypes.UPDATE_LOCALE,
      payload: {
        locale,
        messages: res,
      },
    })
  }
};

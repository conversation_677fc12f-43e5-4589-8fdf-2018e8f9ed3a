import { Action } from 'redux';
import { Locale } from '../../lib/intl/locale';

export enum IntlActionTypes {
  UPDATE_LOCALE = '@@intl/UPDATE_LOCALE',
}

// Declare state types with `readonly` modifier to get compile time immutability.
// https://github.com/piotrwitek/react-redux-typescript-guide#state-with-type-level-immutability
export type IntlState = {
  readonly locale: Locale;
  readonly messages: any;
};

export type UpdateLocaleAction = Action<IntlActionTypes.UPDATE_LOCALE> & {
  payload: {
    locale: Locale;
    messages: any;
  };
};

export type IntlActions = UpdateLocaleAction;

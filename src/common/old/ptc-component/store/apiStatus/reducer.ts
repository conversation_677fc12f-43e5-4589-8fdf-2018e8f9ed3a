import { Reducer } from 'redux';
import { ApiStatusActionTypes, ApiStatusState, ApiStatusActions } from './types';

// Type-safe initialState!
const initialState: ApiStatusState = {
  loading: false,
  error: undefined,
  status: undefined,
};

// Thanks to Redux 4's much simpler typings, we can take away a lot of typings on the reducer side,
// everything will remain type-safe.
const reducer: Reducer<ApiStatusState, ApiStatusActions> = (state: ApiStatusState = initialState, action: ApiStatusActions) => {
  switch (action.type) {
    case ApiStatusActionTypes.SET_API_LOADING: {
      return { ...state, loading: true };
    }
    case ApiStatusActionTypes.RESET_API_LOADING: {
      return { ...state, loading: false };
    }
    case ApiStatusActionTypes.SET_API_ERROR: {
      return { ...state, error: action.payload.error };
    }
    case ApiStatusActionTypes.SET_API_STATUS: {
      return { ...state, status: action.payload.status };
    }
    case ApiStatusActionTypes.RESET_API_ALL: {
      return { ...state, loading: false, status: undefined, error: undefined };
    }
    default: {
      return state;
    }
  }
};

// Instead of using default export, we use named exports. That way we can group these exports
// inside the `index.js` folder.
export { reducer as apiStatusReducer };
export { initialState as apiStatusInitialState };

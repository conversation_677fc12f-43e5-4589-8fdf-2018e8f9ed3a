import { ActionCreator } from 'redux';

import {
  SetApiLoadingAction,
  SetApiStatusAction,
  SetApiErrorAction,
  ResetApiLoadingAction,
  ApiStatusActionTypes,
  ResetApiAllAction,
} from './types';
import { ApiError } from '../../../../types/apiStatus';

// Type these action creators with `: ActionCreator<ActionTypeYouWantToPass>`.
// Remember, you can also pass parameters into an action creator. Make sure to
// type them properly.

export const setApiLoading: ActionCreator<SetApiLoadingAction> = () => ({
  type: ApiStatusActionTypes.SET_API_LOADING,
});

export const resetApiLoading: ActionCreator<ResetApiLoadingAction> = () => ({
  type: ApiStatusActionTypes.RESET_API_LOADING,
});

export const setApiStatus: ActionCreator<SetApiStatusAction> = (status: number) => ({
  type: ApiStatusActionTypes.SET_API_STATUS,
  payload: {
    status,
  },
});

export const setApiError: ActionCreator<SetApiErrorAction> = (error: ApiError) => ({
  type: ApiStatusActionTypes.SET_API_ERROR,
  payload: {
    error,
  },
});

export const resetApiAll: ActionCreator<ResetApiAllAction> = () => ({
  type: ApiStatusActionTypes.RESET_API_ALL,
});

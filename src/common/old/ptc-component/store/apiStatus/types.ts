// This file holds our state type, as well as any other types related to this Redux store.

import { Action } from 'redux';
import { ApiError } from '@common/types/apiStatus';

// Define however naming conventions you'd like for your action types, but
// personally, I use the `@@context/ACTION_TYPE` convention, to follow the convention
// of Redux's `@@INIT` action.
export enum ApiStatusActionTypes {
  SET_API_LOADING = '@@apiStatus/SET_API_LOADING',
  RESET_API_LOADING = '@@apiStatus/RESET_API_LOADING',
  SET_API_STATUS = '@@apiStatus/SET_API_STATUS',
  SET_API_ERROR = '@@apiStatus/SET_API_ERROR',
  RESET_API_ALL = '@@apiStatus/RESET_API_ALL',
}

// Declare state types with `readonly` modifier to get compile time immutability.
// https://github.com/piotrwitek/react-redux-typescript-guide#state-with-type-level-immutability
export type ApiStatusState = {
  readonly loading: boolean;
  readonly error?: ApiError;
  readonly status?: number;
};

export type SetApiStatusAction = Action<ApiStatusActionTypes.SET_API_STATUS> & {
  payload: {
    status: number;
  };
};

export type SetApiErrorAction = Action<ApiStatusActionTypes.SET_API_ERROR> & {
  payload: {
    error: ApiError;
  };
};
export type SetApiLoadingAction = Action<ApiStatusActionTypes.SET_API_LOADING>;

export type ResetApiLoadingAction = Action<ApiStatusActionTypes.RESET_API_LOADING>;

export type ResetApiAllAction = Action<ApiStatusActionTypes.RESET_API_ALL>;

export type ApiStatusActions = SetApiErrorAction | SetApiStatusAction | SetApiLoadingAction | ResetApiLoadingAction | ResetApiAllAction;

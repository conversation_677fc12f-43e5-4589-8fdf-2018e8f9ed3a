// This file holds our state type, as well as any other types related to this Redux store.

import { Action } from 'redux';
import { UserSession } from '../../../../types/auth';
import {
  SetApiLoadingAction,
  ResetApiLoadingAction,
  SetApiStatusAction,
  SetApiErrorAction,
  ResetApiAllAction,
} from '../apiStatus/types';
import { UpdateOrgAction } from '../pageData/types';
import { UserDTO } from '@ngiq/ngiq-api-types';

// Define however naming conventions you'd like for your action types, but
// personally, I use the `@@context/ACTION_TYPE` convention, to follow the convention
// of Redux's `@@INIT` action.
export const enum AuthActionTypes {
  AUTHENTICATION_SUCCESS = '@@auth/AUTHENTICATION_SUCCESS',
  LOGGING_OUT = '@@auth/LOGGING_OUT',
  SESSION_TIMEOUT = '@@auth/SESSION_TIMEOUT',
  RENE<PERSON><PERSON>HTOKEN_SUCCESS = '@@auth/RENEWAUTHTOKEN_SUCCESS',
  CACHE_SESSION_EXTENSION_SETTING = '@@auth/CACHE_SESSION_EXTENSION_SETTING',
  GET_SESSION_FROM_STORAGE = '@@auth/GET_SESSION_FROM_STORAGE',
  UPDATE_PROFILE = '@@auth/UPDATE_PROFILE',
}

// Declare state types with `readonly` modifier to get compile time immutability.
// https://github.com/piotrwitek/react-redux-typescript-guide#state-with-type-level-immutability
// Declare state types with `readonly` modifier to get compile time immutability.
// https://github.com/piotrwitek/react-redux-typescript-guide#state-with-type-level-immutability
export type AuthState = {
  readonly userSession?: UserSession;
  readonly sessionExtensionSetting: number;
};

export type LogoutAction = Action<AuthActionTypes.LOGGING_OUT>;
export type LogoutActions = LogoutAction | ResetApiAllAction;
export type AuthenticationSuccessAction = Action<AuthActionTypes.AUTHENTICATION_SUCCESS> & {
  payload: {
    userSession: UserSession;
  };
};

export type UpdateProfileAction = Action<AuthActionTypes.UPDATE_PROFILE> & {
  payload: {
    user: UserDTO;
  };
};

export type AuthenticateActions =
  AuthenticationSuccessAction |
  SetApiLoadingAction |
  SetApiStatusAction |
  ResetApiAllAction |
  ResetApiLoadingAction |
  SetApiErrorAction |
  UpdateOrgAction;

export type AuthActions =
  AuthenticationSuccessAction |
  LogoutAction |
  UpdateProfileAction;

import { Action } from 'redux';

import { AutoUpdateStatus } from '@common/types/messageTypes';

export enum AppStatusActionTypes {
  UPDATE_APP_STATUS = '@@appStatus/UPDATE_APP_STATUS',
  UPDATE_APP_PERCENT = '@@appStatus/UPDATE_APP_PERCENT',
  UPDATE_APP_INFO = '@@appStatus/UPDATE_APP_INFO',
}

export type AppStatusState = {
  status: AutoUpdateStatus;
  isForceUpgrade?: boolean;
  newVersion?: string;
  percent?: number;
  checkUpdateTypes?: CheckUpdateTypes;
};

/**触发检查更新的手段 */
export enum CheckUpdateTypes {
  /**打开app时自动检查更新 */
  OPEN_APP = 'OPEN_APP',
  /**关于我们页面手动检查更新 */
  MANUAL_CHECK = 'MANUAL_CHECK',
  /**轮询检查更新 */
  POLL = 'POLL',
}

export type UpdateAppStatustAction = Action<AppStatusActionTypes.UPDATE_APP_STATUS> & {
  payload: {
    status: AutoUpdateStatus;
  };
};

export type UpdateAppPercentAction = Action<AppStatusActionTypes.UPDATE_APP_PERCENT> & {
  payload: {
    percent: number;
  };
};

export type UpdateAppUpdateInfoAction = Action<AppStatusActionTypes.UPDATE_APP_INFO> & {
  payload: Partial<AppStatusState>;
};

export type AppStatusActions = UpdateAppStatustAction | UpdateAppPercentAction;

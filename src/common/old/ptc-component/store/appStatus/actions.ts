import { ActionCreator } from 'redux';
import {
  AppStatusActionTypes,
  UpdateAppStatustAction,
  UpdateAppPercentAction,
  UpdateAppUpdateInfoAction,
  AppStatusState,
} from './types';
import { AutoUpdateStatus } from '@common/types/messageTypes';

export const updateAppStatus: ActionCreator<UpdateAppStatustAction> = (
  status: AutoUpdateStatus
) => {
  return {
    type: AppStatusActionTypes.UPDATE_APP_STATUS,
    payload: {
      status,
    },
  };
};

export const updateAppPrecent: ActionCreator<UpdateAppPercentAction> = (
  percent: number
) => {
  return {
    type: AppStatusActionTypes.UPDATE_APP_PERCENT,
    payload: {
      percent,
    },
  };
};

export const updateAppUpdateInfo: ActionCreator<UpdateAppUpdateInfoAction> = (
  appUpdateInfo: Partial<AppStatusState>
) => {
  return {
    type: AppStatusActionTypes.UPDATE_APP_INFO,
    payload: appUpdateInfo,
  };
};

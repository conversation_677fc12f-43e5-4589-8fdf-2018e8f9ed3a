import { Reducer } from 'redux';

import { AutoUpdateStatus } from '@common/types/messageTypes';

import { AppStatusActionTypes, AppStatusActions, AppStatusState } from './types';

export const initialState: AppStatusState = {
  status: AutoUpdateStatus.CheckingForUpdate,
  percent: 0,
};

// Thanks to Redux 4's much simpler typings, we can take away a lot of typings on the reducer side,
// everything will remain type-safe.
const reducer: Reducer<AppStatusState, AppStatusActions> = (
  state: AppStatusState = initialState,
  action: AppStatusActions,
) => {
  switch (action.type) {
    case AppStatusActionTypes.UPDATE_APP_STATUS: {
      return { ...state, status: action.payload.status };
    }
    case AppStatusActionTypes.UPDATE_APP_PERCENT: {
      return { ...state, percent: action.payload.percent };
    }
    case AppStatusActionTypes.UPDATE_APP_INFO: {
      return { ...state, ...action.payload };
    }
    default: {
      return state;
    }
  }
};

export { reducer as appStatusReducer };
export { initialState as appStatusInitialState };

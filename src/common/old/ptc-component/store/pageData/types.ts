import { Action } from 'redux';
import { SubjectFullDTO, SubjectDTO, ProjectDTO, OrgDTO, TaskDesign, BoldRunInfo, ScanGroupDTO, JobFullDTO, AllFileDescriptor, SeedInputType } from '@ngiq/ngiq-api-types';
import { VizGroup } from '../../ui-component/visualization/vizDef';
// import { DataSourceType } from '../../containers/subject/subjectContainer';
import { VizMap } from '../../ui-component/visualization/vizUtils';
import { ArtiDefSurfBase, Arti, ArtiDefSurfOverlay } from '../../ui-component/visualization/artiDef';
import { PointerType } from '../../ui-component/visualizers/control/surface/point/point';

export enum PageDataActionTypes {
  ADD_SUBJECT_LIST = '@data/ADD_SUBJECT_LIST',
  ADD_SUBJECT = '@data/ADD_SUBJECT',
  UPDATE_SUBJECT_LIST = '@data/UPDATE_SUBJECT_LIST',
  ADD_PROJECT_LIST = '@data/ADD_PROJECT_LIST',
  UPDATE_PROJECT_LIST = '@data/UPDATE_PROJECT_LIST',
  UPDATE_ORG_INFO = '@data/UPDATE_ORG_INFO',
  MARK_PROJECT_ID = '@data/MARK_PROJECT_ID',
  MARK_SUBJECT_ID = '@data/MARK_SUBJECT_ID',
  DELETE_SUBJECT = '@data/DELETE_SUBJECT',
  MARCK_TASKS = '@data/MARCK_TASKS',
  UPDATE_RUNINFO = '@data/UPDATE_RUNINFO',
  UPLOAD_SUBJECT = '@data/UPDATE_SUBJECT',
  UPDATE_HASHCHANGE_TIME = '@data/UPDATE_HASHCHANGE_TIME',
  SET_ORGS = '@data/SET_ORGS',
  UPDATE_SPREAD_STATUS = '@data/UPDATE_SPREAD_STATUS',
  SET_SCAN_GROUPS = '@data/SET_SCAN_GROUPS',
  SET_MARK_PROJECT_JOBS = '@data/SET_MARK_PROJECT_JOBS',
  SET_SUBJECT_FULLDTO_DATA = '@data/SET_SUBJECT_FULLDTO_DATA',
  UPDATE_ONLINE_STATUS = '@data/@UPDATE_ONLINE_STATUS',
  UPDATE_FILE_FILTER = '@data/UPDATE_FILE_FILTER',
  UPLOAD_SAVE_CSVS = '@data/UPLOAD_SAVE_CSVS',
  SINGLE_UPLOAD_SAVE_SUBJECT = '@data/SINGLE_UPLOAD_SAVE_SUBJECT',
  UPLOAD_SAVE_SUBJECTS = '@data/UPLOAD_SAVE_SUBJECTS',
  UPDATE_STATUS_APP_UPLOAD = '@data/UPDATE_STATUS_APP_UPLOAD',
  UPDATE_STATUS_APP_DOWNLOAD = '@data/UPDATE_STATUS_APP_DOWNLOAD',
  UPDATE_STATUS_OF_FETCH = '@data/UPDATE_STATUS_OF_FETCH',
  UPDATE_AUTH = '@data/UPDATE_AUTH',
  UPDATE_SURG_MAIN = '@data/UPDATE_SURG_MAIN',
  UPDATE_SURG_HOME = '@data/UPDATE_SURG_HOME',
  UPDATE_NG_LEFT = '@data/UPDATE_NG_LEFT',
  SET_VIZ_MEUN_TABS_ACTION = '@data/SET_VIZ_MEUN_TABS_ACTION',
  SET_SSHOW_SCAN_VISUALLIZATION_NAV_ACTION = '@data/SET_SSHOW_SCAN_VISUALLIZATION_NAV_ACTION',
  ShowConnectivityTabsAction = '@data/SHOW_CONNECTIVITY_TABS_ACTION',
  HEADER_COLLAPSE_CHANGE = '@data/HEADER_COLLAPSE_CHANGE',
  RE_GET_PRO_JECT_LIST = '@DATA/RE_GET_PRO_JECT_LIST',
  UPLOAD_FILES_STATUS = '@data/UPLOAD_FILES_STATUS',
}

export type SubejctContainerStoreType = {
  subjectFullInfo?: SubjectFullDTO;
  jobVizGroup: Map<number, VizGroup[]>;
  tasks: TaskDesign[];
  checkJobInfo?: any | undefined;
  isTask: boolean;
  jobVizMap: Map<number, VizMap>;
  uploadVisible?: boolean;
};

export type ConnectivityVizDataType = {
  isShowConnectivityTab?: boolean;
  openConnectivityTab?: boolean;
  subjectId?: number;
  isFull?: boolean;
  controlTitle?: string;
  jobId?: number;
  isAnatomical?: boolean;
  lh_presigned?: string; // eslint-disable-line camelcase
  rh_presigned?: string; // eslint-disable-line camelcase
  lh_presigned_fs4_native?: string; // eslint-disable-line camelcase
  rh_presigned_fs4_native?: string; // eslint-disable-line camelcase
  presigned_fs4_native_json?: string; // eslint-disable-line camelcase
  lh_presigned_fs4?: string; // eslint-disable-line camelcase
  rh_presigned_fs4?: string; // eslint-disable-line camelcase
  seedIndex?: string;
  selectedBase?: Arti<ArtiDefSurfBase>;
  selectOverlay?: Arti<ArtiDefSurfOverlay>;
  pointerParams?: PointerType;
  dynamicColorMap?: string;
  selectedSeedIndex?: number;
  resolution?: number;
  regionTemplate?: string | number;
  parcelName?: string;
  region?: string;
  seedInputType?: SeedInputType;
  createSeed?: {
    radius: string;
    id: number;
    name: string;
    description?: string;
  };
  connectivityTab?: string;
};

export type ScanVisualizationNavType = {
  isShowScanVisualizationNav: boolean;
  url?: string;
  analysisName?: string;
};

export type PageDataState = {
  readonly subjectList: SubjectDTO[];
  readonly projectList: ProjectDTO[];
  readonly org: OrgDTO;
  readonly projectId: number;
  readonly subjectId: number;
  readonly tasks: TaskDesign[];
  readonly runInfo: BoldRunInfo[];
  readonly uploadSubject?: SubjectDTO;
  readonly hashchangeTime: number;
  readonly orgs: OrgDTO[];
  readonly spreadStatus: boolean;
  // readonly scanGroups?: ScanGroupDTO[];
  readonly markGroupJobs: JobFullDTO[];
  // readonly onlineStatus: boolean;
  readonly subjectContainer: SubejctContainerStoreType;
  readonly fileFilter: AllFileDescriptor[];
  // readonly uploadCsvs?: ValidateBatchUploadSubject[];
  readonly uploadSubjects?: SubjectDTO[];
  readonly statusAppUpload?: boolean;
  readonly statusAppDownload?: boolean;
  readonly statusOfFecth: boolean;
  readonly updateAuth?: boolean;
  readonly updateSurgMain?: boolean;
  readonly updateSurgHome?: boolean;
  readonly updateNGLeft?: boolean;
  // readonly singleUploadSubject?: ValidateBatchUploadSubject;
  readonly connectivityVizData?: ConnectivityVizDataType;
  readonly vizMeunTabs?: string;
  readonly goScanVisualizationNav?: ScanVisualizationNavType;
  readonly isShowCollapse?: boolean;
  readonly isUploading?: boolean;
};

export type AddSubjectListAction = Action<PageDataActionTypes.ADD_SUBJECT_LIST> & {
  payload: {
    subjectList: SubjectDTO[];
  };
};

export type UpdateSubjectListAction = Action<PageDataActionTypes.UPDATE_SUBJECT_LIST> & {
  payload: {
    subject: SubjectDTO;
  };
};

export type AddProjectListAction = Action<PageDataActionTypes.ADD_PROJECT_LIST> & {
  payload: {
    projectList: ProjectDTO[];
  };
};

export type UpdateProjectListAction = Action<PageDataActionTypes.UPDATE_PROJECT_LIST> & {
  payload: {
    project: ProjectDTO;
  };
};

export type UpdateOrgAction = Action<PageDataActionTypes.UPDATE_ORG_INFO> & {
  payload: {
    org: OrgDTO;
  };
};

export type DeleteSubjectAction = Action<PageDataActionTypes.DELETE_SUBJECT> & {
  payload: {
    subjectId: number;
  };
};

export type AddSubjectAction = Action<PageDataActionTypes.ADD_SUBJECT> & {
  payload: {
    subject: SubjectDTO;
  };
};

export type MarkProjectIdAction = Action<PageDataActionTypes.MARK_PROJECT_ID> & {
  payload: {
    projectId: number;
  };
};

export type MarkSubjectIdAction = Action<PageDataActionTypes.MARK_SUBJECT_ID> & {
  payload: {
    subjectId: number;
  };
};

export type MarkTasksAction = Action<PageDataActionTypes.MARCK_TASKS> & {
  payload: {
    tasks: TaskDesign[];
  };
};

export type UpdateRunInfoAction = Action<PageDataActionTypes.UPDATE_RUNINFO> & {
  payload: {
    runInfo: BoldRunInfo[];
  };
};

export type UploadSubjectAction = Action<PageDataActionTypes.UPLOAD_SUBJECT> & {
  payload: {
    uploadSubject: SubjectDTO;
  };
};

export type HashChangeTimeAction = Action<PageDataActionTypes.UPDATE_HASHCHANGE_TIME> & {
  payload: {
    hashchangeTime: number;
  };
};

export type SetOrgsAction = Action<PageDataActionTypes.SET_ORGS> & {
  payload: {
    orgs: OrgDTO[];
  };
};

export type UpdateSpreadStatusAction = Action<PageDataActionTypes.UPDATE_SPREAD_STATUS> & {
  payload: {
    spreadStatus: boolean;
  };
};

// export type SetScanGroupAction = Action<PageDataActionTypes.SET_SCAN_GROUPS> & {
//   payload: {
//     scanGroups: ScanGroupDTO[] | undefined;
//   };
// };

export type SetMarkProjectJobsAction = Action<PageDataActionTypes.SET_MARK_PROJECT_JOBS> & {
  payload: {
    jobs: JobFullDTO[];
  };
};

export type SetSubjectFullDtoAction = Action<PageDataActionTypes.SET_SUBJECT_FULLDTO_DATA> & {
  payload: {
    subjectContainer: SubejctContainerStoreType;
  };
};

export type UpdateFileFilterAction = Action<PageDataActionTypes.UPDATE_FILE_FILTER> & {
  payload: {
    fileFilter: AllFileDescriptor[];
  };
};

// export type BatchUploadSaveCsvsAction = Action<PageDataActionTypes.UPLOAD_SAVE_CSVS> & {
//   payload: {
//     subjects: ValidateBatchUploadSubject[];
//   };
// };

// export type SingleUploadSaveSubjectAction = Action<PageDataActionTypes.SINGLE_UPLOAD_SAVE_SUBJECT> & {
//   payload: {
//     subject: ValidateBatchUploadSubject;
//   };
// };

export type UploadSaveSubjectsAction = Action<PageDataActionTypes.UPLOAD_SAVE_SUBJECTS> & {
  payload: {
    uploadSubjects: SubjectDTO[];
  };
};

export type UpdateStatusAppUploadAction = Action<PageDataActionTypes.UPDATE_STATUS_APP_UPLOAD> & {
  payload: {
    statusAppUpload: boolean;
  };
};

export type UpdateStatusAppDownloadAction = Action<PageDataActionTypes.UPDATE_STATUS_APP_DOWNLOAD> & {
  payload: {
    statusAppDownload: boolean;
  };
};

export type ShowConnectivityTabsAction = Action<PageDataActionTypes.ShowConnectivityTabsAction> & {
  payload: {
    connectivityVizData: ConnectivityVizDataType;
  };
};

export type UpdateFecthStatusAction = Action<PageDataActionTypes.UPDATE_STATUS_OF_FETCH> & {
  payload: {
    statusOfFecth: boolean;
  };
};

export type UpdateAuthInfoAction = Action<PageDataActionTypes.UPDATE_AUTH> & {
  payload: {
    updateAuth: boolean;
  };
};

export type UpdateSurgMainAction = Action<PageDataActionTypes.UPDATE_SURG_MAIN> & {
  payload: {
    updateSurgMain: boolean;
  };
};

export type UpdateSurgHomeAction = Action<PageDataActionTypes.UPDATE_SURG_HOME> & {
  payload: {
    updateSurgHome: boolean;
  };
};

export type UpdateNGLeftAction = Action<PageDataActionTypes.UPDATE_NG_LEFT> & {
  payload: {
    updateNGLeft: boolean;
  };
};

export type SetVizMeunTabsAction = Action<PageDataActionTypes.SET_VIZ_MEUN_TABS_ACTION> & {
  payload: {
    vizMeunTabs: string;
  };
};

export type SetShowScanVisualizationNavAction = Action<PageDataActionTypes.SET_SSHOW_SCAN_VISUALLIZATION_NAV_ACTION> & {
  payload: {
    isShowScanVisualizationNav: boolean;
    url?: string;
    analysisName?: string;
  };
};

export type HeaderCollapseChange = Action<PageDataActionTypes.HEADER_COLLAPSE_CHANGE> & {
  payload: {
    isShowCollapse: boolean;
  };
};

export type ReGetProjectList = Action<PageDataActionTypes.RE_GET_PRO_JECT_LIST> & {
  payload: {
    projectList: ProjectDTO[];
  };
};

export type UploadFileStatus = Action<PageDataActionTypes.UPLOAD_FILES_STATUS> & {
  payload: {
    isUploading: boolean;
  };
};

export type PageDataActions =
  AddSubjectListAction
  | UpdateSubjectListAction
  | AddProjectListAction
  | UpdateProjectListAction
  | UpdateOrgAction
  | DeleteSubjectAction
  | MarkProjectIdAction
  | MarkSubjectIdAction
  | AddSubjectAction
  | MarkTasksAction
  | UpdateRunInfoAction
  | UploadSubjectAction
  | HashChangeTimeAction
  | SetOrgsAction
  | UpdateSpreadStatusAction
  | SetScanGroupAction
  | SetMarkProjectJobsAction
  | SetSubjectFullDtoAction
  | UpdateFileFilterAction
  // | BatchUploadSaveCsvsAction
  | UploadSaveSubjectsAction
  | UpdateStatusAppUploadAction
  | UpdateStatusAppDownloadAction
  // | SingleUploadSaveSubjectAction
  | ShowConnectivityTabsAction
  | UpdateFecthStatusAction
  | UpdateAuthInfoAction
  | UpdateSurgMainAction
  | UpdateSurgHomeAction
  | UpdateNGLeftAction
  | SetVizMeunTabsAction
  | SetShowScanVisualizationNavAction
  | HeaderCollapseChange
  | ReGetProjectList
  | UploadFileStatus;

import { PageModelOfPntOrgModel } from '@common/types/index.defs';
import { IHttpClient } from '../../../api/httpClient/IHttpClient';
import {
  CreateSubjectDTO,
  SubjectDTO,
  UpdateSubjectDTO,
  SubjectFullDTO,
  CreateSubjectSnapshotDTO,
  SubjectSnapshotDTO,
  CreateSubjectProbeDTO,
  SimpleSubjectDTO,
  DeleteSubjectProbeResultDTO,
  SubjectAttachmentParam,
  SubjectAttachmentResult,
} from '@ngiq/ngiq-api-types';

type GetSubjectDList = {
  keyword?: string,
  page: number,
  pageSize: number,
  sortTypeEnum?: number,
  orgIds?: string,
};

type CreateSubjectInfo = {
  orgId: number,
  fullName: string,
  patientCode: string,
  gender: number,
  dateOfBirth: number,
  mobile?: string,
  comments?: string,
  subjectFileIdList?: number[],
}

type updateSubjectInfo = {
  gender: number,
  dateOfBirth: number,
  mobile?: string,
  comments?: string,
}

type updatePatient = {
  subjectId: number,
  subjectInfo: updateSubjectInfo
}

type pageType = {
  page: number,
  pageSize: number
}

type GetSubjectTreatmentType = {
  page: pageType,
  subjectId: number,
  planGroupIdList?: [],
}

type GetTaskMonitorType = {
  orgIds?: number[],
  planStatus?: number[],
  page?: number,
  pageSize?: number,
  sortTypeEnum?: number,
}

export interface SubjectInfo {
  /**
   * 业务归属 box:1 or null ;cloud: 2
   */
  businessBelong?: number;
  /**
   * 备注
   */
  comments?: string;
  /**
   * 创建时间，毫秒时间戳
   */
  createdAt: number;
  /**
   * 创建者 ID
   */
  createdByUserId: number;
  /**
   * 生日，毫秒时间戳
   */
  dateOfBirth: number;
  /**
   * 姓名
   */
  fullName: string;
  /**
   * 性别
   */
  gender: number;
  /**
   * id
   */
  id: number;
  /**
   * 电话号码
   */
  mobile?: string;
  /**
   * orgId
   */
  orgId: number;
  /**
   * 患者编号
   */
  patientCode: string;
  /**
   * 姓名拼音
   */
  pinyinFullName: string;
  /**
   * 更新时间
   */
  updatedAt: number;

  /**
   * 患者下治疗任务总数
   */
  planCount?: number;

  /**
   * 患者目标治疗次数
   */
  expectedTreatmentCount?: number;

  subjectFileList:SubjectAttachmentResult[];
}


export class SubjectApi {
  private httpClient: IHttpClient;

  constructor(httpClient: IHttpClient, authToken: string) {
    httpClient.setAuthToken(authToken);
    this.httpClient = httpClient;
  }

  public async createSubject(subjectDto: CreateSubjectDTO): Promise<SubjectDTO> {
    return this.httpClient.restPost<CreateSubjectDTO, SubjectDTO>('/subjects', subjectDto);
  }

  //获取患者列表
  public async getSubjectList(params: GetSubjectDList): Promise<PageModelOfPntOrgModel> {
    return this.httpClient.restGet<GetSubjectDList, PageModelOfPntOrgModel>(`/v1/subjects`, params);
  }

   //创建患者
  public async createSubjectV1(subjectInfo: CreateSubjectInfo): Promise<SubjectInfo> {
    return this.httpClient.restPost<CreateSubjectInfo, SubjectInfo>(`/v1/subjects`, subjectInfo);
  }

   //更新患者
   public async updateSubjectV1(params: updatePatient): Promise<SubjectInfo> {
    return this.httpClient.restPut<updatePatient, SubjectInfo>(`/v1/subjects/${params.subjectId}`, params.subjectInfo);
  }

   //获取患者详情
   public async getSubjectInfoV1(subjectId: number): Promise<SubjectInfo> {
    return this.httpClient.restGet<updatePatient, SubjectInfo>(`/v1/subjects/${subjectId}`);
  }

  //获取患者任务列表
  public async getSubjectTreatmentTasks(subject: GetSubjectTreatmentType): Promise<PageModelOfPntOrgModel> {
    return this.httpClient.restGet<GetSubjectTreatmentType, PageModelOfPntOrgModel>(`/v1/subjects/${subject.subjectId}/plans`, {...subject.page, planGroupIdList: subject.planGroupIdList});
  }

  //获取病种模板
  public async getDisableTemplate(): Promise<any> {
    return this.httpClient.restGet<{}, any>(`/pipeline/templates`);
  }

  //平台任务监控
  public async getTaskMonitorInfo(monitorInfo:GetTaskMonitorType): Promise<any> {
    return this.httpClient.restGet<GetTaskMonitorType, any>('/v1/admin/plans', monitorInfo);
  }

  // 优点解决方案导出
  public async getPlanNgFiles(planId: number, password: string): Promise<any> {
    return this.httpClient.restPost<{planId: number}, any>(`/v1/plans/${planId}/ngfiles`,{ password });
  }

  public async updateSubject(subjectDto: UpdateSubjectDTO): Promise<SubjectDTO> {
    return this.httpClient.restPut<UpdateSubjectDTO, SubjectDTO>(`/subjects/${subjectDto.id}`, subjectDto);
  }

  public async getSubject(subjectId: number, projectId?: number): Promise<SubjectFullDTO> {
    const params = projectId ? { projectId } : {};

    return this.httpClient.restGetOne<{}, SubjectFullDTO>(`/subjects/${subjectId}`, params);
  }

  public async getAllSubjects(orgId: number): Promise<SubjectDTO[]> {
    return this.httpClient.restGet<{}, SubjectDTO>(`/subjects?orgId=${orgId}`, {});
  }

  public async detectDeleteSubject(subjectId: number, projectId: number): Promise<DeleteSubjectProbeResultDTO> {
    return this.httpClient.restGetOne<{}, DeleteSubjectProbeResultDTO>(`/subjects/${subjectId}/${projectId}/deletesubjectprobe/`, {});
  }

  public async deleteSubject(subjectId: number, projectId: number): Promise<{}> {
    return this.httpClient.restDelete<{}>(`/subjects/${subjectId}/${projectId}`);
  }

  public async createSubjectSnapshot(subjectSnapshot: CreateSubjectSnapshotDTO): Promise<SubjectSnapshotDTO> {
    return this.httpClient.restPost<CreateSubjectSnapshotDTO, SubjectSnapshotDTO>(`/subjects/${subjectSnapshot.subjectId}/snapshots`, subjectSnapshot);
  }

  public async getBaseSubject(subjectId: number): Promise<SubjectDTO> {
    return this.httpClient.restGetOne<{}, SubjectDTO>(`/subjects/${subjectId}`, {});
  }

  public async createSubjectProbe(createSubjectProbe: CreateSubjectProbeDTO): Promise<SimpleSubjectDTO[]> {
    return this.httpClient.restPost<CreateSubjectProbeDTO, SimpleSubjectDTO[]>('/subjects/createsubjectprobe', createSubjectProbe);
  }

  public async createSubjectAttachment(param: SubjectAttachmentParam): Promise<SubjectAttachmentResult> {
    return this.httpClient.restPost<SubjectAttachmentParam,SubjectAttachmentResult>('/subjects/subjectfile', param);
  }
}

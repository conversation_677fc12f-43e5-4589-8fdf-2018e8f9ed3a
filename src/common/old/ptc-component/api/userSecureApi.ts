import { IHttpClient } from '../../../api/httpClient/IHttpClient';
import {
  UserDTO,
  UsersExistDTO,
  EmailsExistRequestDTO,
  CreateUserDTO,
  UpdateUserDTO,
  EmailAvailabeRequestDTO,
  EmailAvailableDTO,
  StatusDTO,
  UpdateProfileDTO,
  OrgIdQueryDTO,
  CreateUserProbeDTO,
  CreateUserProbeResultDTO,
  ManageNGRAOfOrgDTO,
  ManageNGRAOfOrgResultDTO,
  ResetPasswordDTO,
  ChangePasswordDTO,
  CreateOperationLogDTO,
} from '@ngiq/ngiq-api-types';

export class UserSecureApi {
  private httpClient: IHttpClient;

  constructor(httpClient: IHttpClient, authToken: string) {
    httpClient.setAuthToken(authToken);
    this.httpClient = httpClient;
  }

  public async createUser(userDto: CreateUserDTO): Promise<UserDTO> {
    return this.httpClient.restPost<CreateUserDTO, UserDTO>('/users', userDto);
  }

  public async checkUsersExist(emails: EmailsExistRequestDTO): Promise<UsersExistDTO> {
    return this.httpClient.restPut<EmailsExistRequestDTO, UsersExistDTO>('/users/check/emails', emails);
  }

  public async getAllUsers(param: OrgIdQueryDTO): Promise<UserDTO[]> {
    return this.httpClient.restGet<OrgIdQueryDTO, UserDTO>('/users', param);
  }

  public async getUser(userId: number): Promise<UserDTO> {
    return this.httpClient.restGetOne<{}, UserDTO>(`/users/${userId}`, {});
  }

  public async updateUser(updateUserDto: UpdateUserDTO): Promise<UserDTO> {
    return this.httpClient.restPut<UpdateUserDTO, UserDTO>(`/users/${updateUserDto.id}`, updateUserDto);
  }

  public async checkEmailAvailable(emailAvailableRequest: EmailAvailabeRequestDTO): Promise<EmailAvailableDTO> {
    return this.httpClient.restPut<EmailAvailabeRequestDTO, EmailAvailableDTO>('/users/check/emailavailable', emailAvailableRequest);
  }

  public async changeUserStatus(userId: number, status: boolean): Promise<UserDTO> {
    return this.httpClient.restPut<StatusDTO, UserDTO>(`/users/${userId}/changestatus`, { status });
  }

  public async genPwdResetToken(userId: number): Promise<UserDTO> {
    return this.httpClient.restPost<{}, UserDTO>(`/users/${userId}/sendpwdtoken`, {});
  }

  public async updateProfile(data: UpdateProfileDTO): Promise<UserDTO> {
    return this.httpClient.restPut<UpdateProfileDTO, UserDTO>(`/users/profile/${data.id}`, data);
  }

  public async createuserprobes(data: CreateUserProbeDTO): Promise<CreateUserProbeResultDTO[]> {
    return this.httpClient.restPost<{}, CreateUserProbeResultDTO[]>('/users/createuserprobe', data);
  }

  public async raUserAssociationOrg(id: number, data: ManageNGRAOfOrgDTO): Promise<ManageNGRAOfOrgResultDTO> {
    return this.httpClient.restPut<ManageNGRAOfOrgDTO, ManageNGRAOfOrgResultDTO>(`/orgs/${id}/ra`, data);
  }

  public async getRaUsersOfOrg(id: number): Promise<UserDTO[]> {
    return this.httpClient.restGet(`/orgs/${id}/ra`, undefined);
  }

  public async getAllRa(): Promise<UserDTO[]> {
    return this.httpClient.restGet('/users/all/ra', undefined);
  }

  public async getAllNG(): Promise<UserDTO[]> {
    return this.httpClient.restGet('/users/all/ngadmin', undefined);
  }

  public async changePassword(userId: number, data: ChangePasswordDTO): Promise<UserDTO> {
    return this.httpClient.restPut<ChangePasswordDTO, UserDTO>(`/users/password/${userId}`, data);
  }

  public async resetPassword(userId: number, data: ResetPasswordDTO): Promise<UserDTO> {
    return this.httpClient.restPut<ResetPasswordDTO, UserDTO>(`/users/reset/${userId}`, data);
  }

  public async checkUserReport(data: CreateOperationLogDTO): Promise<any> {
    return this.httpClient.restPost<CreateOperationLogDTO, UserDTO>('/operationlog', data);
  }
}

import { IHttpClient } from '../../../api/httpClient/IHttpClient';

import {
  OrgDTO,
  CreateOrgDTO,
  StatusDTO,
  UpdateOrgDTO,
  ManageOrgOfNGRADTO,
  JobFullDTO,
  OrgOverviewDTO,
  OrgProjectDTO,
  OrgLicenseDTO,
  CreateJobProbeResultDTO,
  CreateJobProbeDTO,
  OrgStatisticsRequireDTO,
  OrgStatisticsDTO,
  QueryOrgProjectsDTO,
  DeviceDTO,
  DeviceItemsDTO,
  CreateOrgLicenseDTOV2,
  UpdateOrgLicenseDTOV2,
  QueryApplicationDTO,
  DicomDTO,
  ApplyDicomsDTO,
  ApplyedDicomsRes,
  DeleteDicomsDTO,
  DeleteDicomsRes,
  HasPacsDeviceDTO,
  OrgIPRulesDTO,
  EndpointType,
  GetIPRulesResponseDTO,
  IPRuleResponseDTO,
  CreateIPRuleDTO,
  UpdateIPRuleDTO,
  OrgPointStatisticsDTO,
} from '@ngiq/ngiq-api-types';

export class OrgApi {
  private httpClient: IHttpClient;

  constructor(httpClient: IHttpClient, authToken: string) {
    httpClient.setAuthToken(authToken);
    this.httpClient = httpClient;
  }

  public async createOrg(orgDto: CreateOrgDTO): Promise<OrgDTO> {
    return this.httpClient.restPost<CreateOrgDTO, OrgDTO>('/orgs', orgDto);
  }

  public async updateOrg(orgDto: UpdateOrgDTO): Promise<OrgDTO> {
    return this.httpClient.restPut<UpdateOrgDTO, OrgDTO>(`/orgs/${orgDto.id}`, orgDto);
  }

  public async getOrg(orgId: number): Promise<OrgDTO> {
    return this.httpClient.restGetOne<{}, OrgDTO>(`/orgs/${orgId}`, {});
  }

  public async deleteOrg(orgId: number): Promise<{}> {
    return this.httpClient.restDelete<{}>(`/orgs/${orgId}`);
  }

  public async getAllOrgs(): Promise<OrgDTO[]> {
    return this.httpClient.restGet<{}, OrgDTO>('/orgs', {});
  }

  public async changeOrgStatus(orgId: number, status: boolean): Promise<OrgDTO> {
    return this.httpClient.restPut<StatusDTO, OrgDTO>(`/orgs/${orgId}/changestatus`, { status });
  }

  public async getOrgListOfRa(userId: number): Promise<OrgDTO[]> {
    return this.httpClient.restGet<{}, OrgDTO>(`users/${userId}/orgs`, {});
  }

  public async editOrgOfRaUsers(userId: number, editOrgIdsDto: ManageOrgOfNGRADTO): Promise<{}> {
    return this.httpClient.restPut(`users/${userId}/orgs`, editOrgIdsDto);
  }

  public async getOrgJobs(orgId: number): Promise<JobFullDTO[]> {
    return this.httpClient.restGet<{}, JobFullDTO>(`orgs/${orgId}/jobs`, {});
  }

  public async getOrgOverview(orgId: number, appType: QueryApplicationDTO): Promise<OrgOverviewDTO> {
    return this.httpClient.restGetOne<{}, OrgOverviewDTO>(`orgs/${orgId}/overview`, appType);
  }
  public async getApplicationOverview(orgId: number): Promise<OrgOverviewDTO> {
    return this.httpClient.restGetOne<{}, OrgOverviewDTO>(`orgs/${orgId}/application`, {});
  }

  public async getOrgProjects(orgId: number, query: QueryOrgProjectsDTO): Promise<OrgProjectDTO[]> {
    return this.httpClient.restGet<{}, OrgProjectDTO>(`orgs/${orgId}/projects`, query);
  }

  public async updateOrgLicense(orgId: number, licenseId: number, license: UpdateOrgLicenseDTOV2): Promise<OrgLicenseDTO> {
    return this.httpClient.restPost<{}, OrgLicenseDTO>(`orgs/${orgId}/licenses/${licenseId}`, license);
  }

  public async createOrgLicense(orgId: number, license: CreateOrgLicenseDTOV2): Promise<OrgLicenseDTO> {
    return this.httpClient.restPut<{}, OrgLicenseDTO>(`orgs/${orgId}/licenses`, license);
  }

  public async deleteOrgLicense(orgId: number, licenseId: number): Promise<{}> {
    return this.httpClient.restDelete<{}>(`orgs/${orgId}/licenses/${licenseId}`);
  }

  public async createJobProbe(orgId: number, jobProbe: CreateJobProbeDTO): Promise<CreateJobProbeResultDTO> {
    return this.httpClient.restPost<{}, CreateJobProbeResultDTO>(`orgs/${orgId}/createjobprobe`, jobProbe);
  }

  public async getStatistics(statistics: OrgStatisticsRequireDTO): Promise<OrgStatisticsDTO> {
    return this.httpClient.restPost<{}, OrgStatisticsDTO>('orgs/statistics', statistics);
  }
  public async getOrgBox(orgId: number, includeAllDevices: number = 0): Promise<DeviceDTO> {
    return this.httpClient.restGetOne<{}, DeviceDTO>(`orgs/${orgId}/box`, {
      includeAllDevices,
    });
  }

  public async getOrgDevices(orgId: number): Promise<DeviceItemsDTO> {
    return this.httpClient.restGetOne<{}, DeviceItemsDTO>(`orgs/${orgId}/devices`, {});
  }

  public async getOrgStatistics(): Promise<OrgPointStatisticsDTO> {
    return this.httpClient.restGetOne<{}, OrgPointStatisticsDTO>('orgs/point/statistics', {});
  }

  public async getOrgDicoms(orgId: number): Promise<DicomDTO[]> {
    return this.httpClient.restGet<{}, DicomDTO>(`orgs/${orgId}/dicoms`, {});
  }

  public async applyDicoms(orgId: number, params: ApplyDicomsDTO): Promise<ApplyedDicomsRes> {
    return this.httpClient.restPost<ApplyDicomsDTO, ApplyedDicomsRes>(`orgs/${orgId}/dicoms/apply`, params);
  }

  public async delDicoms(orgId: number, params: DeleteDicomsDTO): Promise<DeleteDicomsRes> {
    return this.httpClient.restPost<DeleteDicomsDTO, DeleteDicomsRes>(`orgs/${orgId}/dicoms/batchdel`, params);
  }

  public async hasPacs(orgId: number): Promise<HasPacsDeviceDTO> {
    return this.httpClient.restGetOne<{}, HasPacsDeviceDTO>(`orgs/${orgId}/haspacsdevice`, {});
  }

  public async getIPLimits(orgId: number): Promise<OrgIPRulesDTO[]> {
    return this.httpClient.restGet<{}, OrgIPRulesDTO>(`orgs/${orgId}/ipRules`, {});
  }

  public async getSpecialIPLimits(): Promise<GetIPRulesResponseDTO> {
    return this.httpClient.restGetOne<{}, GetIPRulesResponseDTO>('tmspoint/iprules', {});
  }

  public async updateIPLimits(orgId: number, params: OrgIPRulesDTO[]): Promise<OrgIPRulesDTO[]> {
    return this.httpClient.restPost<{}, OrgIPRulesDTO[]>(`orgs/${orgId}/ipRules`, params);
  }

  public async createSpecialIPLimits(params: CreateIPRuleDTO): Promise<IPRuleResponseDTO> {
    return this.httpClient.restPost<{}, IPRuleResponseDTO>('tmspoint/iprules', params);
  }

  public async deleteSpecialIPLimits(id: number) {
    return this.httpClient.restDelete<{}>(`tmspoint/iprules/${id}`);
  }

  public async updateSpecialIPLimits(id: number, update: UpdateIPRuleDTO): Promise<IPRuleResponseDTO> {
    return this.httpClient.restPut<UpdateIPRuleDTO, IPRuleResponseDTO>(`tmspoint/iprules/${id}`, update);
  }

  public async getDicomList(status: number[], page: number, pageSize: number, orderBy: string, sort: string): Promise<any> {
    let statusParams = '';
    if(status.length === 0 || status.length === 2){
      statusParams = 'status[]=0&status[]=1';
    }else{
      statusParams = `status[]=${status[0]}`;
    }
    let sortParam = sort.startsWith('asc') ? 0 : 1;

    return this.httpClient.restGet<{}, any>(`endpointhealth/endpoints?${statusParams}&page=${page}&pageSize=${pageSize}&orderBy=${orderBy}&sort=${sortParam}`,{});
  }

  public async getRobotDivice(endpointType: EndpointType): Promise<DeviceItemsDTO> {
    return this.httpClient.restGetOne<{}, DeviceItemsDTO>(`orgs/45/devices?endpointType=${endpointType}`,{});
  }

  public async getDicomLog(status: number[], page: number, pageSize: number, deviceId: number): Promise<any>{
    let statusParams = '';
    if(status.length === 0 || status.length === 2){
      statusParams = 'status[]=0&status[]=1';
    }else{
      statusParams = `status[]=${status[0]}`;
    }

    return this.httpClient.restGet<{}, any>(`endpointhealth/logs?${statusParams}&page=${page}&pageSize=${pageSize}&deviceId=${deviceId}`,{});
  }
}

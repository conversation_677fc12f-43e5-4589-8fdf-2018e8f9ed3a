// 公司官网地址
export const CompanyPageURL = 'https://www.neuralgalaxy.cn/';
// 公司邮箱
export const CompanyEmailURL = 'mailto:<EMAIL>';

/**
 * 传输状态
 */
export enum TransferTaskStatus {
  // 排队中
  Pending = 'Pending',
  // 传输中
  Processing = 'Processing',
  // 传输暂停
  Paused = 'Paused',
  // 传输取消
  Canceld = 'Canceld',
  // 传输成功
  Successful = 'Successful',
  // 传输失败
  Failed = 'Failed',
}


/**
 * @description 下载对象的结构，存储于store
 */
export interface DownloadType {
  // 传输任务ID
  id: string;
  // 下载链接
  url?: string;
  // 文件名
  fileName: string;
  // 文件名
  fileId?: string;
  // 传输状态
  status: TransferTaskStatus;
  // 文件大小
  size: number;
  // 已完成的数据量
  completedSize?: number;
  // 传输速度
  speed?: number;
  // 异常信息
  error?: string;
  // desktop：保存至用户本地的目录
  saveDir?: string;
  createTime: number;
  completeTime?: number;
}

/**
 * @description 更新下载对象的结构，只需id必填
 */
export interface UpdateDownloadType {
  // 传输任务ID
  id: string;
  // 下载链接
  url?: string;
  // 文件名
  fileName?: string;
  // 传输状态
  status?: TransferTaskStatus;
  // 文件大小
  size?: number;
  // 已完成的数据量
  completedSize?: number;
  // 传输速度
  speed?: number;
  // 异常信息
  error?: string;
  // desktop：保存至用户本地的目录
  saveDir?: string;
  createTime?: number;
  completeTime?: number;
}

export interface CompleteDownloadType extends UpdateDownloadType {
  // 下载的数据
  resultArray: any[];
  mime?: string;
}

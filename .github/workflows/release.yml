## This workflow for build
#name: release
#on:
#  push:
#    tags:
#      - "v*"
#
#jobs:
#  build:
#    runs-on: [self-hosted, linux, x64, aws-us]
#    permissions:
#      id-token: write
#      contents: read
#    strategy:
#      fail-fast: false
#      matrix:
#        platform: [win, linux]
#        stack: [app, staging, staging2]
#        profile: [aliyun-prod, default]
#        exclude:
#          - stack: staging2
#            profile: default
#          - stack: staging
#            profile: aliyun-prod
#    steps:
#      - name: Checkout Code
#        uses: actions/checkout@v2
#      - name: Setup Node.js v14
#        uses: actions/setup-node@v2.5.0
#        with:
#          node-version: 14.18.2
#      - name: Setup docker-compose
#        uses: KengoTODA/actions-setup-docker-compose@v1.0.3
#        with:
#          version: "v2.2.1"
#      - name: build package
#        run: |
#          sed -i 's/nexus.common.neuralgalaxy.cn\/repository\/npm-all\/@ngiq/registry.neuralgalaxy.net\/repository\/npm-group\/@ngiq/g' package-lock.json
#          ./scripts/build/build.sh ${{ matrix.stack }} ${{ matrix.profile }} \
#              ${{ matrix.platform == 'linux' }} \
#              ${{ matrix.platform == 'win' }}  \
#      - name: setup profile
#        uses: GlobeFishNG/setup-cloud-cli@v1.0.1
#        with:
#          profile: ${{ matrix.profile }}
#      - name: upload
#        run: |
#          ./scripts/upload/${{ matrix.profile }}.sh ${{ matrix.stack }}
#
#  mac:
#    runs-on: [self-hosted, x64, macos, desktop]
#    strategy:
#      matrix:
#        stack: [app, staging, staging2, pbfs]
#        profile: [aliyun-prod, default]
#        exclude:
#          - stack: staging2
#            profile: default
#          - stack: pbfs
#            profile: default
#          - stack: staging
#            profile: aliyun-prod
#    steps:
#      - name: Checkout Code
#        uses: actions/checkout@v2
#      - name: Setup Node.js v14
#        uses: actions/setup-node@v2.5.0
#        with:
#          node-version: 14.18.2
#      - name: Run build aliyun package
#        run: |
#          sed -i '' 's/nexus.common.neuralgalaxy.cn\/repository\/npm-all\/@ngiq/registry.neuralgalaxy.net\/repository\/npm-group\/@ngiq/g' package-lock.json
#          ./scripts/build/build_mac.sh ${{ matrix.stack }} ${{ matrix.profile }}
#          ./scripts/upload/${{ matrix.profile }}.sh ${{ matrix.stack }}
#
#  release:
#    runs-on: ubuntu-20.04
#    needs:
#      - build
#      - mac
#    steps:
#      - name: Checkout Code
#        uses: actions/checkout@v2
#      - uses: olegtarasov/get-tag@v2.1
#        id: tagName
#        with:
#          tagRegex: "(.*)"
#      - name: Create release branch
#        uses: peterjgrainger/action-create-branch@v2.0.1
#        env:
#          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
#        with:
#          branch: "release-${{steps.tagName.outputs.tag}}"

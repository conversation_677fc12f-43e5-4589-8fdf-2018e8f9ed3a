#name: test
#
#on:
#  pull_request:
#    types:
#      - opened
#      - synchronize
#      - reopened
#
#jobs:
#  test:
#    concurrency:
#      group: ${{ github.ref }}
#      cancel-in-progress: true
#    runs-on: [self-hosted, linux, x64, aws-us]
#    steps:
#      - name: Checkout Code
#        uses: actions/checkout@v2
#      - name: Setup Node.js v14
#        uses: actions/setup-node@v2
#        with:
#          node-version: 14
#      - name: Run CI Script
#        run: |
#          sed -i 's/nexus.common.neuralgalaxy.cn\/repository\/npm-all\/@ngiq/registry.neuralgalaxy.net\/repository\/npm-group\/@ngiq/g' package-lock.json
#          npm run ci
#        timeout-minutes: 25

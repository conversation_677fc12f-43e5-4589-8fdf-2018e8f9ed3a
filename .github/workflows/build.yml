## This workflow for build
#name: build
#
#on:
#  workflow_dispatch:
#    inputs:
#      stack_name:
#        description: "the stack name"
#        required: true
#      profile:
#        description: "profile: default, aws-cn, aliyun"
#        required: true
#
#      build_linux:
#        description: "build linux desktop"
#        type: boolean
#        required: true
#        default: "false"
#      build_mac:
#        description: "build mac desktop"
#        type: boolean
#        required: true
#        default: "false"
#      build_win:
#        description: "build windows desktop"
#        type: boolean
#        required: true
#        default: "false"
#jobs:
#  linux:
#    runs-on: [self-hosted, linux, x64, aws-us]
#    if: github.event.inputs.build_linux == 'true'
#    permissions:
#      id-token: write
#      contents: read
#    steps:
#      - name: check actor
#        run: |
#          echo "User ${{github.actor}} is not allowd"
#          exit 255
#        if: ${{ github.actor != 'ngiq-jenkins' }}
#      - name: Checkout Code
#        uses: actions/checkout@v2
#      - name: Setup Node.js v14
#        uses: actions/setup-node@v2.5.0
#        with:
#          node-version: 14.18.2
#      - name: Setup docker-compose
#        uses: KengoTODA/actions-setup-docker-compose@v1.0.3
#        with:
#          version: "v2.2.1"
#      - name: Run build.sh Script
#        run: |
#          sed -i 's/nexus.common.neuralgalaxy.cn\/repository\/npm-all\/@ngiq/registry.neuralgalaxy.net\/repository\/npm-group\/@ngiq/g' package-lock.json
#          ./scripts/build/build.sh "${{ github.event.inputs.stack_name }}" \
#            "${{ github.event.inputs.profile }}" \
#            "true" \
#            "false" \
#            "false"
#      - name: setup cloud cli (${{ github.event.inputs.profile }})
#        uses: GlobeFishNG/setup-cloud-cli@v1.0.1
#        with:
#          profile: "${{ github.event.inputs.profile }}"
#      - name: Run Upload Script
#        run: |
#          ./scripts/upload/${{ github.event.inputs.profile }}.sh "${{ github.event.inputs.stack_name }}"
#
#  win:
#    runs-on: [self-hosted, linux, x64, aws-us]
#    if: github.event.inputs.build_win == 'true'
#    permissions:
#      id-token: write
#      contents: read
#    steps:
#      - name: Checkout Code
#        uses: actions/checkout@v2
#      - name: Setup Node.js v14
#        uses: actions/setup-node@v2.5.0
#        with:
#          node-version: 14.18.2
#      - name: Setup docker-compose
#        uses: KengoTODA/actions-setup-docker-compose@v1.0.3
#        with:
#          version: "v2.2.1"
#      - name: Run build.sh Script
#        run: |
#          sed -i 's/nexus.common.neuralgalaxy.cn\/repository\/npm-all\/@ngiq/registry.neuralgalaxy.net\/repository\/npm-group\/@ngiq/g' package-lock.json
#          ./scripts/build/build.sh "${{ github.event.inputs.stack_name }}" \
#            "${{ github.event.inputs.profile }}" \
#            "false" \
#            "true" \
#            "false"
#      - name: setup cloud cli (${{ github.event.inputs.profile }})
#        uses: GlobeFishNG/setup-cloud-cli@v1.0.1
#        with:
#          profile: "${{ github.event.inputs.profile }}"
#      - name: Run Upload Script
#        run: |
#          ./scripts/upload/${{ github.event.inputs.profile }}.sh "${{ github.event.inputs.stack_name }}"
#
#  mac:
#    runs-on: [self-hosted, x64, macos, desktop]
#    if: github.event.inputs.build_mac == 'true'
#    steps:
#      - name: Checkout Code
#        uses: actions/checkout@v2
#      - name: Setup Node.js v14
#        uses: actions/setup-node@v2.5.0
#        with:
#          node-version: 14.18.2
#      - name: Run build.sh Script
#        run: |
#          sed -i '' 's/nexus.common.neuralgalaxy.cn\/repository\/npm-all\/@ngiq/registry.neuralgalaxy.net\/repository\/npm-group\/@ngiq/g' package-lock.json
#          ./scripts/build/build_mac.sh "${{ github.event.inputs.stack_name }}" "${{ github.event.inputs.profile }}"
#      - name: Run Upload Script
#        run: |
#          ./scripts/upload/${{ github.event.inputs.profile }}.sh "${{ github.event.inputs.stack_name }}"

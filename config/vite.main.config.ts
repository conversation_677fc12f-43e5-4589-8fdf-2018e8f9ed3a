import { externalizeDepsPlugin } from 'electron-vite';
import path from 'path';
import copy from 'rollup-plugin-copy';
import { ConfigEnv, UserConfig } from 'vite';

export default function getMainConfig(configEnv: ConfigEnv): UserConfig {
  const isDevelopment = configEnv.mode === 'development';

  return {
    publicDir: 'public',

    resolve: {
      alias: {
        '@common': path.resolve('src/common'),
        '@main': path.resolve('src/main'),
        '@preload': path.resolve('src/preload'),
      },
      extensions: ['.js', '.ts', '.jsx', '.tsx'],
    },
    // https://cn.vitejs.dev/guide/env-and-mode.html#env-files
    // https://cn.vitejs.dev/config/#using-environment-variables-in-config
    // 指定 .env 文件目录
    envDir: path.resolve('env'),
    // 注意：以 envPrefix 开头的环境变量会通过 import.meta.env 暴露在你的客户端源码中。所以敏感信息不要以 envPrefix 为前缀。
    //【在 vite 中】：默认值 => VITE_
    //【在 electron-vite preload 中】：默认值 => MAIN_VITE_
    envPrefix: 'PUBLIC_',

    build: {
      minify: 'esbuild',
      rollupOptions: {
        input: {
          index: path.resolve(__dirname, '../src/main/index.ts'),
        },
        output: {
          // 指定输出路径（相对于 项目根目录)
          dir: 'dist/main',
          manualChunks: (id, meta) => {
            // console.log('id,meta: ', id, meta);
            if (id.includes('node_modules')) {
              return 'vendor';
            }
          },
        },
      },
    },
    plugins: [
      externalizeDepsPlugin({
        // 如果 npm 依赖安装在 dependencies 里，就不需要在这里设置
        // include:['request','axios','node-machine-id','@electron/remote','ali-oss'],
        // include: ['electron-devtools-assembler'],
      }),
      copy({
        targets: [
          isDevelopment ? { src: 'src/main/plugins/updater/dev-app-update.yml', dest: 'dist' } : undefined,
          // 目前主进程拷贝文件的输出路径不能指定为 dist/main，设置无效。
          // 猜测是插件先拷贝文件输出到 dist/main 目录下，然后打包 main 进程 chunk 文件时，会先删除 dist/main 目录，然后再输出到 dist/main 目录下
          // 因为 主进程 先于 preload 打包，所以把下面的文件放到 preload 中处理
          // {
          //   src: 'src/main/old/i18n/locales',
          //   dest: 'dist',
          // },
        ].filter(Boolean),
      }),
    ],
  };
}

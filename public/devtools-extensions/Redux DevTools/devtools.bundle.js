(()=>{"use strict";var e={};function t(e){chrome.devtools.panels.create("Redux","img/logo/scalable.png",e,(function(){}))}e.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),(()=>{var t;e.g.importScripts&&(t=e.g.location+"");var r=e.g.document;if(!t&&r&&(r.currentScript&&(t=r.currentScript.src),!t)){var n=r.getElementsByTagName("script");n.length&&(t=n[n.length-1].src)}if(!t)throw new Error("Automatic publicPath is not supported in this browser");t=t.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),e.p=t})(),e.p,chrome.runtime.getBackgroundPage?chrome.runtime.getBackgroundPage((e=>{t(e?"window.html":"devpanel.html")})):t("devpanel.html")})();
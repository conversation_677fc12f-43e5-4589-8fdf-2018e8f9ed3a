# https://cn.vitejs.dev/guide/env-and-mode.html#env-files
# https://cn-evite.netlify.app/guide/env-and-mode.html#%E5%85%A8%E5%B1%80%E7%8E%AF%E5%A2%83%E5%8F%98%E9%87%8F


# 注意：
# 1、默认情况下，以 PUBLIC_ 为前缀的变量暴露给主进程，PRELOAD_VITE_ 用于预加载脚本，PUBLIC_ 则用于渲染器。
# 2、以 PUBLIC_ 开头的环境变量会通过 import.meta.env 暴露在你的客户端源码中。所以敏感信息不要以 PUBLIC_ 为前缀。
# 3、此文件存放生产环境需要用到的环境变量
# 4、如果 .env、.env.production、.env.production.local 中都设置了同名的环境变量，优先使用优先级高的值，优先级从低到高为【.env < .env.production < .env.production.local】

# 公共的环境变量



# 渲染进程用到的环境变量



# 主进程用到的环境变量

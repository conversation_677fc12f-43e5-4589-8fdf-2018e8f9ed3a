# https://cn.vitejs.dev/guide/env-and-mode.html#env-files
# https://cn-evite.netlify.app/guide/env-and-mode.html#%E5%85%A8%E5%B1%80%E7%8E%AF%E5%A2%83%E5%8F%98%E9%87%8F

# 注意：
# 1、默认情况下，以 PUBLIC_ 为前缀的变量暴露给主进程，PRELOAD_VITE_ 用于预加载脚本，PUBLIC_ 则用于渲染器。
# 2、以 PUBLIC_ 开头的环境变量会通过 import.meta.env 暴露在你的客户端源码中。所以敏感信息不要以 PUBLIC_ 为前缀。
# 3、此文件存放开发环境需要用到的环境变量，如果是包含敏感信息、用户自定义配置的变量，建议存放到 .env.development.local 中
# 4、如果 .env、.env.development、.env.development.local 中都设置了同名的环境变量，优先使用优先级高的值，优先级从低到高为【.env < .env.development < .env.development.local】

# 公共的环境变量
PUBLIC_APP_REQUEST_HEADERS_ORIGIN = 'http://127.0.0.1:5000'

# PUBLIC_APP_MOCK_API_BASEURL: http://127.0.0.1:4523/mock/2740365


# 渲染进程用到的环境变量
# 渲染进程用到的环境变量
# 渲染进程用到的环境变量
# 1：开 0：关 - 在本地调试时，控制国际化文案热更新
PUBLIC_APP_I18N_HOT_FIX = '1'

# 项目中接入 react-dev-inspector 插件，支持点击页面元素直接跳转 IDE 查看对应组件
# 当前用户使用的编辑器类型（ vscode | webstorm ）
PUBLIC_REACT_DEV_INSPECTOR_LAUNCH_EDITOR='vscode'


# 治疗方案页——路由拦截开关 1：开 0：关
# PUBLIC_TREATMENT_PLAN_ROUTE_INTERCEPT_SWITCH  = '0'


# 主进程用到的环境变量
# 主进程用到的环境变量
# 主进程用到的环境变量
# 是否允许本地开发环境测试 APP 更新检测、更新相关功能
PUBLIC_APP_UPDATER_FORCE_DEV_UPDATE_CONFIG = false

# 是否开启主进程 axios 请求代理转发
PUBLIC_APP_MAIN_PROCESS_AXIOS_PROXY_SWITCH = false
PUBLIC_APP_MAIN_PROCESS_AXIOS_PROXY_PROTOCOL = 'http'
PUBLIC_APP_MAIN_PROCESS_AXIOS_PROXY_HOST = '127.0.0.1'
PUBLIC_APP_MAIN_PROCESS_AXIOS_PROXY_PORT = 8899
